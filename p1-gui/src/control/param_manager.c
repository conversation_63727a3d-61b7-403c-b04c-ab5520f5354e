/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdio.h>
#include <string.h>

//============================> Libraries Headers <============================
#include "dx.h"
#include "param_manager.h"
#include "proto.h"
#include "ui.h"

#include "../ui_conf_internal.h"

//============================> Project Headers <============================

/******************************************************************************

                                Defines

 ******************************************************************************/

#define MC_STATUS_BUFF_SIZE             UI_ARR_LEN(param_manager.gui_ia->dev_status.dev_sta)

/******************************************************************************

                                Structs

 ******************************************************************************/

typedef struct s_menu_config
{
    uint32_t menu_config_index  :   8;  // 菜单索引值: 0x00 - 0xFF
    uint32_t menu_config_flag   :   24; // 固定值: 0x55356d
    uint16_t menu_item_count;           // 菜单项总数
    uint16_t reserved;
} menu_config_t;

typedef struct s_work_mode_menu_config
{
    menu_config_t base;
    union
    {
        uint32_t raw;
        struct
        {
            uint32_t digital_conv : 1;     // 0: 可见, 1: 不可见
            uint32_t digital_trunking : 1; // 0: 可见, 1: 不可见
            uint32_t analog_trunking : 1;  // 0: 可见, 1: 不可见
            uint32_t analog_conv : 1;      // 0: 可见, 1: 不可见
            uint32_t adhoc : 1;            // 虽然写频软件可勾选"应急网"模式, 但是写频后菜单中仍然"模式切换"菜单中仍然没有此菜单项
            uint32_t q : 1;                // 0: 可见, 1: 不可见
            uint32_t n : 1;                // 0: 可见, 1: 不可见
            uint32_t v_plus : 1;           // 0: 可见, 1: 不可见
            uint32_t v : 1;                // 0: 可见, 1: 不可见
            uint32_t poc : 1;              // 0: 可见, 1: 不可见
        } bit;
    };
} work_mode_menu_config_t;

typedef struct s_misc_config
{
    menu_config_t base;

    union
    {
        uint32_t raw;
        struct
        {
            uint32_t left_signal_hidden : 1;   // B0, 左信号图标 0：可见，1：不可见
            uint32_t satellite_hidden : 1;     // B1, 卫星图标 0：可见，1：不可见
            uint32_t bluetooth_hidden : 1;     // B2, 蓝牙图标 0：可见，1：不可见
            uint32_t time_hidden : 1;          // B3, 时间图标 0：可见，1：不可见
            uint32_t msg_hidden : 1;           // B4, 消息图标 0：可见，1：不可见
            uint32_t battery_hidden : 1;       // B5, 电池图标 0：可见，1：不可见
            uint32_t right_signal_hidden : 1;  // B6, 右信号图标 0：可见，1：不可见
            uint32_t volume_hidden : 1;        // B7, 音量图标 0：可见，1：不可见
            uint32_t pos_hidden : 1;           // B8, 态势图标 0：可见，1：不可见
            uint32_t rf_power_hidden : 1;      // B9, 功率图标 0：可见，1：不可见
            uint32_t chan_num_hidden : 1;      // B10, 空闲信道图标 0：可见，1：不可见
            uint32_t work_mode_hidden : 1;     // B11, 工作模式图标 0：可见，1：不可见
            uint32_t menu_hidden : 1;          // B12, 主菜单图标 0：可见，1：不可见
            uint32_t contact_hidden : 1;       // B13, 联系人图标 0：可见，1：不可见
            uint32_t accessory_hidden : 1;     // B14, 配件图标 0：可见，1：不可见
            uint32_t encryption_hidden : 1;    // B15, 加密图标 0：可见，1：不可见
            uint32_t compass_en : 1;           // B16, 指南针 0：禁用, 1：启用
            uint32_t man_down_en : 1;          // B17, 倒放检测 0：禁用, 1：启用
            uint32_t key_prog : 1;             // B18, 按键编程功能使能：
                                               //      1 - 启用则类似815，支持按键自定义，待机页面时点击确认键即可直接进入主菜单；
                                               //      0 - 禁用按键编程，按键功能固定，需输入指令才能进入主菜单
            uint32_t subgroup_sel_en_xv : 1;   // B19, 应急网模式下是否支持组群选择 0：不支持，1：支持
            uint32_t ptt_alarm_on_busy_en : 1; // B20, 当信道忙时按下PTT会发出告警：
                                               //      0 - 功能关闭，1 - 功能开启

        } bit;
    };
} misc_config_t;

typedef union
{
    uint8_t raw;
    struct
    {
        uint8_t : 4;
        uint8_t pdt_left_num : 1;
        uint8_t pdt_right_bar : 1;
        uint8_t adhoc_left_bar : 1;
        uint8_t adhoc_right_num : 1;
    } bit;
} signal_icon_disp_mode_t;

/******************************************************************************

                                Global Variables

 ******************************************************************************/

param_manager_t param_manager =
{
    .dev_state_curr = NULL,
    .dev_state_prev = NULL
};

/******************************************************************************

                                Helper Functions

 ******************************************************************************/

static bool is_valid_work_mode(uint8_t work_mode)
{
    if (work_mode != PARAM_WORK_MODE_DIGITAL_CONVENTIONAL && work_mode != PARAM_WORK_MODE_DIGITAL_TRUNKING &&
        work_mode != PARAM_WORK_MODE_ANALOG_CONVENTIONAL && work_mode != PARAM_WORK_MODE_ANALOG_TRUNKING &&
        work_mode != PARAM_WORK_MODE_ADHOC_N && work_mode != PARAM_WORK_MODE_ADHOC_Q &&
        work_mode != PARAM_WORK_MODE_ADHOC_V && work_mode != PARAM_WORK_MODE_ADHOC_V25 &&
        work_mode != PARAM_WORK_MODE_POC)
    {
        return false;
    }
    else
    {
        return true;
    }
}

/**
 * @brief 从参数结构体中, 获取特定菜单的配置信息
 *
 * @param menu_index                    菜单索引值
 *                                      - 0x07: 模式切换
 *                                      - 0x08: 图标栏配置, 指南针, 倒放检测等
 *
 * @retval menu_config_t *           菜单配置结构体的指针
 * @retval NULL                         获取失败
 */
static menu_config_t *param_manager_find_menu_config(uint8_t menu_index)
{
    size_t menu_config_size = sizeof(work_mode_menu_config_t);                    // 菜单配置结构体的大小
    size_t menu_config_total_size = sizeof(param_manager.static_param->menu_config); // 菜单配置结构体的总大小
    size_t menu_config_count = menu_config_total_size / menu_config_size;            // 菜单配置结构体的总数量

    for (int i = 0; i < menu_config_count; ++i)
    {
        menu_config_t *menu_config = (menu_config_t *)(param_manager.static_param->menu_config + i * menu_config_size);

        if (menu_config->menu_config_flag == 0x55356d && menu_config->menu_config_index == menu_index)
        {
            return menu_config;
        }
    }

    return NULL;
}

/**
 * @brief 获取待处理的主控状态帧
 * 
 * @note 若缓冲区中存在多个待处理的主控状态帧, 则将读取最早被推入缓冲区队列的主控状态帧。
 * 若缓冲区队列中没有待处理的主控状态帧, 则返回最新的已处理的主控状态帧。
 */
static const STATUS_TO_BT_TYPEDEF *param_get_pending_mc_status_frame(void)
{
    uint8_t write_index = param_manager.gui_ia->dev_status.dev_sta_wr;
    uint8_t read_index = param_manager.gui_ia->dev_status.dev_sta_rd;

    if (write_index == read_index)
    {
        return param_manager.gui_ia->dev_status.dev_sta + read_index;
    }
    else
    {
        read_index = (read_index + 1) % MC_STATUS_BUFF_SIZE;
        return param_manager.gui_ia->dev_status.dev_sta + read_index;
    }
}

/******************************************************************************

                                Public Functions

 ******************************************************************************/


void *param_get_runtime_params(void)
{
    return param_manager.runtime_param;
}

void *param_get_static_params(void)
{
    return param_manager.static_param;
}

void *param_get_factory_params(void)
{
    return param_manager.factory_param;
}

char *param_get_chan_num_input_buff(void)
{
    return param_manager.chan_num_input_buff;
}

void param_clear_chan_num_input_buff(void)
{
    param_manager.chan_num_input_buff[0] = '\0';
}

char *param_get_dial_num_input_buff(void)
{
    return param_manager.dial_num_input_buff;
}

void param_clear_dial_num_input_buff(void)
{
    param_manager.dial_num_input_buff[0] = '\0';
}

char *param_get_poc_username_input_buff(void)
{
    return param_manager.poc_username_input_buff;
}

char *param_get_poc_password_input_buff(void)
{
    return param_manager.poc_password_input_buff;
}

char *param_get_poc_server_addr_input_buff(void)
{
    return param_manager.poc_server_addr_input_buff;
}

char *param_get_poc_server_port_input_buff(void)
{
    return param_manager.poc_server_port_input_buff;
}

char *param_get_poc_apn_input_buff(void)
{
    return param_manager.poc_apn_input_buff;
}

void param_set_poc_apn_input_buff(const char *apn)
{
    size_t apn_len = strlen(apn);

    if (apn_len == 0)
    {
        memset(param_manager.poc_apn_input_buff, 0, sizeof(param_manager.poc_apn_input_buff));
    }
    else
    {
        if (apn_len > sizeof(param_manager.poc_apn_input_buff) - 1)
        {
            apn_len = sizeof(param_manager.poc_apn_input_buff) - 1;
        }

        memmove(param_manager.poc_apn_input_buff, apn, apn_len);
    }
}

void param_set_poc_username_input_buff(const char *username)
{
    size_t username_len = strlen(username);

    if (username_len == 0)
    {
        memset(param_manager.poc_username_input_buff, 0, sizeof(param_manager.poc_username_input_buff));
    }
    else
    {
        if (username_len > sizeof(param_manager.poc_username_input_buff) - 1)
        {
            username_len = sizeof(param_manager.poc_username_input_buff) - 1;
        }

        memmove(param_manager.poc_username_input_buff, username, username_len);
    }
}

void param_set_poc_password_input_buff(const char *password)
{
    size_t password_len = strlen(password);

    if (password_len == 0)
    {
        memset(param_manager.poc_password_input_buff, 0, sizeof(param_manager.poc_password_input_buff));
    }
    else
    {
        if (password_len > sizeof(param_manager.poc_password_input_buff) - 1)
        {
            password_len = sizeof(param_manager.poc_password_input_buff) - 1;
        }

        memmove(param_manager.poc_password_input_buff, password, password_len);
    }
}

void param_set_poc_server_ipv4(uint32_t ipv4)
{
    param_manager.poc_server_ipv4 = ipv4;
}

int param_poc_server_ipv4_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    int ret = lv_snprintf(string_buff, *string_buff_size, "%d.%d.%d.%d", param_manager.poc_server_ipv4 & 0xFF, (param_manager.poc_server_ipv4 >> 8) & 0xFF,
                          (param_manager.poc_server_ipv4 >> 16) & 0xFF, (param_manager.poc_server_ipv4 >> 24) & 0xFF);

    if (ret < 0)
    {
        return -1;
    }
    else
    {
        *string_buff_size = ret;
        return ret;
    }
}

uint8_t param_get_poc_subgroup_index(void)
{
    return param_manager.poc_param->param.poc_subgroup_index;
}

void param_set_poc_subgroup_index(uint8_t index)
{
    param_manager.poc_param->param.poc_subgroup_index = index;
}

uint8_t param_get_poc_subgroup_index_temp(void)
{
    return param_manager.poc_standby_idx_temp;
}

uint8_t param_set_poc_standby_index_temp(bool inc)
{
    if (inc)
    {
        param_manager.poc_standby_idx_temp = (param_manager.poc_standby_idx_temp + 1) % param_manager.contact_list_grp.size;
    }
    else
    {
        param_manager.poc_standby_idx_temp = (param_manager.poc_standby_idx_temp == 0) ? (param_manager.contact_list_grp.size - 1) : (param_manager.poc_standby_idx_temp - 1);
    }

    return param_manager.poc_standby_idx_temp;
}

void param_switch_poc_group(uint8_t confirm_switch)
{
    if (confirm_switch == 1)
    {
        param_manager.poc_param->param.poc_standby_group_index = param_manager.poc_standby_idx_temp;
    }
    else if (confirm_switch == 0)
    {
        param_manager.poc_standby_idx_temp = param_manager.poc_param->param.poc_standby_group_index;
    }
}

int param_contact_list_add_index(param_contact_list_t *contact_list, uint16_t index)
{
    if (contact_list == NULL || contact_list->index_list == NULL)
    {
        return -1;
    }

    if (contact_list->size >= contact_list->capacity)
    {
        contact_list->capacity += 50;
        contact_list->index_list = lv_mem_realloc(contact_list->index_list, contact_list->capacity * sizeof(uint16_t));
    }

    contact_list->index_list[contact_list->size++] = index;
    
    return 0;
}

//============================> Control Functions <============================

int param_read_current_contact_into(void *buff)
{
    if (param_get_work_mode() == PARAM_WORK_MODE_POC)
    {
        uint8_t standby_index = param_manager.poc_standby_idx_temp;
        standby_index = (standby_index >= param_manager.contact_list_grp.size) ? 0 : standby_index;
        uint16_t userbook_index = param_manager.contact_list_grp.index_list[standby_index];
        USER_BOOK *user_book = param_manager.group_books->contacts.books + userbook_index;
        memmove(buff, user_book, sizeof(USER_BOOK));
    }
    else
    {
        memmove(buff, &param_manager.gui_ia->watch_grp, sizeof(USER_BOOK));
    }

    return 0;
}

uint32_t param_get_current_standby_group_id(void)
{
    if (param_get_work_mode() == PARAM_WORK_MODE_POC)
    {
        uint8_t standby_index = param_manager.poc_standby_idx_temp;
        standby_index = (standby_index >= param_manager.contact_list_grp.size) ? 0 : standby_index;
        uint16_t userbook_index = param_manager.contact_list_grp.index_list[standby_index];
        USER_BOOK *user_book = param_manager.group_books->contacts.books + userbook_index;
        return user_book->id;
    }
    else
    {
        return param_manager.gui_ia->watch_grp.id;
    }
}

void param_set_current_standby_group(const void *buff)
{
    memmove(&param_manager.gui_ia->watch_grp, buff, sizeof(USER_BOOK));
}

const void *param_get_individual_contact(uint16_t index)
{
    if (index < param_manager.contact_list_ind.size)
    {
        uint16_t user_book_index = param_manager.contact_list_ind.index_list[index];
        
        if (user_book_index < MAX_USER_BOOK_COUNT)
        {
            return &param_manager.group_books->contacts.books[user_book_index];
        }
    }

    return NULL;
}

const void *param_get_group_contact(uint16_t index)
{
    if (index < param_manager.contact_list_grp.size)
    {
        uint16_t user_book_index = param_manager.contact_list_grp.index_list[index];

        if (user_book_index < MAX_USER_BOOK_COUNT)
        {
            return &param_manager.group_books->contacts.books[user_book_index];
        }
    }

    return NULL;
}

const void *param_get_hidden_contact(uint16_t index)
{
    if (index < param_manager.contact_list_hidden.size)
    {
        uint16_t user_book_index = param_manager.contact_list_hidden.index_list[index];

        if (user_book_index < MAX_USER_BOOK_COUNT)
        {
            return &param_manager.group_books->contacts.books[user_book_index];
        }
    }

    return NULL;
}

uint16_t param_get_individual_contact_count(void)
{
    return param_manager.contact_list_ind.size;
}

uint16_t param_get_group_contact_count(void)
{
    return param_manager.contact_list_grp.size;
}

uint16_t param_get_hidden_contact_count(void)
{
    return param_manager.contact_list_hidden.size;
}

int param_user_book_name_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    const USER_BOOK *user_book = (const USER_BOOK *)field_value_buff;

    if (user_book == NULL || ui_utils_strnlen((const char *)user_book->name, sizeof(user_book->name)) == 0)
    {
        return -1;
    }

    return proto_gb2312_array_to_utf8_string(user_book->name, strlen((const char *)user_book->name), string_buff, string_buff_size);
}

int param_user_book_id_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    const USER_BOOK *user_book = (const USER_BOOK *)field_value_buff;

    if (user_book == NULL || ui_utils_strnlen((const char *)user_book->name, sizeof(user_book->name)) == 0)
    {
        return -1;
    }

    uint32_t user_book_type_bits = (user_book->id >> 24) & 0xFF;
    bool ig = UI_HAS_BIT(user_book_type_bits, 4) ? UI_INDIVIDUAL : UI_GROUP;

    return ui_utils_id_to_string(user_book->id & 0xFFFFFF, ig, string_buff, string_buff_size, '\0');
}

int param_read_rf_power_into(void *field_value_buff, size_t *buff_size)
{
    *(uint8_t *)field_value_buff = param_get_rf_power();
    return 0;
}

uint8_t param_get_rf_power(void)
{
    param_device_type_t device_type = param_get_device_type();

    if (device_type == PARAM_DEV_TYPE_K1)
    {
        if (param_get_switch_state() == true)
        {
            return param_manager.temp_rf_power;
        }

        switch (param_manager.runtime_param->runtime_paras.rf_power)
        {
        case 0:
            return PARAM_RF_POWER_2W;
        case 1:
            return PARAM_RF_POWER_4W;
        case 2:
            return PARAM_RF_POWER_8W;
        case 3:
            return PARAM_RF_POWER_15W;
        case 4:
            return PARAM_RF_POWER_20W;
        case 5:
            return PARAM_RF_POWER_25W;
        default:
            return PARAM_RF_POWER_UNDEFINED;
        }
    }
    else
    {
        switch (param_manager.runtime_param->runtime_paras.rf_power)
        {
        case 0:
            return PARAM_RF_POWER_1W;
        case 1:
            return PARAM_RF_POWER_4W;
        case 2:
            return PARAM_RF_POWER_AUTO;
        default:
            return PARAM_RF_POWER_UNDEFINED;
        }
    }
}

int param_set_rf_power(uint8_t rf_power)
{
    param_device_type_t device_type = param_get_device_type();
    // 【车机：0-2W; 1-5W; 2-10W; 3-15W; 4-20W; 5-25W，不允许写入其他值】【手台：0-低功率；1-高功率；2-功率自动控制】*/

    uint8_t current_rf_power = param_get_rf_power();

    if (current_rf_power == rf_power)
    {
        return 0;
    }

    if (device_type == PARAM_DEV_TYPE_K1)
    {
        uint8_t new_rf_power;

        switch (rf_power)
        {
        case PARAM_RF_POWER_2W:
            new_rf_power = 0;
            break;
        case PARAM_RF_POWER_4W:
            new_rf_power = 1;
            break;
        case PARAM_RF_POWER_8W:
            new_rf_power = 2;
            break;
        case PARAM_RF_POWER_15W:
            new_rf_power = 3;
            break;
        case PARAM_RF_POWER_20W:
            new_rf_power = 4;
            break;
        case PARAM_RF_POWER_25W:
            new_rf_power = 5;
            break;
        default:
            return -1;
        }

        /**
         * @brief 当 K1 的波段开关处于开启状态时，为了实现“波段开关开启时，调节功率不掉电保存”的功能，
         * 我们在调节功率时，首先将当前的 `功率参数` 保存到临时变量中，然后设置新的 `功率参数`，
         * 并通过调用主控接口使新的功率生效。随后，将临时变量中的功率参数恢复到 `功率参数` 中。
         * 
         * 通过这种方式，在调节功率时可以确保实际功率调整生效，但 `功率参数` 不会被改变。
         */
        if (param_get_switch_state() == true)
        {
            // 将当前的`功率参数`保存到临时变量中
            uint8_t temp_rf_power = param_manager.runtime_param->runtime_paras.rf_power;

            // 随后设置新的`功率参数`, 并调用主控接口以使新的功率生效,
            param_manager.runtime_param->runtime_paras.rf_power = new_rf_power;
            ui_controller_set_rf_power();

            // 最后将临时变量中的功率参数恢复到`功率参数`中, 然后将新的功率保存到临时变量中。
            param_manager.runtime_param->runtime_paras.rf_power = temp_rf_power;
            param_manager.temp_rf_power = rf_power;
        }
        else
        {
            // 修改功率参数, 并调用主控接口以使新的功率生效
            param_manager.runtime_param->runtime_paras.rf_power = new_rf_power;
            ui_controller_set_rf_power();
        }
    }
    else if (device_type == PARAM_DEV_TYPE_P2)
    {
        switch (rf_power)
        {
        case PARAM_RF_POWER_1W:
            rf_power = 0;
            break;
        case PARAM_RF_POWER_4W:
            rf_power = 1;
            break;
        case PARAM_RF_POWER_AUTO:
            rf_power = 2;
            break;
        default:
            return -1;
        }

        param_manager.runtime_param->runtime_paras.rf_power = rf_power;
        ui_controller_set_rf_power();
    }
    else
    {
        switch (rf_power)
        {
        case PARAM_RF_POWER_1W:
            rf_power = 0;
            break;
        case PARAM_RF_POWER_4W:
            rf_power = 1;
            break;
        case PARAM_RF_POWER_AUTO:
            rf_power = 2;
            break;
        default:
            return -1;
        }

        param_manager.runtime_param->runtime_paras.rf_power = rf_power;
        ui_controller_set_rf_power();
    }

    return 0;
}

int param_write_rf_power_from(const void *field_value_buff, size_t buff_size)
{
    uint8_t rf_power = *(uint8_t *)field_value_buff;
    return param_set_rf_power(rf_power);
}

int param_read_stun_state_into(void *field_value_buff, size_t *buff_size)
{
    *(uint8_t *)field_value_buff = param_get_stun_state();
    return 0;
}

uint8_t param_get_stun_state(void)
{
    uint8_t work_mode = param_get_work_mode();

    // 若设备处于自组网模式
    if (ui_utils_is_work_mode_adhoc(work_mode))
    {
        uint8_t remote_kill = param_manager.runtime_param->runtime_paras.misc_runtime_config.remote_kill;
        uint8_t remote_stun = param_manager.runtime_param->runtime_paras.misc_runtime_config.remote_stun;

        if (remote_stun)
        {
            return PARAM_STUN_STATE_STUN;
        }
        else if (remote_kill)
        {
            return PARAM_STUN_STATE_KILL;
        }
        else
        {
            return PARAM_STUN_STATE_REVIVE;
        }
    }
    // 若设备处于数字常规/集群模式
    else if (ui_utils_is_work_mode_digital(work_mode))
    {
        uint32_t stack_mode = param_manager.runtime_param->runtime_paras.stack_mode[1];

        if (ui_utils_stack_mode_is_stunned(stack_mode))
        {
            return PARAM_STUN_STATE_STUN;
        }
        else if (ui_utils_stack_mode_is_killed(stack_mode))
        {
            return PARAM_STUN_STATE_KILL;
        }
        else
        {
            return PARAM_STUN_STATE_REVIVE;
        }
    }
    // 若设备处于模拟集群模式
    else if (work_mode == PARAM_WORK_MODE_ANALOG_TRUNKING)
    {
        uint32_t stack_mode = param_manager.runtime_param->runtime_paras.stack_mode[2];

        if (ui_utils_stack_mode_is_stunned(stack_mode))
        {
            return PARAM_STUN_STATE_STUN;
        }
        else if (ui_utils_stack_mode_is_killed(stack_mode))
        {
            return PARAM_STUN_STATE_KILL;
        }
        else
        {
            return PARAM_STUN_STATE_REVIVE;
        }
    }
    else
    {
        return PARAM_STUN_STATE_REVIVE;
    }
}

int param_read_work_mode_into(void *field_value_buff, size_t *buff_size)
{
    *(uint8_t *)field_value_buff = param_get_work_mode();
    return 0;
}

int param_write_work_mode_from(const void *field_value_buff, size_t buff_size)
{
    uint8_t work_mode = *(uint8_t *)field_value_buff;
    return param_set_work_mode(work_mode);
}

int param_set_work_mode(uint8_t work_mode)
{
    if (ui_controller_is_gui_exec_invoked() == false)
    {
        LOG_ERROR("'gui_exec' has not invoked yet\n");
        return -1;
    }

    uint8_t prev_work_mode = param_get_work_mode();

    if (is_valid_work_mode(work_mode) == false)
    {
        LOG_ERROR("Invalid work mode: %d\n", work_mode);
        return -1;
    }

    if (prev_work_mode == work_mode)
    {
        return 0;
    }

    // 若设置的工作模式为POC模式
    if (work_mode == PARAM_WORK_MODE_POC)
    {
        /**
         * @brief 若设置的工作模式为POC模式, 则需要: 
         *  1. 将'POC启用'参数置1
         *  2. 调用主控接口以通知主控切换到POC模式
         *  3. 将`运行参数`中的`工作模式`参数修改为`数字集群`, 因为POC模式需要使用集群的守候组
         *  4. 重启设备使参数生效
         */
        param_manager.poc_param->param.poc_enabled = true;
        ui_controller_toggle_poc_mode(true);
        param_manager.runtime_param->runtime_paras.work_mode = PARAM_WORK_MODE_DIGITAL_TRUNKING;
        ui_controller_reboot_device();
    }
    else
    {
        if (prev_work_mode == PARAM_WORK_MODE_POC)
        {
            param_manager.poc_param->param.poc_enabled = false;
            ui_controller_toggle_poc_mode(false);
        }

        // 当我们需要修改"运行参数"中的"工作模式"时, 需要重启设备
        param_manager.runtime_param->runtime_paras.work_mode = work_mode;
        ui_controller_reboot_device();
    }

    return 0;
}

int param_init_work_mode(uint8_t work_mode)
{
    // 若设置的工作模式为POC模式
    if (work_mode == PARAM_WORK_MODE_POC)
    {
        /**
         * @brief 若设置的工作模式为POC模式, 则需要: 
         *  1. 将'POC启用'参数置1
         *  2. 将`运行参数`中的`工作模式`参数修改为`数字集群`, 因为POC模式需要使用集群的守候组
         */
        param_manager.poc_param->param.poc_enabled = true;
        ui_controller.gui_ia->dev_misc_notify.poc_mode = true;
        ui_controller.gui_ia->dev_misc_notify.limit_dport_data = true;
        param_manager.runtime_param->runtime_paras.work_mode = PARAM_WORK_MODE_DIGITAL_TRUNKING;
    }
    else
    {
        param_manager.poc_param->param.poc_enabled = false;
        ui_controller.gui_ia->dev_misc_notify.poc_mode = false;
        ui_controller.gui_ia->dev_misc_notify.limit_dport_data = false;
        param_manager.runtime_param->runtime_paras.work_mode = work_mode;
    }

    return 0;
}

uint8_t param_get_work_mode(void)
{
    if (param_manager.poc_param->param.poc_enabled)
    {
        /**
         * @note 若'PoC启用'标志位被置1, 但是'工作模式'参数不为数字集群, 则将'PoC启用'标志位清零,
         * 并返回'工作模式'参数。
         */
        if (param_manager.runtime_param->runtime_paras.work_mode != PARAM_WORK_MODE_DIGITAL_TRUNKING)
        {
            param_manager.poc_param->param.poc_enabled = false;
            return (uint8_t)param_manager.runtime_param->runtime_paras.work_mode;
        }
        else
        {
            return PARAM_WORK_MODE_POC;
        }
    }
    else
    {
        return (uint8_t)param_manager.runtime_param->runtime_paras.work_mode;
    }
}

int param_read_telecom_standard_into(void *field_value_buff, size_t *buff_size)
{
    uint8_t work_mode = param_get_work_mode();
    ui_stack_mode_t stack_mode;

    if (ui_utils_is_work_mode_digital(work_mode))
    {
        stack_mode.u32 = param_manager.runtime_param->runtime_paras.stack_mode[1];
    }
    else
    {
        return -1;
    }

    *(uint8_t *)field_value_buff = stack_mode.bit.pdt;

    return 0;
}

int param_write_telecom_standard_from(const void *field_value_buff, size_t buff_size)
{
    uint8_t work_mode = param_get_work_mode();
    uint8_t telecom_standard = *((uint8_t *)field_value_buff);

    // 获取当前的制式
    uint8_t current_telecom_standard = PARAM_TELECOM_STANDARD_UNDEFINED;
    param_read_telecom_standard_into(&current_telecom_standard, NULL);

    // 若新的制式不是PDT或DMR, 则返回
    if (telecom_standard != PARAM_TELECOM_STANDARD_PDT && telecom_standard != PARAM_TELECOM_STANDARD_DMR)
    {
        return -1;
    }

    // 若当前的制式与新的制式相同, 则返回
    if (current_telecom_standard == telecom_standard)
    {
        return 0;
    }

    // 若设备不是处于数字常规/集群模式, 则返回
    if (!ui_utils_is_work_mode_digital(work_mode))
    {
        return -1;
    }

    // 修改配置字后, 重置协议栈
    ui_stack_mode_t *stack_mode = (ui_stack_mode_t *)(param_manager.runtime_param->runtime_paras.stack_mode + 1);
    stack_mode->bit.pdt = telecom_standard;
    ui_controller_reset_protostack();

    return 0;
}

uint8_t param_get_language(void)
{
    return param_manager.runtime_param->runtime_paras.language_para;
}

int param_read_language_into(void *field_value_buff, size_t *buff_size)
{
    *(uint8_t *)field_value_buff = param_get_language();
    return 0;
}

int param_write_language_from(const void *field_value_buff, size_t buff_size)
{
    uint8_t language = *((uint8_t *)field_value_buff);
    switch (language)
    {
    case PARAM_LANGUAGE_CHINESE:
        param_manager.runtime_param->runtime_paras.language_para = language;
        return 0;
    case PARAM_LANGUAGE_ENGLISH:
        param_manager.runtime_param->runtime_paras.language_para = language;
        return 0;
    default:
        return -1;
    }
}

bool param_is_auto_brightness_supported(void)
{
    return param_manager.auto_brightness_supported;
}

bool param_is_compass_supported(void)
{
    menu_config_t *menu_config = param_manager_find_menu_config(8);

    // 若menu_config为NULL, 则不生效，指南针默认为不启用
    if (menu_config == NULL)
    {
        return false;
    }

    misc_config_t *misc_config = (misc_config_t *)menu_config;

    return misc_config->bit.compass_en;
}

bool param_is_key_programmable(void)
{
    menu_config_t *menu_config = param_manager_find_menu_config(8);

    // 若menu_config为NULL, 则不生效，指南针默认为不启用
    if (menu_config == NULL)
    {
        return false;
    }
    misc_config_t *misc_config = (misc_config_t *)menu_config;
    return misc_config->bit.key_prog;
}

uint8_t param_get_key_func(uint8_t key_index)
{
    switch (key_index)
    {
    case PARAM_KEY_FUNC_INDEX_VOL_INDEPENDENT:
        return param_manager.static_param->key_remap_table.vol_independent;
    case PARAM_KEY_FUNC_INDEX_LEFT:
        return param_manager.static_param->key_remap_table.func_left;
    case PARAM_KEY_FUNC_INDEX_RIGHT:
        return param_manager.static_param->key_remap_table.func_right;
    case PARAM_KEY_FUNC_INDEX_ENTER:
        return param_manager.static_param->key_remap_table.func_enter;
    case PARAM_KEY_FUNC_INDEX_MIDDLE:
        return param_manager.static_param->key_remap_table.func_middle;
    case PARAM_KEY_FUNC_INDEX_PTT:
        return param_manager.static_param->key_remap_table.func_ptt;
    case PARAM_KEY_FUNC_INDEX_PTT2:
        return param_manager.static_param->key_remap_table.func_ptt2;
    case PARAM_KEY_FUNC_INDEX_RETURN:
        return param_manager.static_param->key_remap_table.func_return;
    case PARAM_KEY_FUNC_INDEX_SHORTCUT:
        return param_manager.static_param->key_remap_table.func_shortcut;
    case PARAM_KEY_FUNC_INDEX_ALARM:
        return param_manager.static_param->key_remap_table.func_alarm;
    case PARAM_KEY_FUNC_INDEX_ZZW_PTT:
        return param_manager.static_param->key_remap_table.zzw_func_ptt;
    case PARAM_KEY_FUNC_INDEX_ZZW_PTT2:
        return param_manager.static_param->key_remap_table.zzw_func_ptt2;
    case PARAM_KEY_FUNC_INDEX_ZZW_RETURN:
        return param_manager.static_param->key_remap_table.zzw_func_return;
    default:
        return 0;
    }
}

bool param_select_subgroups_enable(void)
{
    menu_config_t *menu_config = param_manager_find_menu_config(8);

    // 若menu_config为NULL, 则不生效，指南针默认为不启用
    if (menu_config == NULL)
    {
        return false;
    }
    misc_config_t *misc_config = (misc_config_t *)menu_config;
    return misc_config->bit.subgroup_sel_en_xv;
}

bool param_is_man_down_supported(void)
{
    return param_manager.man_down_detection_supported;
}

bool param_is_switch_supported(void)
{
    return param_manager.switch_supported;
}

int param_read_screen_on_period_into(void *field_value_buff, size_t *buff_size)
{
    // 屏幕点亮时间为8位参数, 其低6位存储在lcd_turnoff中, 高2位存储在reserved9的b3b2中
    *(uint8_t *)field_value_buff = param_get_screen_on_period();
    return 0;
}

uint8_t param_get_screen_on_period(void)
{
    // 屏幕点亮时间为8位参数, 其低6位存储在lcd_turnoff中, 高2位存储在reserved9的b3b2中
    uint8_t screen_on_time_low_6_bits = param_manager.runtime_param->runtime_paras.lcd_ctrl.lcd_turnoff;
    uint8_t screen_on_time_high_2_bits = UI_BF_GET(param_manager.runtime_param->runtime_paras.reserved9, 2, 2);
    uint8_t screen_on_time = screen_on_time_low_6_bits | (screen_on_time_high_2_bits << 6);
    return screen_on_time;
}

int param_write_screen_on_period_from(const void *field_value_buff, size_t buff_size)
{
    uint8_t screen_on_time = *((uint8_t *)field_value_buff);
    uint8_t screen_on_time_low_6_bits = UI_BF_GET(screen_on_time, 0, 6);
    uint8_t screen_on_time_high_2_bits = UI_BF_GET(screen_on_time, 6, 2);
    param_manager.runtime_param->runtime_paras.lcd_ctrl.lcd_turnoff = screen_on_time_low_6_bits;
    UI_BF_SET(param_manager.runtime_param->runtime_paras.reserved9, screen_on_time_high_2_bits, 2, 2);
    hal_disp_set_screen_on_period(screen_on_time);
    return 0;
}

bool param_is_screen_always_on_during_call(void)
{
    return param_manager.static_param->misc_static_config.lcd_on_when_calling;
}

int param_read_screen_inverted_info(void *field_value_buff, size_t *buff_size)
{
    uint8_t screen_rotation = param_manager.runtime_param->runtime_paras.lcd_ctrl.lcd_reverse;
    if (screen_rotation == PARAM_SCREEN_ROT_NONE)
    {
        *(bool *)field_value_buff = false;
    }
    else if (screen_rotation == PARAM_SCREEN_ROT_180)
    {
        *(bool *)field_value_buff = true;
    }
    else
    {
        return -1;
    }

    return 0;
}

int param_write_screen_inverted_from(const void *field_value_buff, size_t buff_size)
{
    bool screen_inverted = *((bool *)field_value_buff);

    if (screen_inverted)
    {
        param_manager.runtime_param->runtime_paras.lcd_ctrl.lcd_reverse = PARAM_SCREEN_ROT_180;
        hal_disp_set_screen_inverted(true);
    }
    else
    {
        param_manager.runtime_param->runtime_paras.lcd_ctrl.lcd_reverse = PARAM_SCREEN_ROT_NONE;
        hal_disp_set_screen_inverted(false);
    }

    return 0;
}

int param_read_theme_into(void *field_value_buff, size_t *buff_size)
{
    *(uint8_t *)field_value_buff = UI_BF_GET(param_manager.runtime_param->runtime_paras.reserved9, 0, 2);
    return 0;
}

int param_write_theme_from(const void *field_value_buff, size_t buff_size)
{
    uint8_t theme = *((uint8_t *)field_value_buff);
    UI_BF_SET(param_manager.runtime_param->runtime_paras.reserved9, theme, 0, 2);
    ui_theme_set_mode((ui_theme_mode_t)theme);
    return 0;
}

int param_read_brightness_level_into(void *field_value_buff, size_t *buff_size)
{
    *(uint8_t *)field_value_buff = hal_disp_get_brightness();
    return 0;
}

int param_write_brightness_level_from(const void *field_value_buff, size_t buff_size)
{
    uint8_t brightness_level = *((uint8_t *)field_value_buff);
    hal_disp_set_max_brightness(brightness_level);
    return hal_disp_set_brightness(brightness_level);
}

int param_read_auto_brightness_enabled_into(void *field_value_buff, size_t *buff_size)
{
    *(bool *)field_value_buff = hal_disp_is_auto_brightness_enabled();
    return 0;
}

uint8_t param_get_signal_icon_display_mode(uint8_t signal_id)
{
    uint8_t work_mode = param_get_work_mode();
    signal_icon_disp_mode_t disp_mode = {.raw = param_manager.runtime_param->runtime_paras.reserved9};

    if (work_mode == PARAM_WORK_MODE_DIGITAL_CONVENTIONAL || work_mode == PARAM_WORK_MODE_DIGITAL_TRUNKING)
    {
        if (signal_id == UI_ICON_ID_LEFT_SIGNAL)
        {
            return disp_mode.bit.pdt_left_num;
        }
        else
        {
            return !disp_mode.bit.pdt_right_bar;
        }
    }
    else
    {
        if (signal_id == UI_ICON_ID_LEFT_SIGNAL)
        {
            return !disp_mode.bit.adhoc_left_bar;
        }
        else
        {
            return disp_mode.bit.adhoc_right_num;
        }
    }
}

void param_set_signal_icon_display_mode(uint8_t signal_icon_id, uint8_t display_mode)
{
    uint8_t work_mode = param_get_work_mode();
    signal_icon_disp_mode_t *disp_mode = (signal_icon_disp_mode_t *)&param_manager.runtime_param->runtime_paras.reserved9;

    if (work_mode == PARAM_WORK_MODE_DIGITAL_CONVENTIONAL || work_mode == PARAM_WORK_MODE_DIGITAL_TRUNKING)
    {
        if (signal_icon_id == UI_ICON_ID_LEFT_SIGNAL)
        {
            disp_mode->bit.pdt_left_num = display_mode;
        }
        else
        {
            disp_mode->bit.pdt_right_bar = !display_mode;
        }
    }
    else
    {
        if (signal_icon_id == UI_ICON_ID_LEFT_SIGNAL)
        {
            disp_mode->bit.adhoc_left_bar = !display_mode;
        }
        else
        {
            disp_mode->bit.adhoc_right_num = display_mode;
        }
    }
}

int param_read_vocoder_type_into(void *field_value_buff, size_t *buff_size)
{
    uint8_t work_mode = param_get_work_mode();
    uint8_t vocoder_type;

    if (ui_utils_is_work_mode_adhoc(work_mode))
    {
        vocoder_type = PARAM_VOCODER_TYPE_VTEC;
    }
    else if (ui_utils_is_work_mode_digital(work_mode))
    {
        vocoder_type = param_manager.runtime_param->runtime_paras.pdt_vocoder.vocoder_type;
    }
    else
    {
        return -1;
    }

    *(uint8_t *)field_value_buff = vocoder_type;
    return 0;
}

int param_write_vocoder_type_from(const void *field_value_buff, size_t buff_size)
{
    // 获取当前的工作模式
    uint8_t work_mode = param_get_work_mode();
    // 获取当前的声码器类型, 新的声码器类型
    uint8_t current_vocoder_type = PARAM_VOCODER_TYPE_UNDEFINED;
    uint8_t vocoder_type = *((uint8_t *)field_value_buff);
    param_read_vocoder_type_into(&current_vocoder_type, NULL);

    // 若声码器类型相同, 则直接返回
    if (current_vocoder_type == vocoder_type)
    {
        return 0;
    }

    // 除数字常规/集群模式之外, 其余模式下不允许修改声码器类型
    if (ui_utils_is_work_mode_digital(work_mode))
    {
        // 若声码器类型不是AMBE或NVOC, 则直接返回
        if (vocoder_type != PARAM_VOCODER_TYPE_AMBE && vocoder_type != PARAM_VOCODER_TYPE_NVOC)
        {
            return -1;
        }
        else
        {
            param_manager.runtime_param->runtime_paras.pdt_vocoder.vocoder_type = vocoder_type;
        }
    }
    else
    {
        return -1;
    }

    ui_controller_set_vocoder();
    return 0;
}

int param_read_vocoder_speed_into(void *field_value_buff, size_t *buff_size)
{
    uint8_t work_mode = param_get_work_mode();
    uint8_t vocoder_speed;

    if (ui_utils_is_work_mode_digital(work_mode))
    {
        vocoder_speed = param_manager.runtime_param->runtime_paras.pdt_vocoder.vocoder_speed;
    }
    else if (ui_utils_is_work_mode_adhoc(work_mode))
    {
        vocoder_speed = PARAM_VOCODER_SPEED_1200;
    }
    else
    {
        return -1;
    }

    *(uint8_t *)field_value_buff = vocoder_speed;
    return 0;
}

int param_write_vocoder_speed_from(const void *field_value_buff, size_t buff_size)
{
    // 获取当前的工作模式
    uint8_t work_mode = param_get_work_mode();

    // 获取当前的声码器速率, 新的声码器速率
    uint8_t current_vocoder_speed = PARAM_VOCODER_SPEED_UNDEFINED;
    param_read_vocoder_speed_into(&current_vocoder_speed, NULL);
    uint8_t vocoder_speed = *((uint8_t *)field_value_buff);

    // 若声码器速率相同, 则直接返回
    if (current_vocoder_speed == vocoder_speed)
    {
        return 0;
    }

    // 除数字常规/集群模式之外, 其余模式下不允许修改声码器速率
    if (ui_utils_is_work_mode_digital(work_mode))
    {
        // 若声码器速率不是2200或2400, 则直接返回
        if (vocoder_speed != PARAM_VOCODER_SPEED_2200 && vocoder_speed != PARAM_VOCODER_SPEED_2400)
        {
            return -1;
        }
        else
        {
            param_manager.runtime_param->runtime_paras.pdt_vocoder.vocoder_speed = vocoder_speed;
        }
    }
    else
    {
        return -1;
    }

    ui_controller_set_vocoder();
    return 0;
}

int param_read_vocoder_agc_into(void *field_value_buff, size_t *buff_size)
{
    uint8_t work_mode = param_get_work_mode();
    bool vocoder_agc = false;

    if (ui_utils_is_work_mode_digital(work_mode))
    {
        vocoder_agc = param_manager.runtime_param->runtime_paras.pdt_vocoder.vocoder_agc;
    }
    else if (ui_utils_is_work_mode_adhoc(work_mode))
    {
        vocoder_agc = param_manager.runtime_param->runtime_paras.zzw_vocoder.vocoder_agc;
    }
    else
    {
        return -1;
    }

    *(bool *)field_value_buff = vocoder_agc;
    return 0;
}

int param_write_vocoder_agc_from(const void *field_value_buff, size_t buff_size)
{
    // 获取当前的工作模式
    uint8_t work_mode = param_get_work_mode();

    // 获取当前的声码器自动增益状态, 新的声码器自动增益状态
    bool current_vocoder_agc = false;
    param_read_vocoder_agc_into(&current_vocoder_agc, NULL);
    bool vocoder_agc = *((bool *)field_value_buff);

    // 若声码器自动增益状态相同, 则直接返回
    if (current_vocoder_agc == vocoder_agc)
    {
        return 0;
    }

    if (ui_utils_is_work_mode_digital(work_mode))
    {
        param_manager.runtime_param->runtime_paras.pdt_vocoder.vocoder_agc = vocoder_agc;
    }
    else if (ui_utils_is_work_mode_adhoc(work_mode))
    {
        param_manager.runtime_param->runtime_paras.zzw_vocoder.vocoder_agc = vocoder_agc;
    }
    else
    {
        return -1;
    }

    ui_controller_set_vocoder();
    return 0;
}

int param_read_vocoder_ns_into(void *field_value_buff, size_t *buff_size)
{
    uint8_t work_mode = param_get_work_mode();
    bool vocoder_ns = false;

    if (ui_utils_is_work_mode_digital(work_mode))
    {
        vocoder_ns = param_manager.runtime_param->runtime_paras.pdt_vocoder.vocoder_ns;
    }
    else if (ui_utils_is_work_mode_adhoc(work_mode))
    {
        vocoder_ns = param_manager.runtime_param->runtime_paras.zzw_vocoder.vocoder_ns;
    }
    else
    {
        return -1;
    }

    *(bool *)field_value_buff = vocoder_ns;
    return 0;
}

int param_write_vocoder_ns_from(const void *field_value_buff, size_t buff_size)
{
    // 获取当前的工作模式
    uint8_t work_mode = param_get_work_mode();

    // 获取当前的声码器噪声抑制, 新的声码器噪声抑制状态
    bool current_vocoder_ns = false;
    param_read_vocoder_ns_into(&current_vocoder_ns, NULL);
    bool vocoder_ns = *((bool *)field_value_buff);

    // 若声码器噪声抑制状态相同, 则直接返回
    if (current_vocoder_ns == vocoder_ns)
    {
        return 0;
    }

    if (ui_utils_is_work_mode_digital(work_mode))
    {
        param_manager.runtime_param->runtime_paras.pdt_vocoder.vocoder_ns = vocoder_ns;
    }
    else if (ui_utils_is_work_mode_adhoc(work_mode))
    {
        param_manager.runtime_param->runtime_paras.zzw_vocoder.vocoder_ns = vocoder_ns;
    }
    else
    {
        return -1;
    }

    ui_controller_set_vocoder();
    return 0;
}

uint8_t param_get_tip_self(void)
{
    return param_manager.runtime_param->runtime_paras.tip_sound.tip_self;
}

int param_read_tip_self_into(void *field_value_buff, size_t *buff_size)
{
    *(uint8_t *)field_value_buff = param_get_tip_self();
    return 0;
}

int param_set_tip_self(uint8_t tip_self)
{
    if (tip_self > PARAM_TIP_LVL_MAX)
    {
        return -1;
    }

    ui_controller_adjust_alert_volume(tip_self);
    return 0;
}

int param_write_tip_self_from(const void *field_value_buff, size_t buff_size)
{
    uint8_t tip_self = *((uint8_t *)field_value_buff);
    return param_set_tip_self(tip_self);
}

int param_read_sound_gain_into(void *field_value_buff, size_t *buff_size)
{
    *(uint8_t *)field_value_buff = param_get_sound_gain();
    return 0;
}

uint8_t param_get_sound_gain(void)
{
    if (param_is_earphone_plugged_in())
    {
        return param_manager.runtime_param->runtime_paras.earphone_gain.sound_gain;
    }
    else
    {
        return param_manager.runtime_param->runtime_paras.self_gain.sound_gain;
    }
}

uint8_t param_get_nextprev_sound_gain(uint8_t sound_gain, uint8_t nextprev)
{
    if (nextprev == UI_NEXT)
    {
        switch (sound_gain)
        {
        case PARAM_SOUND_GAIN_LVL_0:
            return PARAM_SOUND_GAIN_LVL_1;
        case PARAM_SOUND_GAIN_LVL_1:
            return PARAM_SOUND_GAIN_LVL_2;
        case PARAM_SOUND_GAIN_LVL_2:
            return PARAM_SOUND_GAIN_LVL_3;
        case PARAM_SOUND_GAIN_LVL_3:
            return PARAM_SOUND_GAIN_LVL_4;
        case PARAM_SOUND_GAIN_LVL_4:
            return PARAM_SOUND_GAIN_LVL_5;
        case PARAM_SOUND_GAIN_LVL_5:
            return PARAM_SOUND_GAIN_LVL_6;
        case PARAM_SOUND_GAIN_LVL_6:
            return PARAM_SOUND_GAIN_LVL_7;
        case PARAM_SOUND_GAIN_LVL_7:
            return PARAM_SOUND_GAIN_LVL_8;
        case PARAM_SOUND_GAIN_LVL_8:
            return PARAM_SOUND_GAIN_LVL_MAX;
        case PARAM_SOUND_GAIN_LVL_MAX:
            return PARAM_SOUND_GAIN_LVL_0;
        default:
            return PARAM_SOUND_GAIN_LVL_0;
        }
    }
    else
    {
        switch (sound_gain)
        {
        case PARAM_SOUND_GAIN_LVL_0:
            return PARAM_SOUND_GAIN_LVL_MAX;
        case PARAM_SOUND_GAIN_LVL_1:
            return PARAM_SOUND_GAIN_LVL_0;
        case PARAM_SOUND_GAIN_LVL_2:
            return PARAM_SOUND_GAIN_LVL_1;
        case PARAM_SOUND_GAIN_LVL_3:
            return PARAM_SOUND_GAIN_LVL_2;
        case PARAM_SOUND_GAIN_LVL_4:
            return PARAM_SOUND_GAIN_LVL_3;
        case PARAM_SOUND_GAIN_LVL_5:
            return PARAM_SOUND_GAIN_LVL_4;
        case PARAM_SOUND_GAIN_LVL_6:
            return PARAM_SOUND_GAIN_LVL_5;
        case PARAM_SOUND_GAIN_LVL_7:
            return PARAM_SOUND_GAIN_LVL_6;
        case PARAM_SOUND_GAIN_LVL_8:
            return PARAM_SOUND_GAIN_LVL_7;
        case PARAM_SOUND_GAIN_LVL_MAX:
            return PARAM_SOUND_GAIN_LVL_8;
        default:
            return PARAM_SOUND_GAIN_LVL_0;
        }
    }
}

int param_set_sound_gain(uint8_t sound_gain)
{
    if (sound_gain > PARAM_SOUND_GAIN_LVL_MAX)
    {
        return -1;
    }

    if (param_get_work_mode() == PARAM_WORK_MODE_POC)
    {
        uint8_t trx_state = param_get_trx_state();

        if (trx_state == PARAM_TRX_STATE_IDLE)
        {
            param_manager.runtime_param->runtime_paras.self_gain.sound_gain = sound_gain;
        }
        else
        {
            ui_controller_adjust_voice_volume(sound_gain);
        }
    }
    else
    {
        ui_controller_adjust_voice_volume(sound_gain);
    }

    return 0;
}

int param_write_sound_gain_from(const void *field_value_buff, size_t buff_size)
{
    uint8_t sound_gain = *((uint8_t *)field_value_buff);
    return param_set_sound_gain(sound_gain);
}

int param_read_mc_device_information_into(void *field_value_buff, size_t *buff_size)
{
    lv_memcpy(field_value_buff, &param_manager.runtime_param->device_information, sizeof(DEVICE_INFORMATION));
    return 0;
}

int param_device_type_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    const uint8_t *device_type = param_manager.static_param->device_name.dev_type;
    int result = lv_snprintf((char *)string_buff, *string_buff_size, "%s", device_type);
    *string_buff_size = result;
    return result;
}

int param_device_name_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    const uint8_t *device_name = param_manager.static_param->device_name.dev_name;
    size_t device_name_len = sizeof(param_manager.static_param->device_name.dev_name);
    return proto_gb2312_array_to_utf8_string(device_name, device_name_len, (char *)string_buff, string_buff_size);
}

uint32_t param_get_device_id(void)
{
    uint8_t work_mode = param_get_work_mode();

    if (ui_utils_is_work_mode_digital(work_mode))
    {
        return param_manager.static_param->stack_pdt.id & 0xFFFFFF;
    }
    else if (ui_utils_is_work_mode_xv(work_mode))
    {
        return param_manager.static_param->stack_zzw.id & 0xFFFFFF;
    }
    else if (work_mode == PARAM_WORK_MODE_ANALOG_TRUNKING)
    {
        return param_manager.static_param->stack_mpt.id & 0xFFFFFF;
    }
    else if (work_mode == PARAM_WORK_MODE_POC)
    {
        return param_manager.static_param->stack_pdt.id & 0xFFFFFF;
    }
    else
    {
        return 0;
    }
}

int param_device_id_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    uint32_t id = param_get_device_id();

    if (id == 0)
    {
        return -1;
    }

    // 创建一个临时的缓冲区, 用于存储转换后的字符串
    char buff_tmp[20] = {0};
    size_t buff_size_tmp = sizeof(buff_tmp);

    // 将ID转换为字符串
    int result = ui_utils_id_to_string(id, UI_INDIVIDUAL, buff_tmp, &buff_size_tmp, '-');
    if (result <= 0)
    {
        result = lv_snprintf(string_buff, *string_buff_size, "%d", (id & 0xffffff));
        *string_buff_size = result;
    }
    else
    {
        result = lv_snprintf(string_buff, *string_buff_size, "%s", buff_tmp);
        *string_buff_size = result;
    }

    return result;
}

param_device_type_t param_get_device_type(void)
{
    const uint8_t *device_esn = param_manager.static_param->factory_paras.esn;
    uint8_t type_num = ((device_esn[1] & 0x7F) << 1) + (device_esn[2] >> 7);

    switch (type_num)
    {
    case 50:
        return PARAM_DEV_TYPE_P1;
    case 51:
        return PARAM_DEV_TYPE_P2;
    case 52:
        return PARAM_DEV_TYPE_P3;
    case 45:
        return PARAM_DEV_TYPE_K1;
    case 46:
        return PARAM_DEV_TYPE_K2;
    case 47:
        return PARAM_DEV_TYPE_K3;
    case 57:
        return PARAM_DEV_TYPE_AK811;
    default:
        return PARAM_DEV_TYPE_P1;
    }
}

int param_read_device_esn_into(void *field_value_buff, size_t *buff_size)
{
    const uint8_t *device_esn = param_manager.static_param->factory_paras.esn;
    uint16_t manufacturer_number = ((device_esn[0] << 1) | (device_esn[1] >> 7)) & 0x1FF;                                          // 9bit
    uint8_t type_number = ((device_esn[1] & 0x7F) << 1) + (device_esn[2] >> 7);                                                    // 8bit
    uint32_t serial_number = ((device_esn[2] & 0x7F) << 23) | (device_esn[3] << 15) | (device_esn[4] << 7) | (device_esn[5] >> 1); // 30bit

    dx_device_esn_t *esn = (dx_device_esn_t *)field_value_buff;
    esn->manufacturer = manufacturer_number;
    esn->type = type_number;
    esn->serial_num = serial_number;
    return 0;
}

int param_device_esn_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    const uint8_t *device_esn = param_manager.static_param->factory_paras.esn;
    uint16_t manufacturer_number = ((device_esn[0] << 1) | (device_esn[1] >> 7)) & 0x1FF;                                          // 9bit
    uint8_t type_number = ((device_esn[1] & 0x7F) << 1) + (device_esn[2] >> 7);                                                    // 8bit
    uint32_t serial_number = ((device_esn[2] & 0x7F) << 23) | (device_esn[3] << 15) | (device_esn[4] << 7) | (device_esn[5] >> 1); // 30bit
    int result = snprintf(string_buff, *string_buff_size, "%03d.%03d.%010d", manufacturer_number, type_number, serial_number);

    if (result < 0 || result >= *string_buff_size)
    {
        return -1;
    }
    else
    {
        *string_buff_size = result;
        return result;
    }
}

int param_band_range_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    const BAND_RANGE_TYPEDEF *band_range = &param_manager.static_param->factory_paras.band_range;

    uint16_t low, high;
    if (band_range->valid == 0x6c)
    {
        low = band_range->lf_hsb * 100 + band_range->lf_msb * 10 + band_range->lf_lsb;
        high = band_range->hf_hsb * 100 + band_range->hf_msb * 10 + band_range->hf_lsb;

        if (low && high && (low != INVALID_BAND_RANGE_VALUE) && (high != INVALID_BAND_RANGE_VALUE))
        {
            int result = lv_snprintf(string_buff, *string_buff_size, "%3d-%3dMHz", low, high);

            if (result < 0 || result >= *string_buff_size)
            {
                return -1;
            }
            else
            {
                *string_buff_size = result;
                return result;
            }
        }
    }

    return -1;
}

int param_mcu_info_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    uint8_t ver_major = param_manager.runtime_param->device_information.mcu_soft_ver.ver_major;
    uint8_t ver_minor = param_manager.runtime_param->device_information.mcu_soft_ver.ver_minor;
    uint32_t year = param_manager.runtime_param->device_information.mcu_compile.rtc_year;
    uint32_t month = param_manager.runtime_param->device_information.mcu_compile.rtc_month;
    uint32_t day = param_manager.runtime_param->device_information.mcu_compile.rtc_day;

    int result = lv_snprintf(string_buff, *string_buff_size, "%02x.%02x%c%02d%02d%02d%c", ver_major, ver_minor, '(', year, month, day, ')');

    if (result < 0 || result >= *string_buff_size)
    {
        return -1;
    }
    else
    {
        *string_buff_size = result + 1;
        return result;
    }
}

int param_dsp_info_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    uint8_t ver_major = param_manager.runtime_param->device_information.dsp_soft_ver.ver_major;
    uint8_t ver_minor = param_manager.runtime_param->device_information.dsp_soft_ver.ver_minor;
    uint32_t year = param_manager.runtime_param->device_information.dsp_compile.ct_year;
    uint32_t month = param_manager.runtime_param->device_information.dsp_compile.ct_month;
    uint32_t day = param_manager.runtime_param->device_information.dsp_compile.ct_day;

    int result = lv_snprintf(string_buff, *string_buff_size, "%02x.%02x(%02d%02d%02d)", ver_major, ver_minor, year, month, day);

    if (result < 0 || result >= *string_buff_size)
    {
        return -1;
    }
    else
    {
        *string_buff_size = result + 1;
        return result;
    }
}

int param_stack_info_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    uint8_t ver_major = param_manager.runtime_param->device_information.stack_soft_ver.ver_major;
    uint8_t ver_minor = param_manager.runtime_param->device_information.stack_soft_ver.ver_minor;
    uint32_t year = param_manager.runtime_param->device_information.stack_compile.rtc_year;
    uint32_t month = param_manager.runtime_param->device_information.stack_compile.rtc_month;
    uint32_t day = param_manager.runtime_param->device_information.stack_compile.rtc_day;

    int result = lv_snprintf(string_buff, *string_buff_size, "%02x.%02x(%02d%02d%02d)", ver_major, ver_minor, year, month, day);

    if (result < 0 || result >= *string_buff_size)
    {
        return -1;
    }
    else
    {
        *string_buff_size = result + 1;
        return result;
    }
}

int param_gui_version_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    int result = lv_snprintf(string_buff, *string_buff_size, "%d.%d.%d(%04d/%02d/%02d)", UI_VERSION_MAJOR, UI_VERSION_MINOR, UI_VERSION_PATCH, UI_BUILD_YEAR, UI_BUILD_MONTH, UI_BUILD_DAY);

    if (result < 0 || result >= *string_buff_size)
    {
        return -1;
    }
    else
    {
        *string_buff_size = result + 1;
        return result;
    }
}

int param_read_location_service_enabled_into(void *field_value_buff, size_t *buff_size)
{
    *(bool *)field_value_buff = param_is_location_service_enabled();
    return 0;
}

bool param_is_location_service_enabled(void)
{
    return param_manager.runtime_param->runtime_paras.pwr_ctrl.pwr_gps;
}

int param_write_location_service_enabled_from(const void *field_value_buff, size_t buff_size)
{
    bool location_service_enabled = *((bool *)field_value_buff);
    param_set_location_service_enabled(location_service_enabled);
    return 0;
}

void param_set_location_service_enabled(bool enabled)
{
    // 若当前定位服务状态与设置的定位服务状态相同, 则不做任何操作
    if (enabled == param_manager.runtime_param->runtime_paras.pwr_ctrl.pwr_gps)
    {
        return;
    }

    ui_controller_toggle_location_service(enabled);
}

int param_read_location_service_state_into(void *field_value_buff, size_t *buff_size)
{
    if (param_is_location_service_enabled())
    {
        *(uint8_t *)field_value_buff = param_get_positioning_system_type();
    }
    else
    {
        *(uint8_t *)field_value_buff = PARAM_POSITIONING_SYSTEM_OFF;
    }

    return 0;
}

bool param_is_locked_onto_satellite(void)
{
    const STATUS_TO_BT_TYPEDEF *latest_mc_status = param_get_pending_mc_status_frame();
    uint8_t gps_state = latest_mc_status->rmt_gps_state;
    return UI_HAS_BIT(gps_state, 4);
}

int param_read_positioning_system_type_into(void *field_value_buff, size_t *buff_size)
{
    *(uint8_t *)field_value_buff = param_get_positioning_system_type();
    return 0;
}

uint8_t param_get_positioning_system_type(void)
{
    uint8_t positioning_system_type = param_manager.runtime_param->runtime_paras.pwr_ctrl.gps_type;

    if (positioning_system_type != PARAM_POSITIONING_SYSTEM_GPS && positioning_system_type != PARAM_POSITIONING_SYSTEM_BEIDOU)
    {
        positioning_system_type = PARAM_POSITIONING_SYSTEM_DUAL;
    }

    return positioning_system_type;
}

int param_write_positioning_system_type_from(const void *field_value_buff, size_t buff_size)
{
    uint8_t positioning_system_type = *((uint8_t *)field_value_buff);

    if (positioning_system_type >= PARAM_POSITIONING_SYSTEM_COUNT)
    {
        return -1;
    }

    // 若系统启用单北斗定位，但程序尝试设置其他定位系统类型，则返回
    if (param_is_bd_only() && positioning_system_type != PARAM_POSITIONING_SYSTEM_BEIDOU)
    {
        return -1;
    }

    // 若当前定位系统类型与设置的定位系统类型相同, 则不做任何操作
    uint8_t current_positioning_system_type = param_get_positioning_system_type();
    if (positioning_system_type == current_positioning_system_type)
    {
        return 0;
    }

    // 设置定位系统类型
    ui_controller_set_positioning_system(positioning_system_type);

    return 0;
}

param_device_state_t *param_get_device_state(void)
{
    return param_manager.dev_state_curr;
}

param_device_state_t *param_get_prev_device_state(void)
{
    return param_manager.dev_state_prev;
}

uint8_t param_get_trx_state(void)
{
    return param_manager.dev_state_curr->trx_state;
}

uint8_t param_get_prev_trx_state(void)
{
    return param_manager.dev_state_prev->trx_state;
}

bool param_is_call_incoming(void)
{
    return param_manager.dev_state_curr->is_incoming_call;
}

void param_update_status_trx_state(uint8_t trx_state)
{
    param_manager.dev_state_curr->trx_state = trx_state;
}

void param_update_status_voice_call_options(bool ig, bool emg, bool bcast, bool e2ee, bool all, bool caller)
{
    param_manager.dev_state_curr->ig = ig;
    param_manager.dev_state_curr->emg = emg;
    param_manager.dev_state_curr->bcast = bcast;
    param_manager.dev_state_curr->e2ee = e2ee;
    param_manager.dev_state_curr->all = all;
    param_manager.dev_state_curr->calling_party = caller;
}

void param_update_status_pstn_call_info(bool in_pstn_call, const char *number)
{
    if (in_pstn_call)
    {
        param_manager.pstn = true;
        lv_snprintf(param_manager.pstn_number, sizeof(param_manager.pstn_number), "%s", number);
    }
    else
    {
        param_manager.pstn = false;
        memset(param_manager.pstn_number, 0, sizeof(param_manager.pstn_number));
    }
}

bool param_is_caller(void)
{
    return param_manager.dev_state_curr->calling_party;
}

bool param_is_call_group(void)
{
    return param_manager.dev_state_curr->ig;
}

bool param_is_call_emergency(void)
{
    return param_manager.dev_state_curr->emg;
}

bool param_is_call_broadcast(void)
{
    return param_manager.dev_state_curr->bcast;
}

bool param_is_call_e2ee(void)
{
    return param_manager.dev_state_curr->e2ee;
}

bool param_is_call_all(void)
{
    return param_manager.dev_state_curr->all;
}

uint16_t param_get_voice_call_options(void)
{
    dx_so_voice_t so = 
    {
        .bit.pl = param_get_priority(),
        .bit.ig = param_is_call_group(),
        .bit.emg = param_is_call_emergency(),
        .bit.e2ee = param_is_call_e2ee(),
        .bit.bcast = param_is_call_all(),
        .bit.all = param_is_call_all(),
        .bit.caller = param_is_caller()
    };

    return so.raw;
}

bool param_is_call_pstn(void)
{
    return param_manager.pstn;
}

bool param_is_in_dynamic_group(void)
{
    return param_manager.dev_state_curr->is_in_dynamic_group;
}

bool param_is_in_ambient_listening(void)
{
    return param_manager.dev_state_curr->is_ambient_listening;
}

uint32_t param_get_dynamic_group_id(void)
{
    return param_manager.dev_state_curr->dynamic_group_id;
}

int param_read_pstn_number_into(void *field_value_buff, size_t *buff_size)
{
    if (!param_is_call_pstn())
    {
        return -1;
    }
    else
    {
        const char *pstn_number = param_manager.pstn_number;
        *buff_size = lv_snprintf(field_value_buff, *buff_size, "%s", pstn_number);
        return 0;
    }
}

void param_update_status_caller_id(uint32_t caller_id)
{
    param_manager.dev_state_curr->caller_id = caller_id & 0xFFFFFF;
}

void param_update_status_callee_id(uint32_t callee_id)
{
    param_manager.dev_state_curr->callee_id = callee_id & 0xFFFFFF;
}

void param_update_status_speaker_id(uint32_t speaker_id)
{
    param_manager.dev_state_curr->speaker_id = speaker_id & 0xFFFFFF;
}

void param_update_status_call_time(uint16_t call_time)
{
    param_manager.dev_state_curr->call_time = call_time;
}

uint16_t param_get_standby_status(void)
{
    param_device_state_t *dev_state = param_manager.dev_state_curr;

    dx_standby_status_t standby_status = 
    {
        .bit.ambient_listening = dev_state->is_ambient_listening,
        .bit.calling = ui_utils_is_calling(dev_state->trx_state),
        .bit.connected = param_is_registered(),
        .bit.dynamic_grp = dev_state->is_in_dynamic_group,
        .bit.ringing = dev_state->is_ringing,
        .bit.sats_locked = param_is_locked_onto_satellite(),
    };

    uint8_t stun_state = param_get_stun_state();
    if (stun_state == PARAM_STUN_STATE_STUN)
    {
        standby_status.bit.stunned = true;
    }
    else if (stun_state == PARAM_STUN_STATE_KILL)
    {
        standby_status.bit.killed = true;
    }

    return standby_status.raw;
}

bool param_is_ringing(void)
{
    return param_manager.dev_state_curr->is_ringing;
}

uint16_t param_get_call_time(void)
{
    return param_manager.dev_state_curr->call_time;
}

uint32_t param_get_caller_id(void)
{
    return param_manager.dev_state_curr->caller_id;
}

uint32_t param_get_callee_id(void)
{
    return param_manager.dev_state_curr->callee_id;
}

uint32_t param_get_speaker_id(void)
{
    return param_manager.dev_state_curr->speaker_id;
}

uint8_t param_get_call_type(void)
{
    param_device_state_t *dev_state = param_manager.dev_state_curr;
    uint8_t work_mode = param_get_work_mode();

    if (work_mode == PARAM_WORK_MODE_ANALOG_CONVENTIONAL)
    {
        return UI_CALL_TYPE_NORMAL;
    }

    if (dev_state->trx_state == PARAM_TRX_STATE_IDLE)
    {
        return UI_CALL_TYPE_UNDEFINED;
    }

    if (dev_state->all)
    {
        if (dev_state->bcast && !dev_state->emg)
        {
            return UI_CALL_TYPE_ALL_BCAST;
        }
        else if (dev_state->bcast && dev_state->emg)
        {
            return UI_CALL_TYPE_ALL_EMG_BCAST;
        }
        else
        {
            return UI_CALL_TYPE_NORMAL;
        }
    }
    else
    {
        if (dev_state->emg && dev_state->bcast)
        {
            return UI_CALL_TYPE_EMG_BCAST;
        }
        else if (dev_state->emg)
        {
            return UI_CALL_TYPE_EMERGENCY;
        }
        else if (dev_state->bcast)
        {
            return UI_CALL_TYPE_BROADCAST;
        }
        else
        {
            return UI_CALL_TYPE_NORMAL;
        }
    }
}

int16_t param_get_rssi(void)
{
    uint8_t work_mode = param_get_work_mode();

    if (ui_utils_is_work_mode_adhoc(work_mode) && param_is_registered())
    {
        return param_get_latest_rcv_rssi();
    }
    else if (work_mode == PARAM_WORK_MODE_POC)
    {
        uint8_t csq = param_manager.csq;

        if (csq == 99)
        {
            // 返回一个无效值
            return INT16_MIN;
        }
        else
        {
            return (int16_t)(-113 + 2 * csq);
        }
    }
    else
    {
        return param_get_latest_rssi();
    }
}

int param_write_beidou_enabled_from(const void *field_value_buff, size_t buff_size)
{
    // 开启定位服务
    bool location_service_enabled = true;
    param_write_location_service_enabled_from(&location_service_enabled, 0);

    // 设置定位系统类型为北斗
    uint8_t positioning_system_type = PARAM_POSITIONING_SYSTEM_BEIDOU;
    param_write_positioning_system_type_from(&positioning_system_type, 0);
    
    return 0;
}

int param_device_longitude_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    const STATUS_TO_BT_TYPEDEF *gps_state_pointer = param_get_pending_mc_status_frame();
    uint8_t gps_state = gps_state_pointer->rmt_gps_state;
    int result;

    if (UI_HAS_BIT(gps_state, 4))
    { // isAvailable
        const float *longitude = gps_state_pointer->own_gps_pos;
        uint8_t d = (uint8_t)(longitude[0]);
        uint8_t m = (uint8_t)((longitude[0] - d) * 60);
        uint8_t s = (uint8_t)(((longitude[0] - d) * 60 - m) * 60);

        if (UI_HAS_BIT(gps_state, 5))
        { // 1:E,0:W
            result = lv_snprintf(string_buff, *string_buff_size, "%d°%02d'%02d\"E", d, m, s);
        }
        else
        {
            result = lv_snprintf(string_buff, *string_buff_size, "%d°%02d'%02d\"W", d, m, s);
        }
    }
    else
    {
        result = lv_snprintf(string_buff, *string_buff_size, "%d°%02d'%02d\"E", 0, 0, 0);
    }

    if (result < 0 || result >= *string_buff_size)
    {
        return -1;
    }
    else
    {
        *string_buff_size = result;
        return result;
    }
}

int param_device_latitude_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    const STATUS_TO_BT_TYPEDEF *gps_state_pointer = param_get_pending_mc_status_frame();
    uint8_t gps_state = gps_state_pointer->rmt_gps_state;
    int result;

    if (UI_HAS_BIT(gps_state, 4))
    { // isAvailable
        const float *latitude = gps_state_pointer->own_gps_pos;
        uint8_t d = (uint8_t)(latitude[1]);
        uint8_t m = (uint8_t)((latitude[1] - d) * 60);
        uint8_t s = (uint8_t)(((latitude[1] - d) * 60 - m) * 60);

        if (UI_HAS_BIT(gps_state, 6))
        { // 1:N,0:S
            result = lv_snprintf(string_buff, *string_buff_size, "%d°%02d'%02d\"N", d, m, s);
        }
        else
        {
            result = lv_snprintf(string_buff, *string_buff_size, "%d°%02d'%02d\"S", d, m, s);
        }
    }
    else
    {
        result = lv_snprintf(string_buff, *string_buff_size, "%d°%02d'%02d\"N", 0, 0, 0);
    }

    if (result < 0 || result >= *string_buff_size)
    {
        return -1;
    }
    else
    {
        *string_buff_size = result;
        return result;
    }
}

int param_device_time_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    uint8_t *rtc_time = param_manager.gui_ia->rtc_time;
    int result;

    if (param_is_locked_onto_satellite())
    {
        result = lv_snprintf(string_buff, *string_buff_size, "%02d:%02d:%02d", rtc_time[0], rtc_time[1], rtc_time[2]);
    }
    else
    {
        result = lv_snprintf(string_buff, *string_buff_size, "%02d:%02d:%02d", 0, 0, 0);
    }

    if (result < 0 || result >= *string_buff_size)
    {
        return -1;
    }
    else
    {
        *string_buff_size = result;
        return result;
    }
}

int param_device_speed_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    const STATUS_TO_BT_TYPEDEF *status_frame = param_get_pending_mc_status_frame();
    uint8_t speed = status_frame->own_speed;
    int result;

    if (param_is_locked_onto_satellite())
    {
        result = lv_snprintf(string_buff, *string_buff_size, "%dkm/h", speed);
    }
    else
    {
        result = lv_snprintf(string_buff, *string_buff_size, "%dkm/h", 0);
    }

    if (result < 0 || result >= *string_buff_size)
    {
        return -1;
    }
    else
    {
        *string_buff_size = result;
        return result;
    }
}

uint8_t param_get_battery_level(void)
{
    return param_manager.gui_ia->dev_misc_notify2.batt_lv;
}

int param_read_battery_level_into(void *field_value_buff, size_t *buff_size)
{
    *(uint8_t *)field_value_buff = param_get_battery_level();
    return 0;
}

void param_set_battery_level(uint8_t battery_level)
{
    param_manager.gui_ia->dev_misc_notify2.batt_lv = battery_level;
}

int param_read_satellite_count_into(void *field_value_buff, size_t *buff_size)
{
    *(uint8_t *)field_value_buff = param_manager.gui_ia->dev_misc_notify2.locked_num;
    return 0;
}

int param_satellite_count_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    uint8_t satellite_count = param_manager.gui_ia->dev_misc_notify2.locked_num;
    int result = lv_snprintf(string_buff, *string_buff_size, "%d", satellite_count);

    if (result < 0 || result >= *string_buff_size)
    {
        return -1;
    }
    else
    {
        *string_buff_size = result;
        return result;
    }
}

int param_read_default_position_into(void *field_value_buff, size_t *buff_size)
{
    size_t size = sizeof(param_manager.static_param->stack_zzw.zzw_default_pos);
    lv_memcpy(field_value_buff, param_manager.static_param->stack_zzw.zzw_default_pos, size);
    return 0;
}

bool param_is_earphone_plugged_in(void)
{
    return param_manager.gui_ia->dev_misc_notify.earphone;
}

bool param_is_poc_module_online(void)
{
    return param_manager.module_poc_online;
}

void param_set_poc_module_online(bool online)
{
    param_manager.module_poc_online = online;

    if (online)
    {
        param_manager.gui_ia->dev_misc_notify.limit_dport_data = true;
    }
}

bool param_is_registered(void)
{
    uint8_t work_mode = param_get_work_mode();

    if (work_mode == PARAM_WORK_MODE_DIGITAL_TRUNKING || work_mode == PARAM_WORK_MODE_ANALOG_TRUNKING)
    {
        uint32_t cs = param_get_latest_stack_cs();
        return protostack_cs_is_registered(cs);
    }
    else if (work_mode == PARAM_WORK_MODE_ADHOC || work_mode == PARAM_WORK_MODE_ADHOC_N ||
             work_mode == PARAM_WORK_MODE_ADHOC_V25 || work_mode == PARAM_WORK_MODE_ADHOC_V)
    {
        uint32_t xcs = param_get_latest_stack_xcs();

        // 若收到基站心跳等, 则显示有效信号, 并显示心跳的场强; 反之, 显示无效信号条
        if (protostack_xcs_is_heartbeat_sync(xcs) || protostack_xcs_is_bs_sync(xcs) || protostack_xcs_is_gps_sync(xcs))
        {
            return true;
        }
        else
        {
            return false;
        }
    }
    else if (work_mode == PARAM_WORK_MODE_POC)
    {
        if (param_is_poc_service_ok())
        {
            return true;
        }
        else
        {
            return false;
        }
    }
    else
    {
        return false;
    }
}

int param_read_latest_lai_into(void *field_value_buff, size_t *buff_size)
{
    const STATUS_TO_BT_TYPEDEF *status_frame = param_get_pending_mc_status_frame();
    *(uint16_t *)field_value_buff = status_frame->stk_state.lai;
    return 0;
}

int param_read_latest_chan_into(void *field_value_buff, size_t *buff_size)
{
    const STATUS_TO_BT_TYPEDEF *status_frame = param_get_pending_mc_status_frame();
    *(uint16_t *)field_value_buff = UI_BF_GET(status_frame->stk_state.chan, 0, 12);
    return 0;
}

int param_read_latest_cc_into(void *field_value_buff, size_t *buff_size)
{
    const STATUS_TO_BT_TYPEDEF *status_frame = param_get_pending_mc_status_frame();
    *(uint16_t *)field_value_buff = UI_BF_GET(status_frame->stk_state.chan, 12, 4);
    return 0;
}

int param_read_latest_rssi_into(void *field_value_buff, size_t *buff_size)
{
    *(int16_t *)field_value_buff = param_get_latest_rssi();
    return 0;
}

int16_t param_get_latest_rssi(void)
{
    const STATUS_TO_BT_TYPEDEF *status_frame = param_get_pending_mc_status_frame();
    static int16_t rssi = -129;

    if (status_frame->rssi > -140)
    {
        rssi = status_frame->rssi;
    }

    return rssi;
}

int param_read_latest_rcv_rssi_into(void *field_value_buff, size_t *buff_size)
{
    const STATUS_TO_BT_TYPEDEF *status_frame = param_get_pending_mc_status_frame();
    *(int16_t *)field_value_buff = ui_utils_convert_rssi(status_frame->rcv_rssi);
    return 0;
}

int16_t param_get_latest_rcv_rssi(void)
{
    const STATUS_TO_BT_TYPEDEF *status_frame = param_get_pending_mc_status_frame();
    return ui_utils_convert_rssi(status_frame->rcv_rssi);
}

int16_t param_get_rssi_from_slot_info(void)
{
    const ZZWPRO_STATUS_STRUCT *slot_info = param_manager.gui_ia->zzw_sta;
    uint8_t rssi_max = 0;

    for (int i = 0; i < UI_SLOT_NUM_MAX; i++)
    {
        if (slot_info[i].sta.tx == 0 && slot_info[i].sta.rx == 0)
        {
            if (slot_info[i].rssi > rssi_max)
            {
                rssi_max = slot_info[i].rssi;
            }
        }
    }

    return ui_utils_convert_rssi(rssi_max);
}

int16_t param_get_latest_poc_rssi(void)
{
    uint8_t csq = param_manager.csq;

    if (csq == 99)
    {
        // 返回一个无效值
        return INT16_MIN;
    }
    else
    {
        return (int16_t)(-113 + 2 * csq);
    }
}

uint8_t param_get_latest_poc_csq(void)
{
    return param_manager.network_info.csq;
}

int16_t param_get_latest_poc_network_info_rssi(void)
{
    return param_manager.network_info.rssi;
}

uint16_t param_get_latest_poc_login_code(void)
{
    return param_manager.network_info.login_code;
}

int16_t param_get_latest_poc_rsrp(void)
{
    return param_manager.network_info.rsrp;
}

int16_t param_get_latest_poc_rsrq(void)
{
    return param_manager.network_info.rsrq;
}

int16_t param_get_latest_poc_snr(void)
{
    return param_manager.network_info.snr;
}

void param_set_poc_network_info(dx_4g_network_info_dsc_t *network_info)
{
    memmove(&param_manager.network_info, network_info, sizeof(dx_4g_network_info_dsc_t));
}

int param_read_bluetooth_enabled_into(void *field_value_buff, size_t *buff_size)
{
    *(bool *)field_value_buff = param_is_bluetooth_enabled();
    return 0;
}

bool param_is_bluetooth_enabled(void)
{
    return param_manager.runtime_param->runtime_paras.pwr_ctrl.pwr_bt;
}

int param_write_bluetooth_enabled_from(const void *field_value_buff, size_t buff_size)
{
    bool bluetooth_enabled = *((bool *)field_value_buff);
    ui_controller_toggle_bluetooth(bluetooth_enabled);
    return 0;
}

int param_read_encryption_into(void *field_value_buff, size_t *buff_size)
{
    *(bool *)field_value_buff = param_manager.gui_ia->dev_misc_notify.crypto;
    return 0;
}

bool param_is_encryption_enabled(void)
{
    return param_manager.runtime_param->runtime_paras.voice_para.voice_crypto;
}

int param_read_encryption_enabled_into(void *field_value_buff, size_t *buff_size)
{
    *(bool *)field_value_buff = param_is_encryption_enabled();
    return 0;
}

int param_write_encryption_enabled_from(const void *field_value_buff, size_t buff_size)
{
    bool encryption_enabled = *((bool *)field_value_buff);

    // 若当前加密状态与设置的加密状态相同, 则不做任何操作
    if (encryption_enabled == param_manager.runtime_param->runtime_paras.voice_para.voice_crypto)
    {
        return 0;
    }

    param_manager.runtime_param->runtime_paras.voice_para.voice_crypto = encryption_enabled;
    ui_controller_reboot_device();

    return 0;
}

bool param_is_encryption_card_detection_enabled(void)
{
    return !param_manager.runtime_param->runtime_paras.voice_para.sdcard_disable;
}

int param_read_encryption_card_detection_enabled_into(void *field_value_buff, size_t *buff_size)
{
    *(bool *)field_value_buff = param_is_encryption_card_detection_enabled();
    return 0;
}

int param_write_encryption_card_detection_enabled_from(const void *field_value_buff, size_t buff_size)
{
    bool encryption_card_detection_enabled = *((bool *)field_value_buff);

    // 若当前加密卡检测状态与设置的加密卡检测状态相同, 则不做任何操作
    if (encryption_card_detection_enabled == !param_manager.runtime_param->runtime_paras.voice_para.sdcard_disable)
    {
        return 0;
    }

    param_manager.runtime_param->runtime_paras.voice_para.sdcard_disable = !encryption_card_detection_enabled;
    ui_controller_reboot_device();

    return 0;
}

int param_read_active_zone_into(void *field_value_buff, size_t *buff_size)
{
    uint8_t active_zone = param_manager.runtime_param->runtime_paras.active_group_ctrl;
    *(uint8_t *)field_value_buff = active_zone;
    return 0;
}

int param_write_active_zone_from(const void *field_value_buff, size_t buff_size)
{
    uint8_t active_zone_index = *((uint8_t *)field_value_buff);
    param_manager.runtime_param->runtime_paras.active_group_ctrl = active_zone_index;
    ui_controller_reboot_device();
    return 0;
}

uint16_t param_get_zone_count(void)
{
    return param_manager.zone_list.size;
}

int param_get_zone_index(uint8_t zone_num)
{
    if (zone_num >= param_manager.zone_list.size)
    {
        return -1;
    }

    return param_manager.zone_list.zone_index_list[zone_num];
}

uint16_t param_read_active_zone_into_tscc_count(void)
{
    return param_manager.tscc_list.size;
}

uint32_t param_get_tscc_dsc(uint16_t index)
{
    if (index >= param_manager.tscc_list.size)
    {
        return 0;
    }

    return param_manager.tscc_list.tscc_dsc_list[index];
}

int param_read_tscc_hunt_mode_into(void *field_value_buff, size_t *buff_size)
{
    uint8_t tscc_hunt_mode = param_manager.runtime_param->runtime_paras.ctrl_tscc_mode;
    *(uint8_t *)field_value_buff = tscc_hunt_mode;
    return 0;
}

int param_write_tscc_hunt_mode_from(const void *field_value_buff, size_t buff_size)
{
    uint8_t tscc_hunt_mode = *((uint8_t *)field_value_buff);

    if (tscc_hunt_mode > PARAM_TSCC_HUNT_MODE_MAX)
    {
        return -1;
    }

    param_manager.runtime_param->runtime_paras.ctrl_tscc_mode = tscc_hunt_mode;
    ui_controller_reset_protostack();
    return 0;
}

int param_read_selected_tscc_index_into(void *field_value_buff, size_t *buff_size)
{
    uint16_t *selected_tscc_index_ptr = NULL;

    if (param_manager.runtime_param->runtime_paras.work_mode == PARAM_WORK_MODE_DIGITAL_TRUNKING)
    {
        selected_tscc_index_ptr = &param_manager.runtime_param->runtime_paras.ctrl_index[0];
    }
    else if (param_manager.runtime_param->runtime_paras.work_mode == PARAM_WORK_MODE_ANALOG_TRUNKING)
    {
        selected_tscc_index_ptr = &param_manager.runtime_param->runtime_paras.ctrl_index[1];
    }

    if (selected_tscc_index_ptr == NULL)
    {
        return -1;
    }

    *(uint16_t *)field_value_buff = *selected_tscc_index_ptr;

    return 0;
}

int param_write_selected_tscc_index_from(const void *field_value_buff, size_t buff_size)
{
    uint16_t selected_tscc_index = *((uint16_t *)field_value_buff);

    if (UI_HAS_ANY_BITS(selected_tscc_index, 0x8000))
    {
        // 检查tscc_num是否超出12位(即tscc_num的有效范围为0-4095)
        if (UI_HAS_ANY_BITS(selected_tscc_index, 0x7000))
        {
            return -1;
        }

        // 去掉0x8000标志位,
        // uint16_t tscc_num = selected_tscc_index & 0x7FFF;

        //  检查范围是否为241-800
        // if (tscc_num < 241 || tscc_num > 800)
        // {
        //     return -1;
        // }
    }
    else
    {
        if (selected_tscc_index >= param_manager.tscc_list.size)
        {
            return -1;
        }
    }

    if (param_manager.runtime_param->runtime_paras.work_mode == PARAM_WORK_MODE_DIGITAL_TRUNKING)
    {
        param_manager.runtime_param->runtime_paras.ctrl_tscc_mode = PARAM_TSCC_HUNT_MODE_SELECTED_TSCC;
        param_manager.runtime_param->runtime_paras.ctrl_index[0] = selected_tscc_index;
    }
    else if (param_manager.runtime_param->runtime_paras.work_mode == PARAM_WORK_MODE_ANALOG_TRUNKING)
    {
        param_manager.runtime_param->runtime_paras.ctrl_tscc_mode = PARAM_TSCC_HUNT_MODE_SELECTED_TSCC;
        param_manager.runtime_param->runtime_paras.ctrl_index[1] = selected_tscc_index;
    }
    else
    {
        return -1;
    }

    ui_controller_reset_protostack();

    return 0;
}

int param_zone_name_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    uint8_t zone_index = *((uint8_t *)field_value_buff);
    uint8_t *zone_name = param_manager.group_books->grp_ctrl_list[zone_index].name;
    size_t size = strlen((const char *)zone_name);
    return proto_gb2312_array_to_utf8_string(zone_name, size, string_buff, string_buff_size);
}

double param_get_frequence(uint16_t index)
{
    return (param_manager.static_param->stack_pdt.freq_base_para + index * param_manager.static_param->stack_pdt.freq_step_para) / 80.0;
}

int param_read_active_subgroup_into(void *field_value_buff, size_t *buff_size)
{
    uint8_t work_mode = param_get_work_mode();

    if (work_mode == PARAM_WORK_MODE_DIGITAL_TRUNKING)
    {
        uint8_t active_subgroup = param_manager.runtime_param->runtime_paras.active_group_book;
        *(uint8_t *)field_value_buff = active_subgroup;
        return 0;
    }
    else if (work_mode == PARAM_WORK_MODE_POC)
    {
        uint8_t active_subgroup = param_manager.poc_param->param.poc_standby_group_index;
        *(uint8_t *)field_value_buff = active_subgroup;
        return 0;
    }
    else if (ui_utils_is_work_mode_xv(work_mode))
    {
        uint8_t active_subgroup = param_manager.runtime_param->runtime_paras.active_zzwpro_group_book;
        *(uint8_t *)field_value_buff = active_subgroup;
        return 0;
    }

    return -1;
}

int param_write_active_subgroup_from(const void *field_value_buff, size_t buff_size)
{
    uint8_t work_mode = param_get_work_mode();
    uint8_t active_subgroup = *((uint8_t *)field_value_buff);

    if (work_mode == PARAM_WORK_MODE_DIGITAL_TRUNKING || work_mode == PARAM_WORK_MODE_POC)
    {
        param_manager.runtime_param->runtime_paras.active_group_book = active_subgroup;
    }
    else if (ui_utils_is_work_mode_xv(work_mode))
    {
        param_manager.runtime_param->runtime_paras.active_zzwpro_group_book = active_subgroup;
    }
    else
    {
        return -1;
    }

    ui_controller_reboot_device();

    return 0;
}

uint8_t param_get_subgroup_count(void)
{
    uint8_t work_mode = param_get_work_mode();
    static uint8_t subgroup_count_trunking = 0; // 集群模式的群组个数
    static uint8_t subgroup_count_xv = 0;       // XV应急网模式的群组个数
    static bool first_call = true;

    if (first_call)
    {
        for (int i = 0; i < UI_ARR_LEN(param_manager.group_books->grp_trunking); ++i)
        {
            if (param_manager.group_books->grp_trunking[i].name[0] != 0)
            {
                subgroup_count_trunking++;
            }
        }

        for (int i = 0; i < UI_ARR_LEN(param_manager.group_books->grp_zzwpro_trunking); ++i)
        {
            if (param_manager.group_books->grp_zzwpro_trunking[i].name[0] != 0)
            {
                subgroup_count_xv++;
            }
        }

        first_call = false;
    }

    if (work_mode == PARAM_WORK_MODE_DIGITAL_TRUNKING)
    {
        return subgroup_count_trunking;
    }
    else if (work_mode == PARAM_WORK_MODE_POC)
    {
        return 1;
    }
    else if (ui_utils_is_work_mode_xv(work_mode))
    {
        return subgroup_count_xv;
    }
    else
    {
        return 0;
    }
}

int param_subgroup_name_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    uint8_t work_mode = param_get_work_mode();
    uint8_t subgroup_num = *((uint8_t *)field_value_buff);
    uint8_t *subgroup_name = NULL;

    if (work_mode == PARAM_WORK_MODE_DIGITAL_TRUNKING || work_mode == PARAM_WORK_MODE_POC)
    {
        subgroup_name = param_manager.group_books->grp_trunking[subgroup_num].name;
    }
    else if (ui_utils_is_work_mode_xv(work_mode))
    {
        subgroup_name = param_manager.group_books->grp_zzwpro_trunking[subgroup_num].name;
    }
    else
    {
        return -1;
    }

    size_t size = strlen((const char *)subgroup_name);
    return proto_gb2312_array_to_utf8_string(subgroup_name, size, string_buff, string_buff_size);
}

uint8_t param_get_preset_short_message_count(void)
{
    static bool is_first_call = true;
    static uint8_t count = 0;

    if (is_first_call)
    {
        for (; count < MAX_MESSAGE_PRESET_NUMBER; ++count)
        {
            if (param_manager.static_param->msg_preset.msg_content[count][0] == 0)
            {
                break;
            }
        }

        is_first_call = false;
    }

    return count;
}

int param_short_message_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    uint8_t short_message_index = *((uint8_t *)field_value_buff);
    uint8_t short_message_count = param_get_preset_short_message_count();

    if (short_message_index >= short_message_count)
    {
        return -1;
    }

    uint8_t *short_message_buff = param_manager.static_param->msg_preset.msg_content[short_message_index];
    size_t size = strlen((const char *)short_message_buff);
    if (size == 0) return -1;
    return proto_gb2312_array_to_utf8_string(short_message_buff, size, string_buff, string_buff_size);
}

uint8_t param_get_preset_status_message_count(void)
{
    static bool is_first_call = true;
    static uint8_t count = 0;

    if (is_first_call)
    {
        for (; count < MAX_MESSAGE_PRESET_NUMBER; ++count)
        {
            if (param_manager.static_param->msg_status.msg_content[count][0] == 0)
            {
                break;
            }
        }

        is_first_call = false;
    }

    return count;
}

int param_read_status_code_into(void *field_value_buff, size_t *buff_size)
{
    uint8_t status_message_count = param_get_preset_status_message_count();
    uint8_t status_message_index = *((uint8_t *)field_value_buff);

    if (status_message_index >= status_message_count)
    {
        return -1;
    }

    *(uint8_t *)field_value_buff = param_manager.static_param->msg_status.msg_content[status_message_index][0];
    return 0;
}

int param_status_msg_index_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    uint8_t status_message_index = *((uint8_t *)field_value_buff);
    uint8_t status_message_count = param_get_preset_status_message_count();

    if (status_message_index >= status_message_count)
    {
        return -1;
    }

    uint8_t *status_message_buff = param_manager.static_param->msg_status.msg_content[status_message_index];
    size_t size = strlen((const char *)(status_message_buff + 1));
    if (size == 0) return -1;
    return proto_gb2312_array_to_utf8_string(status_message_buff + 1, size, string_buff, string_buff_size);
}

int param_status_code_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    uint8_t status_code = *((uint8_t *)field_value_buff);

    for (uint8_t i = 0; i < MAX_MESSAGE_PRESET_NUMBER; ++i)
    {
        if (param_manager.static_param->msg_status.msg_content[i][0] == status_code)
        {
            size_t size = ui_utils_strnlen((const char *)(param_manager.static_param->msg_status.msg_content[i] + 1), MAX_MESSAGE_PRESET_LENGTH - 1);
            if (size == 0)
            {
                return -1;
            }
            else
            {
                return proto_gb2312_array_to_utf8_string(param_manager.static_param->msg_status.msg_content[i] + 1, size, string_buff, string_buff_size);
            }
        }
    }

    int result = lv_snprintf(string_buff, *string_buff_size, "状态%d", status_code);
    *string_buff_size = result;
    return proto_utf8_string_get_length(string_buff);
}

uint16_t param_get_msg_sent_count(void)
{
    uint16_t msg_sent_count = param_manager.runtime_param->msg_sent_table.message_total;
    return (msg_sent_count > MAX_MESSAGE_SENT_NUMBER) ? MAX_MESSAGE_SENT_NUMBER : msg_sent_count;
}

int param_read_msg_sent_calling_party_id_into(void *field_value_buff, size_t *buff_size)
{
    uint16_t msg_sent_index = *((uint16_t *)field_value_buff);

    if (msg_sent_index >= param_manager.runtime_param->msg_sent_table.message_total)
    {
        return -1;
    }

    *(uint32_t *)field_value_buff = param_manager.runtime_param->msg_sent_table.message[msg_sent_index].id_caller;
    return 0;
}

int param_read_msg_sent_called_party_id_into(void *field_value_buff, size_t *buff_size)
{
    uint16_t msg_sent_index = *((uint16_t *)field_value_buff);

    if (msg_sent_index >= param_manager.runtime_param->msg_sent_table.message_total)
    {
        return -1;
    }

    *(uint32_t *)field_value_buff = param_manager.runtime_param->msg_sent_table.message[msg_sent_index].id_called;
    return 0;
}

int param_read_msg_type_into(void *field_value_buff, size_t *buff_size)
{
    uint16_t msg_sent_index = *((uint16_t *)field_value_buff);

    if (msg_sent_index >= param_manager.runtime_param->msg_sent_table.message_total)
    {
        return -1;
    }

    *(uint8_t *)field_value_buff = param_manager.runtime_param->msg_sent_table.message[msg_sent_index].msg_misc.msg_type;
    return 0;
}

int param_read_msg_sent_content_into(void *field_value_buff, size_t *buff_size)
{
    uint16_t msg_sent_index = *((uint16_t *)field_value_buff);
    if (msg_sent_index >= param_manager.runtime_param->msg_sent_table.message_total)
    {
        return -1;
    }

    uint8_t *msg_sent_buff = param_manager.runtime_param->msg_sent_table.message[msg_sent_index].msg_content;
    uint8_t msg_type = param_manager.runtime_param->msg_sent_table.message[msg_sent_index].msg_misc.msg_type;

    if (msg_type == PARAM_MSG_TYPE_STATUS)
    {
        // 将状态码(数组的首字节)保存在field_value_buff中
        *(uint8_t *)field_value_buff = msg_sent_buff[0];
    }
    else
    {
        // 将消息内容的地址保存在field_value_buff中, 将消息内容的长度保存在buff_size中
        *(uint8_t **)field_value_buff = msg_sent_buff;
        *buff_size = ui_utils_strnlen((const char *)msg_sent_buff, UI_SIZEOF_MEMBER(MESSAGE_SENT_STRUCT, msg_content));
    }

    return msg_type;
}

int param_msg_sent_content_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    uint16_t msg_sent_index = *((uint16_t *)field_value_buff);
    if (msg_sent_index >= param_manager.runtime_param->msg_sent_table.message_total)
    {
        return -1;
    }

    MESSAGE_SENT_STRUCT *msg_sent_struct = param_manager.runtime_param->msg_sent_table.message + msg_sent_index;

    // 若消息类型为状态短消息, 则根据状态码获取状态短消息的内容
    if (msg_sent_struct->msg_misc.msg_type == PARAM_MSG_TYPE_STATUS)
    {
        uint8_t status_code = msg_sent_struct->msg_content[0];
        return param_status_code_to_string(&status_code, 0, string_buff, string_buff_size);
    }
    else
    {
        size_t size = ui_utils_strnlen((const char *)(msg_sent_struct->msg_content), UI_SIZEOF_MEMBER(MESSAGE_SENT_STRUCT, msg_content));

        if (size == 0)
        {
            return -1;
        }
        else
        {
            return proto_gb2312_array_to_utf8_string(msg_sent_struct->msg_content, size, string_buff, string_buff_size);
        }
    }
}

int param_delete_msg_sent(uint16_t msg_sent_index)
{
    uint16_t msg_total = param_manager.runtime_param->msg_sent_table.message_total;
    MESSAGE_SENT_STRUCT *msg_sent_list = param_manager.runtime_param->msg_sent_table.message;
    // 判断索引是否有效
    if (msg_sent_index >= param_manager.runtime_param->msg_sent_table.message_total)
    {
        return -1;
    }

    // 移动数组中后面的元素以填补删除的元素
    for (uint16_t i = msg_sent_index; i < msg_total; ++i)
    {
        lv_memcpy(&msg_sent_list[i], &msg_sent_list[i + 1], sizeof(MESSAGE_SENT_STRUCT));
    }

    // 清空最后一个元素
    lv_memset_00(&msg_sent_list[msg_total - 1], sizeof(MESSAGE_SENT_STRUCT));

    // 更新已发送短信的总数
    param_manager.runtime_param->msg_sent_table.message_total--;

    return 0; // 成功删除
}

int param_delete_all_msg_sent(void)
{
    if (param_manager.runtime_param->msg_sent_table.message_total == 0)
    {
        return -1;
    }
    else
    {
        // 清空所有元素
        for (uint16_t i = 0; i < param_manager.runtime_param->msg_sent_table.message_total; ++i)
        {
            lv_memset_00(&param_manager.runtime_param->msg_sent_table.message[i], sizeof(MESSAGE_SENT_STRUCT));
        }

        param_manager.runtime_param->msg_sent_table.message_total = 0;
        return 0;
    }
}

uint16_t param_get_msg_received_count(void)
{
    uint16_t msg_received_count = param_manager.runtime_param->msg_table.message_total;
    return (msg_received_count > MAX_MESSAGE_NUMBER) ? MAX_MESSAGE_NUMBER : msg_received_count;
}

uint32_t param_get_msg_received_calling_party_id(uint16_t msg_received_index)
{
    uint32_t message_struct_addr = param_manager.runtime_param->msg_table.msg_index[msg_received_index].msg_addr;

    if (message_struct_addr == 0)
    {
        return 0;
    }

    uint32_t calling_party_id_buff = 0;
    uint32_t addr_offset = UI_SIZEOF_MEMBER(MESSAGE_STRUCT, msg_time);
    ui_controller_read_from_spi(message_struct_addr + addr_offset, (void *)&calling_party_id_buff, sizeof(calling_party_id_buff));

    return calling_party_id_buff;
}

uint32_t param_get_msg_received_called_party_id(uint16_t msg_received_index)
{
    uint32_t message_struct_addr = param_manager.runtime_param->msg_table.msg_index[msg_received_index].msg_addr;

    if (message_struct_addr == 0)
    {
        return 0;
    }

    uint32_t called_party_id_buff = 0;
    uint32_t addr_offset = UI_SIZEOF_MEMBER(MESSAGE_STRUCT, msg_time) + UI_SIZEOF_MEMBER(MESSAGE_STRUCT, id_caller);
    ui_controller_read_from_spi(message_struct_addr + addr_offset, (void *)&called_party_id_buff, sizeof(called_party_id_buff));

    return called_party_id_buff;
}

int param_msg_received_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    uint8_t msg_received_index = *((uint8_t *)field_value_buff);
    uint32_t message_struct_addr = param_manager.runtime_param->msg_table.msg_index[msg_received_index].msg_addr;

    if (message_struct_addr == 0)
    {
        return -1; // 无效地址
    }

    // 创建一个用于存储消息内容的缓冲区
    uint8_t msg_content_buff[UI_SIZEOF_MEMBER(MESSAGE_STRUCT, msg_content)];
    uint16_t msg_content_max_len = UI_SIZEOF_MEMBER(MESSAGE_STRUCT, msg_content);

    uint16_t chunk_size = 50; // 每次读取的字节数
    uint16_t bytes_read = 0;  // 已读取的字节数
    uint16_t offset = UI_SIZEOF_MEMBER(MESSAGE_STRUCT, msg_time) + UI_SIZEOF_MEMBER(MESSAGE_STRUCT, id_caller) + UI_SIZEOF_MEMBER(MESSAGE_STRUCT, id_called);

    while (bytes_read < msg_content_max_len)
    {
        // 计算这一次应该从外部Flash读取多少字节
        uint16_t bytes_to_read = (msg_content_max_len - bytes_read) < chunk_size ? (msg_content_max_len - bytes_read) : chunk_size;
        ui_controller_read_from_spi(message_struct_addr + offset + bytes_read, (void *)(msg_content_buff + bytes_read), bytes_to_read);

        // 检查是否到达字符串的结尾
        bool found_null = 0;
        for (uint16_t i = 0; i < bytes_to_read; ++i)
        {
            if (msg_content_buff[bytes_read + i] == '\0')
            {
                found_null = 1;
                bytes_read += i;
                break;
            }
        }

        if (found_null)
        {
            break;
        }

        bytes_read += bytes_to_read;
    }

    // 添加一个终止符
    msg_content_buff[bytes_read] = '\0';
    return proto_gb2312_array_to_utf8_string(msg_content_buff, bytes_read, string_buff, string_buff_size);
}

int param_delete_msg_received(uint16_t msg_received_index)
{
    uint16_t msg_total = param_manager.runtime_param->msg_table.message_total;
    MESSAGE_INDEX_TYPEDEF *msg_received_list = param_manager.runtime_param->msg_table.msg_index;

    // 判断索引是否有效
    if (msg_received_index >= param_manager.runtime_param->msg_table.message_total)
    {
        return -1;
    }

    // 移动数组中后面的元素以填补删除的元素
    for (uint16_t i = msg_received_index; i < msg_total; ++i)
    {
        lv_memcpy(&msg_received_list[i], &msg_received_list[i + 1], sizeof(MESSAGE_INDEX_TYPEDEF));
    }

    // 清空最后一个元素
    lv_memset_00(&msg_received_list[msg_total - 1], sizeof(MESSAGE_INDEX_TYPEDEF));

    // 更新已接收短信的总数
    param_manager.runtime_param->msg_table.message_total--;

    return 0; // 成功删除
}

int param_delete_all_msg_received(void)
{
    if (param_manager.runtime_param->msg_table.message_total == 0)
    {
        return -1;
    }
    else
    {
        // 清空所有元素
        for (uint16_t i = 0; i < param_manager.runtime_param->msg_table.message_total; ++i)
        {
            lv_memset_00(&param_manager.runtime_param->msg_table.msg_index[i], sizeof(MESSAGE_INDEX_TYPEDEF));
        }

        param_manager.runtime_param->msg_table.message_total = 0;
        return 0;
    }
}

int param_read_slot_monitoring_enabled_into(void *field_value_buff, size_t *buff_size)
{
    *((bool *)field_value_buff) = param_manager.slot_monitoring_enabled;
    return 0;
}

int param_write_slot_monitoring_enabled_from(const void *field_value_buff, size_t buff_size)
{
    bool slot_monitoring_enabled = *((bool *)field_value_buff);
    param_manager.slot_monitoring_enabled = slot_monitoring_enabled;

    if (param_manager.slot_monitoring_enabled)
    {
        ui_slot_window_t *slot_window = ui_slot_window_get_instance();
        ui_slot_window_set_user_data(slot_window, param_manager.gui_ia->zzw_sta);
        ui_slot_window_start_service(slot_window);
        ui_page_manager_add_always_on_page_widget(slot_window->container);
    }
    else
    {
        ui_slot_window_t *slot_window = ui_slot_window_get_instance();
        ui_page_manager_remove_always_on_page_widget(slot_window->container);
        ui_slot_window_stop_service(slot_window);
    }

    return 0;
}

void *param_get_dsp_2p_tune(void)
{
    uintptr_t dsp_2p_tune_addr = (uintptr_t)param_manager.gui_ia->g_runtime_inst_xvbase_addr;
    return (void *)dsp_2p_tune_addr;
}

bool param_is_engineer_mode_allowed(void)
{
    return param_manager.runtime_param->runtime_paras.project_mode_permit.prj_permit;
}

void param_set_engineer_mode(bool on)
{
    param_manager.engineer_mode_on = on;
}

bool param_is_engineer_mode_on(void)
{
    return param_manager.engineer_mode_on;
}

int param_read_poc_debug_state_into(void *field_value_buff, size_t *buff_size)
{
    *(bool *)field_value_buff = param_is_poc_debug_mode_on();
    return 0;
}

uint8_t param_get_global_freq_list_size(void)
{
    return param_manager.global_freq_count;
}

uint16_t param_get_global_freq(uint8_t index)
{
    return param_manager.global_freq_list[index];
}

int param_read_latest_stack_step_into(void *field_value_buff, size_t *buff_size)
{
    *(uint32_t *)field_value_buff = param_get_latest_stack_step();
    return 0;
}

uint32_t param_get_latest_stack_step(void)
{
    const STATUS_TO_BT_TYPEDEF *status_frame = param_get_pending_mc_status_frame();
    return status_frame->stk_stage.step;
}

int param_read_latest_stack_vs_into(void *field_value_buff, size_t *buff_size)
{
    *(uint32_t *)field_value_buff = param_get_latest_stack_vs();
    return 0;
}

uint32_t param_get_latest_stack_vs(void)
{
    const STATUS_TO_BT_TYPEDEF *mc_status = param_get_pending_mc_status_frame();
    return mc_status->stk_state.vs;
}

int param_read_latest_stack_cs_into(void *field_value_buff, size_t *buff_size)
{
    *(uint32_t *)field_value_buff = param_get_latest_stack_cs();
    return 0;
}

uint32_t param_get_latest_stack_cs(void)
{
    const STATUS_TO_BT_TYPEDEF *status_frame = param_get_pending_mc_status_frame();
    return status_frame->stk_state.cs;
}

int param_read_latest_stack_xcs_into(void *field_value_buff, size_t *buff_size)
{
    *(uint32_t *)field_value_buff = *(param_manager.gui_ia->zzwpro_stack_xcs);
    return 0;
}

uint32_t param_get_latest_stack_xcs(void)
{
    return *(param_manager.gui_ia->zzwpro_stack_xcs);
}

int param_set_speaker_location(void *field_value_buff)
{
    char *speaker_location = (char *)field_value_buff;
    size_t size = strlen(speaker_location);

    if (size == 0)
    {
        return -1;
    }

    lv_snprintf((char *)param_manager.gui_ia->relative_distance, size, "%s", speaker_location);

    return 0;
}

int param_speaker_location_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    uint8_t *speaker_location = param_manager.gui_ia->relative_distance;
    size_t size = strlen((const char *)speaker_location);

    if (size == 0)
    {
        return -1;
    }

    return proto_gb2312_array_to_utf8_string(speaker_location, size, string_buff, string_buff_size);
}

int param_read_speaker_location_into(void *field_value_buff, size_t *buff_size)
{
    char *speaker_location = (char *)param_manager.gui_ia->relative_distance;
    size_t size = strlen(speaker_location);

    if (size == 0)
    {
        return -1;
    }

    lv_snprintf((char *)field_value_buff, *buff_size, "%s", speaker_location);
    return 0;
}

param_slot_state_t param_get_latest_slot_state(uint8_t slot_index)
{
    const ZZWPRO_STATUS_STRUCT *slot_status = &param_manager.gui_ia->zzw_sta[slot_index];

    // 静态变量, 用于保存上一次的时隙状态
    static param_slot_state_t prev_slot_state[UI_SLOT_NUM_MAX] = {PARAM_SLOT_STATE_IDLE};

    if (slot_status->sta.tx == PARAM_SLOT_STATE_BUSY)
    {
        prev_slot_state[slot_index] = PARAM_SLOT_STATE_TRANSMITTING;
    }
    else if (slot_status->sta.rx == PARAM_SLOT_STATE_BUSY)
    {
        prev_slot_state[slot_index] = PARAM_SLOT_STATE_RECEIVING;
    }
    else if (slot_status->sta.tx == PARAM_SLOT_STATE_IDLE && slot_status->sta.rx == PARAM_SLOT_STATE_IDLE)
    {
        prev_slot_state[slot_index] = PARAM_SLOT_STATE_IDLE;
    }

    return prev_slot_state[slot_index];
}

uint8_t param_get_chan_state(uint8_t chan_state)
{
    uint8_t work_mode = param_get_work_mode();

    if (ui_utils_is_work_mode_adhoc(work_mode))
    {
        if (param_manager.gui_ia->zzwpro_rcv_voice == NULL)
        {
            return PARAM_CHAN_STATE_UNDEFINED;
        }
        else
        {
            if (param_manager.gui_ia->zzwpro_rcv_voice[chan_state] > 0)
            {
                return PARAM_CHAN_STATE_BUSY;
            }
            else
            {
                return PARAM_CHAN_STATE_IDLE;
            }
        }
    }
    else
    {
        return PARAM_CHAN_STATE_UNDEFINED;
    }
}

bool param_has_new_msg(void)
{
    bool has_new_msg = (param_manager.gui_ia->dev_misc_notify.new_msg) & 0x1;
    param_manager.gui_ia->dev_misc_notify.new_msg = 0;
    return has_new_msg;
}

uint32_t param_get_dispatch_id(void)
{
    return param_manager.runtime_param->act_report_conf.id;
}

int param_read_idle_chan_count_into(void *field_value_buff, size_t *buff_size)
{
    *(uint8_t *)field_value_buff = param_get_idle_chan_count();
    return 0;
}

uint8_t param_get_idle_chan_count(void)
{
    uint8_t chan_count = 0;
    uint8_t work_mode = param_get_work_mode();
    uint32_t xcs = param_get_latest_stack_xcs();
    uint32_t vs = param_get_latest_stack_vs();
    int xv_mode = protostack_xcs_to_xv_mode(xcs);
    uint8_t trx_state = protostack_vs_to_trx_state(vs);

    do
    {
        // 若设备当前处于Q模式
        if (work_mode == PARAM_WORK_MODE_ADHOC_Q)
        {
            // 若设备当前处于讲话状态, 则空闲信道数减1
            if (trx_state == PROTOSTACK_TRX_STATE_SPEAKING)
            {
                return 0;
            }
            else if (ui_utils_is_calling(trx_state))
            {
                return 0;
            }
            else
            {
                return 1;
            }
        }
        // 若设备当前处于N模式
        else if (ui_utils_is_work_mode_xv(work_mode))
        {
            // 若设备当前处于讲话状态, 则空闲信道数减1
            if (trx_state == PROTOSTACK_TRX_STATE_SPEAKING)
            {
                return 0;
            }

            if (xv_mode == PROTOSTACK_XV_MODE_V0)
            {
                chan_count = 6;
            }
            else if (xv_mode == PROTOSTACK_XV_MODE_V2)
            {
                chan_count = 3;
            }
            else if (xv_mode == PROTOSTACK_XV_MODE_V3)
            {
                chan_count = 2;
            }
            else if (xv_mode == PROTOSTACK_XV_MODE_V6)
            {
                chan_count = 1;
            }
            else
            {
                chan_count = 6;
            }

            // 计算信道被占用的数量
            uint8_t busy_chan_count = 0;
            for (int i = 0; i < chan_count; i++)
            {
                // 获取信道状态
                uint8_t chan_state = param_get_chan_state(i);

                if (chan_state == PARAM_CHAN_STATE_BUSY)
                {
                    busy_chan_count++;
                }
            }

            // 更新图标维护的空闲信道数
            chan_count -= busy_chan_count;
        }
        // 其余模式
        else
        {
            chan_count = 0;
        }
    } while (0);

    return chan_count;
}

uint8_t param_keycode_to_keyfunc(uint32_t keycode)
{
    uint8_t keyfunc = UI_KEYFUNC_UNDEFINED;
    KEY_FUNC_REMAP_TABLE_TYPEDEF *key_remap_table = &param_manager.static_param->key_remap_table;

    switch (keycode)
    {
    case UI_KEYCODE_KNOB_CLOCKWISE:
    {
        if (key_remap_table->vol_independent != 2)
        {
            keyfunc = UI_KEYFUNC_NEXT; //==0,1
        }
        else
        {
            keyfunc = UI_KEYFUNC_VOL_DOWN; //==2
        }

        break;
    }
    case UI_KEYCODE_KNOB_ANTICLOCKWISE:
    {
        if (key_remap_table->vol_independent != 2)
        {
            keyfunc = UI_KEYFUNC_PREV;
        }
        else
        {
            keyfunc = UI_KEYFUNC_VOL_UP;
        }

        break;
    }
    case UI_KEYCODE_LEFT:
    {
        if (key_remap_table->vol_independent == 1)
        {
            keyfunc = UI_KEYFUNC_VOL_DOWN;
        }
        else
        {
            keyfunc = UI_KEYFUNC_PREV;
        }

        break;
    }
    case UI_KEYCODE_RIGHT:
    {
        if (key_remap_table->vol_independent == 1)
        {
            keyfunc = UI_KEYFUNC_VOL_UP;
        }
        else
        {
            keyfunc = UI_KEYFUNC_NEXT;
        }

        break;
    }
    case UI_KEYCODE_CANCEL:
    {
        uint8_t work_mode = param_get_work_mode();

        if (ui_utils_is_work_mode_adhoc(work_mode))
        {
            if (key_remap_table->zzw_func_return == 0x1C)
            {
                keyfunc = UI_KEYFUNC_3PTT;
            }
            else
            {
                keyfunc = UI_KEYFUNC_CANCEL;
            }
        }
        else
        {
            keyfunc = UI_KEYFUNC_CANCEL;
        }
    }

    default:
        break;
    }

    return keyfunc;
}

bool param_get_switch_state(void)
{
    // 如果设备不支持波段开关功能，则直接返回false，表示波段开关功能关闭
    if (param_is_switch_supported() == false)
    {
        return false;
    }

    return param_manager.misc_runtime_param->param.switch_state;
}

void param_set_switch_state(bool on)
{
    param_manager.misc_runtime_param->param.switch_state = on;

    if (on)
    {
        /**
         * @note 开启"波段开关"时，默认功率为4W。
         */
        param_set_rf_power(PARAM_RF_POWER_4W);
    }
    else
    {
        param_set_hop_mode(PARAM_HOP_MODE_DEFAULT);
        
        /**
         * @note 开启"波段开关"时，我们先将当前的`功率参数`保存到临时变量中，然后调用主控接口以使新的功率生效，
         * 随后将`功率参数`恢复到临时变量中的值。因此，关闭"波段开关"时，我们只需重新调用主控接口即可使
         * 原先的功率参数生效。
         */
        ui_controller_set_rf_power();
    }
}

static void param_restore_rf_power(void *param)
{
    param_manager.runtime_param->runtime_paras.rf_power = param_manager.temp_rf_power;
    param_manager.temp_rf_power = PARAM_RF_POWER_4W;
}

void param_init_switch_state(bool on)
{
    param_manager.misc_runtime_param->param.switch_state = on;

    if (on)
    {
        // 将当前的`功率参数`保存到临时变量中
        param_manager.temp_rf_power = param_manager.runtime_param->runtime_paras.rf_power;

        // 随后设置新的`功率参数`, 并调用主控接口以使新的功率生效,
        param_manager.runtime_param->runtime_paras.rf_power = 1; // K1开机时，若波段开关处于开启状态，则将默认功率设置为4W

        // 通过异步的方式，以恢复原先的功率参数。
        lv_async_call(param_restore_rf_power, NULL);
    }
}

uint32_t param_get_shortcut_function(uint8_t num)
{
    CUSTOM_SHORTCUT_ID_TABLE *shortcut_function_table = &param_manager.static_param->shortcut_id_table;

    if (num > 9)
    {
        return 0;
    }

    return shortcut_function_table->shortcut_bind_id[num];
}

bool param_is_shortcut_function_enabled(uint8_t num)
{
    uint32_t shortcut_function = param_get_shortcut_function(num);

    // 检查最高位是否为1以判断是否启用
    if (shortcut_function & 0x80000000)
    {
        return true;
    }
    else
    {
        return false;
    }
}

uint8_t param_get_shortcut_function_type(uint8_t num)
{
    if (param_is_shortcut_function_enabled(num))
    {
        uint32_t shortcut_function = param_get_shortcut_function(num);

        // 提取32位最高字节的bit3/2
        return PROTO_BF_GET(shortcut_function, 26, 2);
    }
    else
    {
        return 0xFF;
    }
}

uint8_t param_get_shortcut_function_subtype(uint8_t num)
{
    uint32_t shortcut_function = param_get_shortcut_function(num);
    return (shortcut_function >> 16) & 0xFF;
}

bool param_is_menu_item_visible(void *field_value_buff)
{
    ui_menu_item_index_dsc_t *menu_item_index_dsc = (ui_menu_item_index_dsc_t *)field_value_buff;

    if (menu_item_index_dsc->menu_index == UI_MENU_INDEX_WORK_MODE)
    {
        menu_config_t *menu_config = param_manager_find_menu_config(7);

        // 若menu_config为NULL, 则说明该菜单的所有菜单项都可见
        if (menu_config == NULL)
        {
            return true;
        }

        // 若menu_config不为NULL, 则需要检查该菜单的具体配置
        work_mode_menu_config_t *work_mode_menu_config = (work_mode_menu_config_t *)menu_config;

        switch (menu_item_index_dsc->item_index)
        {
        case PARAM_WORK_MODE_DIGITAL_CONVENTIONAL:
            return !work_mode_menu_config->bit.digital_conv;
        case PARAM_WORK_MODE_DIGITAL_TRUNKING:
            return !work_mode_menu_config->bit.digital_trunking;
        case PARAM_WORK_MODE_ANALOG_CONVENTIONAL:
            return !work_mode_menu_config->bit.analog_conv;
        case PARAM_WORK_MODE_ANALOG_TRUNKING:
            return !work_mode_menu_config->bit.analog_trunking;
        case PARAM_WORK_MODE_ADHOC:
            return false;
        case PARAM_WORK_MODE_ADHOC_N:
            return !work_mode_menu_config->bit.n;
        case PARAM_WORK_MODE_ADHOC_V:
            return !work_mode_menu_config->bit.v;
        case PARAM_WORK_MODE_ADHOC_Q:
            return !work_mode_menu_config->bit.q;
        case PARAM_WORK_MODE_ADHOC_V25:
            return !work_mode_menu_config->bit.v_plus;
        case PARAM_WORK_MODE_POC:
            return !work_mode_menu_config->bit.poc;
        default:
            return false;
        }
    }

    return false;
}

bool param_is_icon_visible(uint8_t icon_id)
{
    menu_config_t *menu_config = param_manager_find_menu_config(8);

    // 若menu_config为NULL, 则不生效，所有图标都可见
    if (menu_config == NULL)
    {
        return true;
    }
    else
    {
        misc_config_t *misc_config = (misc_config_t *)menu_config;

        switch (icon_id)
        {
        case UI_ICON_ID_ACCESSORY:
            return !misc_config->bit.accessory_hidden;
        case UI_ICON_ID_BATTERY:
            return !misc_config->bit.battery_hidden;
        case UI_ICON_ID_BLUETOOTH:
            return !misc_config->bit.bluetooth_hidden;
        case UI_ICON_ID_CHAN_NUM:
            return !misc_config->bit.chan_num_hidden;
        case UI_ICON_ID_ENCRYPTION:
            return !misc_config->bit.encryption_hidden;
        case UI_ICON_ID_LEFT_SIGNAL:
            return !misc_config->bit.left_signal_hidden;
        case UI_ICON_ID_MENU:
            return !misc_config->bit.menu_hidden;
        case UI_ICON_ID_MODE:
            return !misc_config->bit.work_mode_hidden;
        case UI_ICON_ID_MSG:
            return !misc_config->bit.msg_hidden;
        case UI_ICON_ID_POSITION:
            return false;
        case UI_ICON_ID_RIGHT_SIGNAL:
            return !misc_config->bit.right_signal_hidden;
        case UI_ICON_ID_SATELLITE:
            return !misc_config->bit.satellite_hidden;
        case UI_ICON_ID_TIME:
            return !misc_config->bit.time_hidden;
        case UI_ICON_ID_TRX_STATE:
            return false;
        case UI_ICON_ID_TX_POWER:
            return !misc_config->bit.rf_power_hidden;
        case UI_ICON_ID_VOLUME:
            return !misc_config->bit.volume_hidden;
        case UI_ICON_ID_CONTACT:
            return !misc_config->bit.contact_hidden;

        default:
            break;
        }

        return true;
    }
}

uint8_t param_get_menu_item_count(void)
{
    return param_manager.menu_item_count;
}

uint8_t param_get_info_item_count(void)
{
    return param_manager.info_item_count;
}

bool param_is_menu_visible(uint8_t menu_index)
{
    switch (menu_index)
    {
    case UI_MENU_INDEX_BRIGHTNESS_SETTINGS:
        return param_manager.brightness_menu_enabled;
    default:
        return false;
    }
}

bool param_is_bd_only(void)
{
    return (param_manager.factory_param->misc_config_factory & MISC_CONFIG_FACTORY_BEIDOU_ONLY);
}

bool param_is_data_port_enabled(void)
{
    return (param_manager.factory_param->misc_config_factory & MISC_CONFIG_FACTORY_DATA_MODULE);
}

uint16_t param_get_rcode(void)
{
    return param_manager.static_param->stack_mpt.rcode;
}

int param_read_calibration_param_into(void *field_value_buff, size_t *buff_size)
{
    memmove(field_value_buff, param_manager.factory_param->p1_calibrator, sizeof(param_manager.factory_param->p1_calibrator));
    return 0;
}

int param_read_mag_calibrate_debug_enabled_into(void *field_value_buff, size_t *buff_size)
{
    *(bool *)field_value_buff = hal_magnetometer_is_debug_enabled(&hal_magnetometer_drv);
    return 0;
}

int param_write_mag_calibrate_debug_enabled_from(const void *field_value_buff, size_t buff_size)
{
    bool enabled = *(bool *)field_value_buff;
    hal_magnetometer_set_debug_enabled(&hal_magnetometer_drv, enabled);
    return 0;
}

int param_read_latest_stack_ctype_into(void *field_value_buff, size_t *buff_size)
{
    const STATUS_TO_BT_TYPEDEF *status_frame = param_get_pending_mc_status_frame();
    *(uint16_t *)field_value_buff = status_frame->stk_stage.ctype;
    return 0;
}

uint8_t param_get_priority(void)
{
    uint8_t work_mode = param_get_work_mode();
    if (work_mode == PARAM_WORK_MODE_DIGITAL_CONVENTIONAL)
    {
        return protostack_mode_to_pl(param_manager.runtime_param->runtime_paras.stack_mode[0]);
    }
    else if (work_mode == PARAM_WORK_MODE_DIGITAL_TRUNKING || work_mode == PARAM_WORK_MODE_POC)
    {
        return protostack_mode_to_pl(param_manager.runtime_param->runtime_paras.stack_mode[1]);
    }
    else if (work_mode == PARAM_WORK_MODE_ANALOG_TRUNKING)
    {
        return protostack_mode_to_pl(param_manager.runtime_param->runtime_paras.stack_mode[2]);
    }
    else
    {
        return 0;
    }
}

uint8_t param_get_ptt_max_time(void)
{
    // 提取协议栈工作常数中的PTT最大时间
    return param_manager.static_param->stack_pdt.conts[7];
}

int param_read_latest_dqi_while_listening_into(void *field_value_buff, size_t *buff_size)
{
    uint8_t work_mode = param_get_work_mode();

    if (ui_utils_is_work_mode_adhoc(work_mode)) // 自组网
    {
        uint32_t vs_current = param_get_latest_stack_vs();

        // 当电台处于放音状态时, 获取接收时隙上的DQI, 并保存在field_value_buff中
        if (protostack_vs_is_listening(vs_current))
        {
            uint8_t listening_slot = param_manager.gui_ia->dev_misc_notify.stack_op_slot;
            uint8_t dqi = param_manager.gui_ia->zzw_sta[listening_slot].dqi;
            *(uint8_t *)field_value_buff = dqi;
        }
        // 当电台处于其他状态时, 将0保存在field_value_buff中
        else
        {
            *(uint8_t *)field_value_buff = 0;
        }
    }
    else // 非自组网模式下的
    {
        for (int i = 0; i < UI_SLOT_NUM_MAX; ++i)
        {
            param_slot_state_t slot_state = param_get_latest_slot_state(i);

            if (slot_state == PARAM_SLOT_STATE_RECEIVING)
            {
                uint8_t dqi = param_manager.gui_ia->zzw_sta[i].dqi;
                *(uint8_t *)field_value_buff = dqi;
                return 0;
            }
        }

        *(uint8_t *)field_value_buff = 0;
    }

    return 0;
}

uint32_t param_get_time_counter(void)
{
    return *(param_manager.gui_ia->timer_10ms);
}

uint32_t param_get_time_elapsed(uint32_t ten_ms)
{
    uint32_t current_time_counter = param_get_time_counter();
    uint32_t time_elapsed;

    if (current_time_counter >= ten_ms)
    {
        time_elapsed = current_time_counter - ten_ms;
    }
    else
    {
        time_elapsed = (UINT32_MAX - ten_ms) + 1 + current_time_counter;
    }

    return time_elapsed * 10;
}

void param_get_current_time(param_rtc_time_t *time)
{
    time->hour = param_manager.gui_ia->rtc_time[0];
    time->minute = param_manager.gui_ia->rtc_time[1];
    time->second = param_manager.gui_ia->rtc_time[2];
}

void param_set_current_time(const param_rtc_time_t *time)
{
    param_manager.gui_ia->rtc_time[0] = time->hour;
    param_manager.gui_ia->rtc_time[1] = time->minute;
    param_manager.gui_ia->rtc_time[2] = time->second;
}

int param_read_pending_mc_status_into(void *field_value_buff, size_t *buff_size)
{
    const STATUS_TO_BT_TYPEDEF *latest_mc_status = param_get_pending_mc_status_frame();
    lv_memcpy(field_value_buff, latest_mc_status, sizeof(STATUS_TO_BT_TYPEDEF));

    return 0;
}

bool param_has_new_mc_status(void)
{
    param_mutex_lock();

    // 若主控状态帧的读写指针不相等, 则说明有新的主控状态帧; 反之, 则没有新的主控状态帧
    uint8_t write_index = param_manager.gui_ia->dev_status.dev_sta_wr;
    uint8_t read_index = param_manager.gui_ia->dev_status.dev_sta_rd;
    bool has_new_status_frame = (write_index != read_index);

    param_mutex_unlock();

    return has_new_status_frame;
}

void param_dequeue_pending_mc_status(void)
{
    // 若主控状态帧的读写指针不相等, 则将读指针加1
    uint8_t write_index = param_manager.gui_ia->dev_status.dev_sta_wr;
    uint8_t read_index = param_manager.gui_ia->dev_status.dev_sta_rd;

    if (write_index != read_index)
    {
        param_manager.gui_ia->dev_status.dev_sta_rd = (read_index + 1) % MC_STATUS_BUFF_SIZE;
    }
}

void param_queue_mc_status(const void *field_value_buff)
{
    // 将主控状态帧保存在环形缓冲区中, 并增加写索引
    uint8_t write_index = (param_manager.gui_ia->dev_status.dev_sta_wr + 1) % MC_STATUS_BUFF_SIZE;
    lv_memcpy(param_manager.gui_ia->dev_status.dev_sta + write_index, field_value_buff, sizeof(STATUS_TO_BT_TYPEDEF));
    param_manager.gui_ia->dev_status.dev_sta_wr = write_index;
}

void param_mc_status_handler(const void *field_value_buff)
{
    const STATUS_TO_BT_TYPEDEF *mc_status = (const STATUS_TO_BT_TYPEDEF *)field_value_buff;

    // 更新卫星状态
    param_update_status_gps(mc_status->rmt_gps_state, mc_status->own_gps_pos[0], mc_status->own_gps_pos[1], mc_status->own_speed);
#if (UI_USE_SIMULATOR == 0) && defined(MODEL_P3_AM6231)
    param_update_status_rmt_gps(mc_status->rmt_gps_pos[0], mc_status->rmt_gps_pos[1]);
#endif

    /***
     * @brief 更新通话状态
     * 
     * @note 当设备处于PoC模式时, 我们主要根据PoC模块推送的状态信息更新设备状态, 而不是根据主控推送的状态信息
     */
    if (param_get_work_mode() == PARAM_WORK_MODE_POC)
    {
        return;
    }

    // 更新通话状态
    uint8_t prev_state = param_manager.dev_state_curr->trx_state;

    // Swap the pointers
    param_device_state_t *temp = param_manager.dev_state_prev;
    param_manager.dev_state_prev = param_manager.dev_state_curr;
    param_manager.dev_state_curr = temp;
    memmove(param_manager.dev_state_curr, param_manager.dev_state_prev, sizeof(param_device_state_t));
    param_device_state_t *dev_state_curr = param_manager.dev_state_curr;

    uint32_t cs = mc_status_to_cs(mc_status);
    uint32_t vs = mc_status_to_vs(mc_status);
    uint32_t ackw = mc_status_to_ackw(mc_status);
    uint32_t step = mc_status_to_step(mc_status);
    uint32_t ctimer = mc_status_to_ctimer(mc_status);

    uint8_t trx_state = protostack_vs_to_trx_state(vs);
    dev_state_curr->trx_state = trx_state;

    /**
     * @brief 检查ctimer的`动态重组`标志位，以确定当前设备是否处于动态重组
     * 状态。
     */
    if (ctimer & VTBUS_TRX_FLAG_DGNA)
    {
        dev_state_curr->is_in_dynamic_group = true;
    }
    else
    {
        dev_state_curr->is_in_dynamic_group = false;
    }

    // 若主控输出了新的"呼叫进程"
    if (mc_status_ackw_is_new_step(ackw))
    {
        // 若呼叫进程与振铃相关
        if (protostack_step_is_ringing(step))
        {
            // 保存振铃相关信息
            dev_state_curr->callee_id = mc_status_to_step_called_party_id(mc_status);
            dev_state_curr->caller_id = mc_status_to_step_calling_party_id(mc_status);
            dev_state_curr->is_incoming_call = protostack_step_is_incoming(step);
            dev_state_curr->calling_party = (dev_state_curr->is_incoming_call) ? false : true;

            if (!protostack_step_is_success(step) && !protostack_step_is_failure(step))
            {
                dev_state_curr->is_ringing = true;
            }
            // 若呼叫进程被标注为"成功"或"失败", 将"振铃"标志位置1, 并且重置"PSTN"状态
            else
            {
                param_manager.pstn = false;
                param_manager.pstn_number[0] = '\0';
                dev_state_curr->is_ringing = false;
            }
        }

        // 若呼叫进程与动态组设置相关, 将"处于动态组"标志位置1
        if (protostack_ctype_is_dgna_assign(mc_status->stk_stage.ctype))
        {
            dev_state_curr->is_in_dynamic_group = true;
            dev_state_curr->dynamic_group_id = mc_status_to_step_calling_party_id(mc_status);
        }
        // 若呼叫进程与动态组取消相关, 将"处于动态组"标志位清零
        else if (protostack_ctype_is_dgna_cancel(mc_status->stk_stage.ctype))
        {
            dev_state_curr->is_in_dynamic_group = false;
            dev_state_curr->dynamic_group_id = 0;
        }
    }

    // 若设备处于空闲状态
    if (trx_state == PROTOSTACK_TRX_STATE_IDLE)
    {
        // 若设备之前处于通话状态, 意味着此时设备刚刚退出通话状态, 则我们需要重置"PSTN"状态
        if (protostack_trx_state_is_calling(prev_state))
        {
            param_manager.pstn = false;
            param_manager.pstn_number[0] = '\0';
        }

        // 重置其他通话状态
        dev_state_curr->call_time = 0;
    }
    // 反之, 若设备处于通话状态
    else
    {
        // 重置"振铃"状态
        dev_state_curr->is_ringing = false;

        // 更新通话相关状态
        dev_state_curr->ig = protostack_vs_to_ig(vs);
        dev_state_curr->all = protostack_vs_is_all_call(vs);
        dev_state_curr->calling_party = protostack_vs_is_calling_party(vs);
        dev_state_curr->emg = protostack_vs_is_emg_call(vs);
        dev_state_curr->bcast = protostack_vs_is_bcast_call(vs);
        dev_state_curr->e2ee = protostack_vs_is_e2ee_call(vs);
        dev_state_curr->call_time = mc_status_to_ctimer(mc_status);
        dev_state_curr->caller_id = mc_status_to_calling_party_id(mc_status);
        dev_state_curr->callee_id = mc_status_to_called_party_id(mc_status);
        dev_state_curr->speaker_id = mc_status_to_speaker_id(mc_status);
        dev_state_curr->is_ambient_listening = protostack_vs_is_ambient_listening(vs);
    }
}

uint16_t *param_get_unicode_to_gb2312_map(void)
{
    return param_manager.gui_ia->unicode2gb;
}

uint16_t *param_get_gb2312_to_unicode_map(void)
{
    return param_manager.gui_ia->gb2unicode;
}

int param_set_hop_mode(uint8_t hop_mode)
{
    // 若未开启波段开关功能, 则返回错误
    if (!param_is_switch_supported())
    {
        return -1;
    }

    // 更新跳数模式参数, 并将新的跳数模式赋值到交互结构体中以通知主控
    if (hop_mode == PARAM_HOP_MODE_V6)
    {
        hop_mode = PROTOSTACK_XV_MODE_V0 | PROTOSTACK_GRP_TYPE_N_RMO;
    }
    else if (hop_mode == PARAM_HOP_MODE_DEFAULT)
    {
        hop_mode = 0xFF;
    }
    else if (hop_mode == PARAM_HOP_MODE_1H6C)
    {
        hop_mode = PROTOSTACK_XV_MODE_V0 | PROTOSTACK_GRP_TYPE_N_DMO;
    }
    else if (hop_mode == PARAM_HOP_MODE_2H3C)
    {
        hop_mode = PROTOSTACK_XV_MODE_V2 | PROTOSTACK_GRP_TYPE_N_DMO;
    }
    else if (hop_mode == PARAM_HOP_MODE_3H2C)
    {
        hop_mode = PROTOSTACK_XV_MODE_V3 | PROTOSTACK_GRP_TYPE_N_DMO;
    }

    ui_controller_set_hop_mode(hop_mode);

    return 0;
}

param_ota_state_t param_get_ota_state(void)
{
    return param_manager.ota_state;
}

void param_set_poc_ota_state(param_ota_state_t ota_state)
{
    param_manager.ota_state = ota_state;
}

uint8_t param_get_ota_progress(void)
{
    return param_manager.ota_progress;
}

void param_set_poc_ota_progress(uint8_t progress)
{
    param_manager.ota_progress = progress;
}

void param_update_status_gps(uint8_t gps_state, float longitude, float latitude, uint8_t speed)
{
    param_manager.gps_state.raw = gps_state;
    param_manager.longitude = longitude;
    param_manager.latitude = latitude;
    param_manager.speed = speed;
}

void param_update_status_rmt_gps(float longitude, float latitude)
{
    param_manager.rmt_longitude = longitude;
    param_manager.rmt_latitude = latitude;
}

float param_get_rmt_gps_longitude(void)
{
    return param_manager.rmt_longitude;
}

float param_get_rmt_gps_latitude(void)
{
    return param_manager.rmt_latitude;
}

uint8_t param_get_gps_state(void)
{
    return param_manager.gps_state.raw;
}

float param_get_longitude(void)
{
    return param_manager.longitude;
}

float param_get_latitude(void)
{
    return param_manager.latitude;
}

uint8_t param_get_speed(void)
{
    return param_manager.speed;
}

void param_set_poc_csq(uint8_t csq)
{
    param_manager.csq = csq;
}

void param_set_poc_reg_status(uint8_t reg_status)
{
    param_manager.reg_status = reg_status;
}

void param_set_poc_standby_status(uint16_t status)
{
    param_manager.poc_standby_status.raw = status;
}

int param_read_poc_username_into(void *field_value_buff, size_t *buff_size)
{
    int ret = lv_snprintf((char *)field_value_buff, *buff_size, "%s", param_manager.poc_param->param.username);

    if (ret < 0)
    {
        return -1;
    }

    *buff_size = ret;
    return 0;
}

char *param_get_poc_username(void)
{
    return param_manager.poc_param->param.username;
}

int param_set_poc_username(const char *username)
{
    if (username == NULL)
    {
        return -1;
    }

    size_t size = strlen(username);

    if (size == 0)
    {
        memset(param_manager.poc_param->param.username, 0, sizeof(param_manager.poc_param->param.username));
    }
    else
    {
        size = size > sizeof(param_manager.poc_param->param.username) ? sizeof(param_manager.poc_param->param.username) : size;
        memmove(param_manager.poc_param->param.username, username, size);
    }

    return 0;
}

int param_read_poc_password_into(void *field_value_buff, size_t *buff_size)
{
    int ret = lv_snprintf((char *)field_value_buff, *buff_size, "%s", param_manager.poc_param->param.password);

    if (ret < 0)
    {
        return -1;
    }

    *buff_size = ret;
    return 0;
}

char *param_get_poc_password(void)
{
    return param_manager.poc_param->param.password;
}

int param_set_poc_password(const char *password)
{
    if (password == NULL)
    {
        return -1;
    }

    size_t size = strlen(password);

    if (size == 0)
    {
        memset(param_manager.poc_param->param.password, 0, sizeof(param_manager.poc_param->param.password));
    }
    else
    {
        size = size > sizeof(param_manager.poc_param->param.password) ? sizeof(param_manager.poc_param->param.password) : size;
        memmove(param_manager.poc_param->param.password, password, size);
    }

    return 0;
}

int param_read_poc_server_addr_into(void *field_value_buff, size_t *buff_size)
{
    int ret = lv_snprintf((char *)field_value_buff, *buff_size, "%s", param_manager.poc_param->param.poc_server_addr);

    if (ret < 0)
    {
        return -1;
    }

    *buff_size = ret;
    return 0;
}

char *param_get_poc_server_addr(void)
{
    return param_manager.poc_param->param.poc_server_addr;
}

int param_set_poc_server_addr(const char *server_addr)
{
    if (server_addr == NULL)
    {
        param_manager.poc_param->param.poc_server_addr[0] = '\0';
        return 0;
    }

    size_t buf_size = sizeof(param_manager.poc_param->param.poc_server_addr);

    // 用 strncpy 拷贝，能保证不会越界，但需要手动补终止符
    strncpy(param_manager.poc_param->param.poc_server_addr, server_addr, buf_size - 1);

    // 手动添加字符串终止符，防止未被覆盖时造成无终止符问题
    param_manager.poc_param->param.poc_server_addr[buf_size - 1] = '\0';

    return 0;
}

uint16_t param_get_poc_server_port(void)
{
    return param_manager.poc_param->param.poc_server_port;
}

void param_set_poc_server_port(uint16_t port)
{
    param_manager.poc_param->param.poc_server_port = port;
}

int param_read_poc_apn_into(void *field_value_buff, size_t *buff_size)
{
    int ret = lv_snprintf((char *)field_value_buff, *buff_size, "%s", param_manager.poc_param->param.apn);

    if (ret < 0)
    {
        return -1;
    }

    *buff_size = ret;
    return 0;
}

char *param_get_poc_apn(void)
{
    return param_manager.poc_param->param.apn;
}

int param_set_poc_apn(const char *apn)
{
    if (apn == NULL)
    {
        return -1;
    }

    size_t size = strlen(apn);

    if (size == 0)
    {
        memset(param_manager.poc_param->param.apn, 0, sizeof(param_manager.poc_param->param.apn));
    }
    else
    {
        size = size > sizeof(param_manager.poc_param->param.apn) ? sizeof(param_manager.poc_param->param.apn) : size;
        memmove(param_manager.poc_param->param.apn, apn, size);
    }

    return 0;
}

uint8_t param_get_poc_auth_type(void)
{
    return param_manager.poc_param->param.auth_type;
}

int param_read_poc_auth_type_into(void *field_value_buff, size_t *buff_size)
{
    *(uint8_t *)field_value_buff = param_manager.poc_param->param.auth_type;
    return 0;
}

int param_set_poc_auth_type(uint8_t auth_type)
{
    param_manager.poc_param->param.auth_type = auth_type;
    return 0;
}

int param_write_poc_auth_type_from(const void *field_value_buff, size_t buff_size)
{
    uint8_t auth_type = *(uint8_t *)field_value_buff;
    return param_set_poc_auth_type(auth_type);
}

uint8_t param_get_poc_activation_type(void)
{
    return param_manager.poc_param->param.activation_type;
}

int param_read_poc_activation_type_into(void *field_value_buff, size_t *buff_size)
{
    *(uint8_t *)field_value_buff = param_manager.poc_param->param.activation_type;
    return 0;
}

int param_set_poc_activation_type(uint8_t activation_type)
{
    param_manager.poc_param->param.activation_type = activation_type;
    return 0;
}

int param_write_poc_activation_type_from(const void *field_value_buff, size_t buff_size)
{
    uint8_t activation_type = *(uint8_t *)field_value_buff;
    return param_set_poc_activation_type(activation_type);
}

void param_set_poc_version(uint8_t major, uint8_t minor, uint8_t patch)
{
    param_manager.poc_major_version = major;
    param_manager.poc_minor_version = minor;
    param_manager.poc_patch_version = patch;
}

int param_poc_version_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size)
{
    uint8_t major = param_manager.poc_major_version;
    uint8_t minor = param_manager.poc_minor_version;
    uint8_t patch = param_manager.poc_patch_version;
    int ret = lv_snprintf((char *)string_buff, *string_buff_size, "%d.%d.%d", major, minor, patch);

    if (ret < 0 || ret >= *string_buff_size)
    {
        return -1;
    }
    else
    {
        *string_buff_size = ret;
        return ret;
    }
}

bool param_is_sim_ready(void)
{
    return param_manager.poc_standby_status.bit.sim_ready;
}

bool param_is_registered_on_server(void)
{
    return param_manager.poc_standby_status.bit.registered; 
}

bool param_is_network_ok(void)
{
    return param_manager.poc_standby_status.bit.network_ok;
}

bool param_is_poc_running(void)
{
    return param_manager.poc_standby_status.bit.running;
}

bool param_is_msvr_conn(void)
{
    return param_manager.poc_standby_status.bit.msvr_conn;
}

bool param_is_poc_service_ok(void)
{
    return param_manager.poc_standby_status.bit.pocsvr_ok;
}

bool param_is_dx_sync(void)
{
    return param_manager.poc_standby_status.bit.dx_sync;
}

uint8_t param_get_reg_status(void)
{
    return param_manager.reg_status;
}

bool param_is_ptt_alarm_on_busy_enable(void)
{
    menu_config_t *menu_config = param_manager_find_menu_config(8);

    // 若menu_config为NULL, 则不生效，信道忙告警提示默认为不启用
    if (menu_config == NULL)
    {
        return false;
    }
    misc_config_t *misc_config = (misc_config_t *)menu_config;
    return misc_config->bit.ptt_alarm_on_busy_en;
}

void param_switch_dev_state(void)
{
    memmove(param_manager.dev_state_prev, param_manager.dev_state_curr, sizeof(param_device_state_t));

    param_device_state_t *temp = param_manager.dev_state_prev;
    param_manager.dev_state_prev = param_manager.dev_state_curr;
    param_manager.dev_state_curr = temp;
}

uint8_t param_get_encryption_type(void)
{
    return param_manager.static_param->misc_static_config.encrypt_type;
}

int param_read_aes_status_into(void *field_value_buff, size_t *buff_size)
{
    *(uint8_t *)field_value_buff = ui_controller_get_aes_status();
    return 0;
}

int param_write_aes_status_from(const void *field_value_buff, size_t buff_size)
{
    uint8_t aes_status = *((uint8_t *)field_value_buff);

    // 若当前加密状态与设置的加密状态相同, 则不做任何操作
    if (aes_status == ui_controller_get_aes_status())
    {
        return 0;
    }

    uint16_t aes_index = ui_controller_get_aes_index();
    ui_controller_set_aes_param(aes_status,aes_index);

    return 0;
}

int param_read_aes_index_into(void *field_value_buff, size_t *buff_size)
{
    *(uint16_t *)field_value_buff = ui_controller_get_aes_index();
    return 0;
}

int param_write_aes_index_from(const void *field_value_buff, size_t buff_size)
{
    uint16_t aes_index = *((uint16_t *)field_value_buff);

    // 若当前秘钥索引与设置的秘钥索引相同, 则不做任何操作
    if (aes_index == ui_controller_get_aes_index())
    {
        return 0;
    }

    uint8_t aes_status = ui_controller_get_aes_status();
    ui_controller_set_aes_param(aes_status, aes_index);

    return 0;
}
