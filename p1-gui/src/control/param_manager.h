#ifndef UI_PARAM_MANAGER_H
#define UI_PARAM_MANAGER_H

// #ifdef __cplusplus
// extern "C"
// {
// #endif

/******************************************************************************

                                Includes

 ******************************************************************************/

//============================> System Headers <============================
#include <stdint.h>
#include <stdlib.h>

//============================> Libraries Headers <============================
#include "lvgl.h" // lvgl.h includes all LVGL related headers
#include "dx.h"

//============================> Project Headers <============================
#include "src/misc/cheji_mobile_setup_data_location_defined.h"
#include "src/misc/ui_master_control_interface.h"
#include "src/misc/vtmobile_poc_param.h"

/******************************************************************************

                                Defines

 ******************************************************************************/

#define INVALID_BAND_RANGE_VALUE (15 * 100 + 15 * 10 + 15)

/******************************************************************************

                                Enums

 ******************************************************************************/

typedef enum
{
    PARAM_RES_OK = 0,
    PARAM_RES_FAIL = -1,
    PARAM_RES_4G_MODULE_OFFLINE = -2,
} param_res_t;

typedef enum
{
    PARAM_TRX_STATE_IDLE,      // 空闲状态
    PARAM_TRX_STATE_CALLING,   // 通话中
    PARAM_TRX_STATE_SPEAKING,  // 本机讲话中
    PARAM_TRX_STATE_LISTENING, // 电台放音中
} param_trx_state_t;

/******************************************************************************

                                Structs

 ******************************************************************************/

typedef struct s_param_contact_list
{
    uint16_t *index_list;           // 联系人索引列表
    uint16_t size;                  // 联系人列表的大小
    uint16_t capacity;              // 联系人列表的容量
} param_contact_list_t;

typedef struct s_param_tscc_list
{
    char name[20];           // 时隙监控列表的名称
    uint32_t *tscc_dsc_list; // 控制信道描述符列表
    uint16_t size;           // 时隙监控列表的大小
    uint16_t capacity;       // 时隙监控列表的容量
} param_tscc_list_t;

typedef struct s_param_zone_list
{
    uint8_t *zone_index_list; // 区域索引列表
    uint16_t size;            // 区域列表的大小
    uint16_t capacity;        // 区域列表的容量
} param_zone_list_t;

typedef struct s_param_device_state
{
    uint8_t trx_state;         // 通话状态
    bool is_ambient_listening; // 是否为环境监听
    bool is_in_dynamic_group;  // 是否在动态组中
    bool is_ringing;           // 是否正在响铃
    bool is_incoming_call;     // 是否为来电
    bool ig;                   // 0: 个呼, 1: 组呼
    bool emg;                  // 是否为紧急呼叫
    bool bcast;                // 是否为广播呼叫
    bool e2ee;                 // 是否为端到端加密呼叫
    bool all;                  // 是否为全呼
    bool calling_party;        // 是否为主叫方
    uint16_t call_time;        // 通话剩余时间(单位: 秒)
    uint32_t caller_id;        // 主叫方ID
    uint32_t callee_id;        // 被叫方ID
    uint32_t speaker_id;       // 讲话人ID
    uint32_t dynamic_group_id; // 动态组ID
} param_device_state_t;

typedef struct s_param_manager
{
    GUI_INTERACTIVE_TYPEDEF *volatile gui_ia;
    GROUP_BOOKS *group_books;
    STATIC_PARAMETERS_TYPEDEF *static_param;
    RUNTIME_PARAMETERS_TYPEDEF *runtime_param;
    RUNTIME_PARAMETERS_XVBASE_TYPEDEF *runtime_xvbase;
    FACTORY_PARAS_TYPEDEF *factory_param;
    MESSAGE_STRUCT *inbox_message_list;

    vtmobile_poc_param_t *poc_param;
    vtmobile_misc_runtime_param_t *misc_runtime_param;

    param_contact_list_t contact_list_ind;    // 单呼联系人列表
    param_contact_list_t contact_list_grp;    // 组呼联系人列表
    param_contact_list_t contact_list_all;    // 所有联系人列表
    param_contact_list_t contact_list_hidden; // 隐含组联系人列表
    param_tscc_list_t tscc_list;              // 控制信道列表
    param_zone_list_t zone_list;              // 集群区域列表

    param_device_state_t dev_states[2];   // 设备状态数组
    param_device_state_t *dev_state_curr; // 当前设备状态指针
    param_device_state_t *dev_state_prev; // 上一个设备状态指针
    bool pstn;                            // 是否为PSTN呼叫
    char pstn_number[20];                 // PSTN号码

    char chan_num_input_buff[10]; // 信道号输入缓冲区
    char dial_num_input_buff[10]; // 拨号输入缓冲区

    bool module_poc_online; // 4G模块是否在线
    uint8_t ota_state;      // OTA升级状态
    uint8_t ota_progress;   // OTA升级进度
    uint8_t csq;            // 4G模块接收场强

    dx_gps_state_t gps_state; // GPS状态
    float longitude;          // 本机经度
    float latitude;           // 本机纬度
    uint8_t speed;            // 速度
#if (UI_USE_SIMULATOR == 0) && defined(MODEL_P3_AM6231)
    float rmt_longitude;    // 远端经度
    float rmt_latitude;     // 远端纬度
#endif

    uint16_t auto_brightness_supported : 1;    // 设备是否支持自动调光功能
    uint16_t brightness_menu_enabled : 1;      // 亮度菜单是否启用
    uint16_t man_down_detection_supported : 1; //  设备是否支持倒地检测
    uint16_t switch_supported : 1;             // 设备是否支持波段开关功能
    uint16_t slot_monitoring_enabled : 1;      // 时隙监控是否启用
    uint8_t menu_item_count;                   // 菜单中的条目的数量
    uint8_t info_item_count;                   // 信息页面中的条目的数量

    bool engineer_mode_on; // 工程模式是否开启

    /**
     * @brief 当开启K1的波段开关功能后, 电台默认的功率为4W, 并且每次用户调节功率
     * 后, 关机后功率参数不会保存。当用户再次开机后, 电台功率参数会恢复为4W。
     * 
     * 为了能够实现该功能, 我们需要在开启该功能后, 将该变量设置为PARAM_RF_POWER_4W。
     * 每当需要读取功率时, 我们将此变量的值输出。每当需要修改功率时, 我们需要修改此
     * 变量为新设置的功率。
     */
    uint8_t temp_rf_power;

    uint8_t temp_sound_gain;                // 临时话音音量
    lv_timer_t *set_sound_gain_async_timer; // 设置话音音量的定时器

    dx_4g_standby_status_t poc_standby_status; // POC待机状态
    uint8_t reg_status;                        // POC注册状态
    uint32_t poc_server_ipv4;                  // POC服务器IPv4地址
    uint8_t poc_major_version;                 // POC主版本号
    uint8_t poc_minor_version;                 // POC次版本号
    uint8_t poc_patch_version;                 // POC修订版本号

    dx_4g_network_info_dsc_t network_info; // 4G网络信息

    char poc_username_input_buff[VTMOBILE_USERNAME_MAX_LEN + 1];   // POC用户名输入缓冲区
    char poc_password_input_buff[VTMOBILE_PASSWORD_MAX_LEN + 1];   // POC密码输入缓冲区
    char poc_server_addr_input_buff[VTMOBILE_SERVER_NAME_LEN + 1]; // POC服务器地址输入缓冲区
    char poc_server_port_input_buff[8];                            // POC服务器端口输入缓冲区
    char poc_apn_input_buff[VTMOBILE_APN_NAME_LEN + 1];            // POC APN输入缓冲区

    uint16_t global_freq_list[10];
    uint8_t global_freq_count;

    uint8_t poc_standby_idx_temp; // 临时POC待机组索引
} param_manager_t;

/******************************************************************************

                                Global Variables

 ******************************************************************************/

/******************************************************************************

                                Public Functions

 ******************************************************************************/

/**
 * @brief 参数管理器的初始化例程
 */
void param_manager_init(void *gui_interface);

void param_mutex_lock(void);
void param_mutex_unlock(void);

void *param_get_runtime_params(void);
void *param_get_static_params(void);
void *param_get_factory_params(void);
void *param_get_gui_ia(void);

char *param_get_chan_num_input_buff(void);
void param_clear_chan_num_input_buff(void);

char *param_get_dial_num_input_buff(void);
void param_clear_dial_num_input_buff(void);

/******************************************************************************

                                守候组相关参数

 ******************************************************************************/

/**
 * @brief 获取当前守候组的信息, 并保存在缓冲'buff'中。
 *
 * @param buff          缓冲
 *
 * @retval  0: 获取当前守候组信息成功
 * @retval -1: 获取当前守候组信息失败
 */
int param_read_current_contact_into(void *buff);
uint32_t param_get_current_standby_group_id(void);

/**
 * @brief 将缓冲区中的守候组信息更新到当前守候组
 */
void param_set_current_standby_group(const void *buff);

/******************************************************************************

                                通讯录相关参数

 ******************************************************************************/

const void *param_get_individual_contact(uint16_t index);
const void *param_get_group_contact(uint16_t index);
const void *param_get_hidden_contact(uint16_t index);

uint16_t param_get_individual_contact_count(void);
uint16_t param_get_group_contact_count(void);
uint16_t param_get_hidden_contact_count(void);

int param_user_book_name_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);
int param_user_book_id_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);

/******************************************************************************

                                功率相关参数

 ******************************************************************************/

enum
{
    PARAM_RF_POWER_1W,
    PARAM_RF_POWER_2W,
    PARAM_RF_POWER_4W,
    PARAM_RF_POWER_8W,
    PARAM_RF_POWER_15W,
    PARAM_RF_POWER_20W,
    PARAM_RF_POWER_25W,
    PARAM_RF_POWER_LOW,
    PARAM_RF_POWER_MEDIUM,
    PARAM_RF_POWER_HIGH,
    PARAM_RF_POWER_AUTO,
    PARAM_RF_POWER_COUNT,
    PARAM_RF_POWER_UNDEFINED,
};

int param_read_rf_power_into(void *field_value_buff, size_t *buff_size);
uint8_t param_get_rf_power(void);
int param_set_rf_power(uint8_t rf_power);
int param_write_rf_power_from(const void *field_value_buff, size_t buff_size);

/******************************************************************************

                               工作模式相关参数

 ******************************************************************************/

enum
{
    PARAM_WORK_MODE_DIGITAL_DIRECT,
    PARAM_WORK_MODE_DIGITAL_CONVENTIONAL,
    PARAM_WORK_MODE_DIGITAL_TRUNKING,
    PARAM_WORK_MODE_ANALOG_TRUNKING,
    PARAM_WORK_MODE_ANALOG_CONVENTIONAL,
    PARAM_WORK_MODE_ADHOC,
    PARAM_WORK_MODE_ADHOC_Q,
    PARAM_WORK_MODE_ADHOC_N,
    PARAM_WORK_MODE_V6 = PARAM_WORK_MODE_ADHOC_N, // 6时隙群集系统
    PARAM_WORK_MODE_ADHOC_V,
    PARAM_WORK_MODE_ADHOC_V25,
    PARAM_WORK_MODE_V4,      // 4时隙群集系统
    PARAM_WORK_MODE_N6_6H1C, // N6单频自组网-6跳1信道
    PARAM_WORK_MODE_N6_3H2C, // N6单频自组网-3跳2信道
    PARAM_WORK_MODE_N6_2H3C, // N6单频自组网-2跳3信道
    PARAM_WORK_MODE_N6_1H6C, // N6单频自组网-1跳6信道
    PARAM_WORK_MODE_N4_4H1C, // N4单频自组网-4跳1信道
    PARAM_WORK_MODE_N4_2H2C, // N4单频自组网-2跳2信道
    PARAM_WORK_MODE_N4_1H4C, // N4单频自组网-1跳4信道
    PARAM_WORK_MODE_QS,      // QS直通全双工
    PARAM_WORK_MODE_H,       // H中继全双工
    PARAM_WORK_MODE_N6,
    PARAM_WORK_MODE_DMT,
    PARAM_WORK_MODE_POC,
    PARAM_WORK_MODE_TATICAL, // 战术对讲
    PARAM_WORK_MODE_NEXT,
    PARAM_WORK_MODE_PREV,
    PARAM_WORK_MODE_UNDEFINED,
    PARAM_WORK_MODE_MAX = PARAM_WORK_MODE_POC,
    PARAM_WORK_MODE_COUNT = PARAM_WORK_MODE_MAX + 1,
};

int param_read_work_mode_into(void *field_value_buff, size_t *buff_size);
int param_write_work_mode_from(const void *field_value_buff, size_t buff_size);
int param_set_work_mode(uint8_t work_mode);
uint8_t param_get_work_mode(void);
int param_init_work_mode(uint8_t work_mode);

/******************************************************************************

                                PDT/DMR制式

 ******************************************************************************/

enum
{
    PARAM_TELECOM_STANDARD_DMR,
    PARAM_TELECOM_STANDARD_PDT,
    PARAM_TELECOM_STANDARD_UNDEFINED,
};
int param_read_telecom_standard_into(void *field_value_buff, size_t *buff_size);
int param_write_telecom_standard_from(const void *field_value_buff, size_t buff_size);

/******************************************************************************

                                语言相关参数

 ******************************************************************************/

enum
{
    PARAM_LANGUAGE_CHINESE,
    PARAM_LANGUAGE_ENGLISH,
    PARAM_LANGUAGE_COUNT,
};

uint8_t param_get_language(void);
int param_read_language_into(void *field_value_buff, size_t *buff_size);
int param_write_language_from(const void *field_value_buff, size_t buff_size);

/******************************************************************************

                                声码器相关参数

 ******************************************************************************/

//============================> 声码器 - 类型 <============================

enum
{
    PARAM_VOCODER_TYPE_AMBE,
    PARAM_VOCODER_TYPE_NVOC,
    PARAM_VOCODER_TYPE_VTEC,
    PARAM_VOCODER_TYPE_UNDEFINED,
};

int param_read_vocoder_type_into(void *field_value_buff, size_t *buff_size);
int param_write_vocoder_type_from(const void *field_value_buff, size_t buff_size);

//============================> 声码器 - 码率 <============================

enum
{
    PARAM_VOCODER_SPEED_2400,
    PARAM_VOCODER_SPEED_2200,
    PARAM_VOCODER_SPEED_1200,
    PARAM_VOCODER_SPEED_600,
    PARAM_VOCODER_SPEED_UNDEFINED,
};

int param_read_vocoder_speed_into(void *field_value_buff, size_t *buff_size);
int param_write_vocoder_speed_from(const void *field_value_buff, size_t buff_size);

//============================> 声码器 - 自动增益 <============================

int param_read_vocoder_agc_into(void *field_value_buff, size_t *buff_size);
int param_write_vocoder_agc_from(const void *field_value_buff, size_t buff_size);

//============================> 声码器 - 噪声抑制 <============================

int param_read_vocoder_ns_into(void *field_value_buff, size_t *buff_size);
int param_write_vocoder_ns_from(const void *field_value_buff, size_t buff_size);

/******************************************************************************

                                音量相关参数

 ******************************************************************************/

//============================> 音量 - 本机提示音 <============================

enum
{
    PARAM_TIP_LVL_0,
    PARAM_TIP_LVL_1,
    PARAM_TIP_LVL_2,
    PARAM_TIP_LVL_3,
    PARAM_TIP_LVL_4,
    PARAM_TIP_LVL_5,
    PARAM_TIP_LVL_6,
    PARAM_TIP_LVL_7,
    PARAM_TIP_LVL_8,
    PARAM_TIP_LVL_9,
    PARAM_TIP_LVL_MUTE = PARAM_TIP_LVL_0,
    PARAM_TIP_LVL_MAX = PARAM_TIP_LVL_9
};

uint8_t param_get_tip_self(void);
int param_read_tip_self_into(void *field_value_buff, size_t *buff_size);
int param_set_tip_self(uint8_t tip_self);
int param_write_tip_self_from(const void *field_value_buff, size_t buff_size);

//============================> 音量 - 话音 <============================

enum
{
    PARAM_SOUND_GAIN_LVL_0,
    PARAM_SOUND_GAIN_LVL_1,
    PARAM_SOUND_GAIN_LVL_2,
    PARAM_SOUND_GAIN_LVL_3,
    PARAM_SOUND_GAIN_LVL_4,
    PARAM_SOUND_GAIN_LVL_5,
    PARAM_SOUND_GAIN_LVL_6,
    PARAM_SOUND_GAIN_LVL_7,
    PARAM_SOUND_GAIN_LVL_8,
    PARAM_SOUND_GAIN_LVL_9,
    PARAM_SOUND_GAIN_MUTE = PARAM_SOUND_GAIN_LVL_0,
    PARAM_SOUND_GAIN_LVL_MAX = PARAM_SOUND_GAIN_LVL_9,
};

int param_read_sound_gain_into(void *field_value_buff, size_t *buff_size);
int param_set_sound_gain(uint8_t sound_gain);
int param_write_sound_gain_from(const void *field_value_buff, size_t buff_size);
uint8_t param_get_sound_gain(void);
uint8_t param_get_nextprev_sound_gain(uint8_t sound_gain, uint8_t nextprev);

/******************************************************************************

                                设备信息相关参数

 ******************************************************************************/

//============================> 设备名称 <============================

int param_device_name_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);

//============================> 设备ID <============================

uint32_t param_get_device_id(void);
int param_device_id_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);

//============================> 设备ESN <============================

typedef enum
{
    PARAM_DEV_TYPE_P1,
    PARAM_DEV_TYPE_P2,
    PARAM_DEV_TYPE_P3,
    PARAM_DEV_TYPE_K1,
    PARAM_DEV_TYPE_K2,
    PARAM_DEV_TYPE_K3,
    PARAM_DEV_TYPE_AK811
} param_device_type_t;

param_device_type_t param_get_device_type(void);
int param_read_device_esn_into(void *field_value_buff, size_t *buff_size);
int param_device_esn_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);

//============================> 设备版本 <============================

int param_read_mc_device_information_into(void *field_value_buff, size_t *buff_size);
int param_device_type_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);
int param_band_range_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);
int param_mcu_info_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);
int param_dsp_info_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);
int param_stack_info_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);
int param_gui_version_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);

/******************************************************************************

                                定位相关参数

 ******************************************************************************/

//============================> 卫星定位 <============================

int param_read_location_service_enabled_into(void *field_value_buff, size_t *buff_size);
bool param_is_location_service_enabled(void);

int param_write_location_service_enabled_from(const void *field_value_buff, size_t buff_size);
void param_set_location_service_enabled(bool enabled);
int param_read_location_service_state_into(void *field_value_buff, size_t *buff_size);

bool param_is_locked_onto_satellite(void);

/**
 * @brief 开启定位系统并启用北斗
 */
int param_write_beidou_enabled_from(const void *field_value_buff, size_t buff_size);

//============================> 定位系统 <============================

enum
{
    PARAM_POSITIONING_SYSTEM_DUAL,
    PARAM_POSITIONING_SYSTEM_GPS,
    PARAM_POSITIONING_SYSTEM_BEIDOU,
    PARAM_POSITIONING_SYSTEM_OFF,
    PARAM_POSITIONING_SYSTEM_UNDEFINED,
    PARAM_POSITIONING_SYSTEM_MAX = PARAM_POSITIONING_SYSTEM_BEIDOU,
    PARAM_POSITIONING_SYSTEM_COUNT = PARAM_POSITIONING_SYSTEM_MAX + 1,
};

int param_read_positioning_system_type_into(void *field_value_buff, size_t *buff_size);
uint8_t param_get_positioning_system_type(void);
int param_write_positioning_system_type_from(const void *field_value_buff, size_t buff_size);

int param_device_longitude_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);
int param_device_latitude_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);
int param_device_time_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);
int param_device_speed_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);
int param_read_satellite_count_into(void *field_value_buff, size_t *buff_size);
int param_satellite_count_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);

//============================> 定位其余相关参数 <============================

int param_read_default_position_into(void *field_value_buff, size_t *buff_size);
bool param_is_bd_only(void);

/******************************************************************************

                                设备状态

 ******************************************************************************/

//============================> 通话状态 <============================

param_device_state_t *param_get_device_state(void);
param_device_state_t *param_get_prev_device_state(void);

uint8_t param_get_trx_state(void);
uint8_t param_get_prev_trx_state(void);
bool param_is_call_incoming(void);
bool param_is_caller(void);
bool param_is_call_group(void);
bool param_is_call_emergency(void);
bool param_is_call_broadcast(void);
bool param_is_call_e2ee(void);
bool param_is_call_all(void);
uint16_t param_get_voice_call_options(void);
bool param_is_call_pstn(void);
bool param_is_in_dynamic_group(void);
bool param_is_in_ambient_listening(void);
uint32_t param_get_dynamic_group_id(void);
int param_read_pstn_number_into(void *field_value_buff, size_t *buff_size);
bool param_is_ringing(void);
uint16_t param_get_call_time(void);
uint32_t param_get_caller_id(void);
uint32_t param_get_callee_id(void);
uint32_t param_get_speaker_id(void);
int param_read_speaker_location_into(void *field_value_buff, size_t *buff_size);
int param_set_speaker_location(void *field_value_buff);
int param_speaker_location_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);
uint8_t param_get_call_type(void);
uint16_t param_get_standby_status(void);

bool param_is_call_failed(void);


/**
 * @brief 获取最新的电台放音时的DQI
 * 
 * @param field_value_buff      保存DQI的缓冲区
 *                              - 放音时, 保存DQI的值
 *                              - 其余时刻, 保存0
 */
int param_read_latest_dqi_while_listening_into(void *field_value_buff, size_t *buff_size);

/**
 * @brief 更新设备的收发状态。
 * 
 * @note 在PoC模式下，该函数用于在接收到PoC状态信息时更新设备的收发状态。
 * 每当设备的收发状态并调用该函数后，我们都需要调用`ui_controller_status_updated_handler`
 * 以更新UI界面。
 */
void param_update_status_trx_state(uint8_t trx_state);
void param_update_status_voice_call_options(bool ig, bool emg, bool bcast, bool e2ee, bool all, bool caller);
void param_update_status_pstn_call_info(bool in_pstn_call, const char *number);
void param_update_status_caller_id(uint32_t caller_id);
void param_update_status_callee_id(uint32_t callee_id);
void param_update_status_speaker_id(uint32_t speaker_id);
void param_update_status_call_time(uint16_t call_time);

//============================> 电量/耳机/POC <============================

uint8_t param_get_battery_level(void);
int param_read_battery_level_into(void *field_value_buff, size_t *buff_size);
void param_set_battery_level(uint8_t battery_level);
bool param_is_earphone_plugged_in(void);
bool param_is_poc_module_online(void);
void param_set_poc_module_online(bool online);

//============================> 网络状态相关 <============================

bool param_is_registered(void);
int param_read_latest_lai_into(void *field_value_buff, size_t *buff_size);
int param_read_latest_chan_into(void *field_value_buff, size_t *buff_size);
int param_read_latest_cc_into(void *field_value_buff, size_t *buff_size);

/**
 * @brief 根据设备的工作模式, 网络状态, 获取用于显示的信号强度
 */
int16_t param_get_rssi(void);
int param_read_latest_rssi_into(void *field_value_buff, size_t *buff_size);
int16_t param_get_latest_rssi(void);
int param_read_latest_rcv_rssi_into(void *field_value_buff, size_t *buff_size);

int16_t param_get_latest_rcv_rssi(void);

int16_t param_get_rssi_from_slot_info(void);

int16_t param_get_latest_poc_rssi(void);
uint8_t param_get_latest_poc_csq(void);
int16_t param_get_latest_poc_network_info_rssi(void);
uint16_t param_get_latest_poc_login_code(void);
int16_t param_get_latest_poc_rsrp(void);
int16_t param_get_latest_poc_rsrq(void);
int16_t param_get_latest_poc_snr(void);

void param_set_poc_network_info(dx_4g_network_info_dsc_t *network_info);

//============================> 空闲信道数 <============================

int param_read_idle_chan_count_into(void *field_value_buff, size_t *buff_size);
uint8_t param_get_idle_chan_count(void);

//============================> 遥晕/遥毙 <============================

enum
{
    PARAM_STUN_STATE_STUN,   // 遥晕
    PARAM_STUN_STATE_REVIVE, // 复活
    PARAM_STUN_STATE_KILL,   // 遥毙
    PARAM_STUN_STATE_UNDEFINED,
};

int param_read_stun_state_into(void *field_value_buff, size_t *buff_size);
uint8_t param_get_stun_state(void);

//============================> 时隙/信道状态 <============================

typedef enum
{
    PARAM_SLOT_STATE_IDLE,
    PARAM_SLOT_STATE_BUSY,
    PARAM_SLOT_STATE_TRANSMITTING,
    PARAM_SLOT_STATE_RECEIVING,
    PARAM_SLOT_STATE_UNDEFINED
} param_slot_state_t;

/**
 * @brief 给定时隙号, 获取该时隙的状态
 */
param_slot_state_t param_get_latest_slot_state(uint8_t slot_index);

enum
{
    PARAM_CHAN_STATE_IDLE,      // 信道空闲
    PARAM_CHAN_STATE_BUSY,      // 信道被占用
    PARAM_CHAN_STATE_UNDEFINED, // 未定义
};

/**
 * @brief 给定信道号, 获取该信道的状态
 */
uint8_t param_get_chan_state(uint8_t chan_state);

//============================> 时间计数器 <============================

/**
 * @brief 获取当前的时间计数器(单位: 10毫秒)
 */
uint32_t param_get_time_counter(void);

/**
 * @brief 给定开始时间, 获取已经过的时间(单位: 毫秒)
 * 
 * @param ten_ms                开始时间(单位: 10毫秒)
 */
uint32_t param_get_time_elapsed(uint32_t ten_ms);

/**
 * @brief 获取
 */
uint32_t param_get_current_tick(void);

typedef struct s_param_rtc_time
{
    uint8_t hour;
    uint8_t minute;
    uint8_t second;
} param_rtc_time_t;

void param_get_current_time(param_rtc_time_t *time);
void param_set_current_time(const param_rtc_time_t *time);

/******************************************************************************

                                设备功能相关参数

 ******************************************************************************/

//============================> 蓝牙/数传/卫星 <============================

int param_read_bluetooth_enabled_into(void *field_value_buff, size_t *buff_size);
bool param_is_bluetooth_enabled(void);
int param_write_bluetooth_enabled_from(const void *field_value_buff, size_t buff_size);
bool param_is_data_port_enabled(void);

//============================> 加密卡检测/加密功能启用 <============================

int param_read_encryption_into(void *field_value_buff, size_t *buff_size);

bool param_is_encryption_enabled(void);
int param_read_encryption_enabled_into(void *field_value_buff, size_t *buff_size);
int param_write_encryption_enabled_from(const void *field_value_buff, size_t buff_size);

bool param_is_encryption_card_detection_enabled(void);
int param_read_encryption_card_detection_enabled_into(void *field_value_buff, size_t *buff_size);
int param_write_encryption_card_detection_enabled_from(const void *field_value_buff, size_t buff_size);
uint8_t param_get_encryption_type(void);
int param_read_aes_status_into(void *field_value_buff, size_t *buff_size);
int param_write_aes_status_from(const void *field_value_buff, size_t buff_size);
int param_read_aes_index_into(void *field_value_buff, size_t *buff_size);
int param_write_aes_index_from(const void *field_value_buff, size_t buff_size);

enum
{
    PARAM_ENCRYPTION_TYPE_MINISTERIAL,
    PARAM_ENCRYPTION_TYPE_FH,
    PARAM_ENCRYPTION_TYPE_AES,
    PARAM_ENCRYPTION_TYPE_UNDEFINED,
};

/******************************************************************************

                                UI相关功能启用/禁用

 ******************************************************************************/

//============================> 自动调光/指南针/倒放 <============================

bool param_is_auto_brightness_supported(void);
bool param_is_compass_supported(void);
bool param_is_man_down_supported(void);

//============================> 按键编程功能 <============================

/**
 * @brief 设备是否支持可编程按键的功能
 * 
 * @note 在P1手台上，当可编程按键功能禁用时，左右键的功能
 */
bool param_is_key_programmable(void);

enum
{
    PARAM_KEY_FUNC_INDEX_VOL_INDEPENDENT,
    PARAM_KEY_FUNC_INDEX_LEFT,
    PARAM_KEY_FUNC_INDEX_RIGHT,
    PARAM_KEY_FUNC_INDEX_ENTER,
    PARAM_KEY_FUNC_INDEX_MIDDLE,
    PARAM_KEY_FUNC_INDEX_PTT,
    PARAM_KEY_FUNC_INDEX_PTT2,
    PARAM_KEY_FUNC_INDEX_RETURN,
    PARAM_KEY_FUNC_INDEX_SHORTCUT,
    PARAM_KEY_FUNC_INDEX_ALARM,
    PARAM_KEY_FUNC_INDEX_ZZW_PTT,
    PARAM_KEY_FUNC_INDEX_ZZW_PTT2,
    PARAM_KEY_FUNC_INDEX_ZZW_RETURN,
};

/**
 * @brief 获取给定的按键功能
 * 
 * @param key_index             按键索引
 * 
 * @retval                      按键功能
 */
uint8_t param_get_key_func(uint8_t key_index);

//============================> 允许选择组群（应急网） <============================

bool param_select_subgroups_enable(void);

/**
 * @brief 设备是否支持波段开关的相关功能
 */
bool param_is_switch_supported(void);

//============================> 屏幕点亮时间 <============================

int param_read_screen_on_period_into(void *field_value_buff, size_t *buff_size);
uint8_t param_get_screen_on_period(void);
int param_write_screen_on_period_from(const void *field_value_buff, size_t buff_size);

//============================> 通话常亮 <============================

bool param_is_screen_always_on_during_call(void);

//============================> 倒置 <============================

enum
{
    PARAM_SCREEN_ROT_NONE,             // 正常
    PARAM_SCREEN_ROT_180,              // 180度(上下倒置)
    PARAM_SCREEN_ROT_90,
    PARAM_SCREEN_ROT_270
};

int param_read_screen_inverted_info(void *field_value_buff, size_t *buff_size);
int param_write_screen_inverted_from(const void *field_value_buff, size_t buff_size);

//============================> 主题 <============================

int param_read_theme_into(void *field_value_buff, size_t *buff_size);
int param_write_theme_from(const void *field_value_buff, size_t buff_size);

//============================> 亮度 <============================

int param_read_brightness_level_into(void *field_value_buff, size_t *buff_size);
int param_write_brightness_level_from(const void *field_value_buff, size_t buff_size);
int param_read_auto_brightness_enabled_into(void *field_value_buff, size_t *buff_size);

//============================> 图标 <============================

/**
 * @brief 给定信号图标ID, 获取其显示模式
 * 
 * @retval                      0: 信号条
 * @retval                      1: 数字
 */
uint8_t param_get_signal_icon_display_mode(uint8_t signal_icon_id);

/**
 * @brief 设置信号图标的显示模式
 * 
 * @param signal_icon_id        信号图标ID
 * @param display_mode          显示模式
 */
void param_set_signal_icon_display_mode(uint8_t signal_icon_id, uint8_t display_mode);

//============================> 菜单配置 <============================

bool param_is_menu_item_visible(void *field_value_buff);
bool param_is_icon_visible(uint8_t icon_id);
uint8_t param_get_menu_item_count(void);
uint8_t param_get_info_item_count(void);
bool param_is_menu_visible(uint8_t menu_index);

/******************************************************************************

                                协议栈相关参数

 ******************************************************************************/

int param_read_latest_stack_step_into(void *field_value_buff, size_t *buff_size);
uint32_t param_get_latest_stack_step(void);
int param_read_latest_stack_vs_into(void *field_value_buff, size_t *buff_size);
uint32_t param_get_latest_stack_vs(void);
int param_read_latest_stack_cs_into(void *field_value_buff, size_t *buff_size);
uint32_t param_get_latest_stack_cs(void);
int param_read_latest_stack_xcs_into(void *field_value_buff, size_t *buff_size);
uint32_t param_get_latest_stack_xcs(void);
int param_read_latest_stack_ctype_into(void *field_value_buff, size_t *buff_size);

uint8_t param_get_priority(void);

/**
 * @brief 获取协议栈电台工作常数-单次PTT最大时间
 */
uint8_t param_get_ptt_max_time(void);

/******************************************************************************

                                用户群组/区域/信道相关参数

 ******************************************************************************/

//============================> 控制信道区域 <============================

int param_read_active_zone_into(void *field_value_buff, size_t *buff_size);
int param_write_active_zone_from(const void *field_value_buff, size_t buff_size);
int param_zone_name_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);
uint16_t param_get_zone_count(void);
int param_get_zone_index(uint8_t zone_num);
uint16_t param_read_active_zone_into_tscc_count(void);
uint32_t param_get_tscc_dsc(uint16_t index);

//============================> 控制信道扫描方式 <============================

enum
{
    PARAM_TSCC_HUNT_MODE_SHORT_SCAN,
    PARAM_TSCC_HUNT_MODE_LONG_SCAN,
    PARAM_TSCC_HUNT_MODE_SELECTED_TSCC,
    PARAM_TSCC_HUNT_MODE_MAX = PARAM_TSCC_HUNT_MODE_SELECTED_TSCC,
    PARAM_TSCC_HUNT_MODE_UNDEFINED,
};

int param_read_tscc_hunt_mode_into(void *field_value_buff, size_t *buff_size);

/**
 * @brief 设置控制信道扫描方式: 
 *        1. 短搜索：置ctrl_tscc_mode为0即可
 *        2. 长搜索：置ctrl_tscc_mode为1即可；
 *        3. 指定：置ctrl_tscc_mode为2，同时：
 *           - 如果是从写入的控制信道列表中选取，则将其在列表中的索引值置到ctrl_index[0]，索引值必须小于信道列表总项数；
 *           - 如果是手工输入，则将（0x8000 | 输入值）置到ctrl_index[0]（输入值范围为241-800）
 */
int param_write_tscc_hunt_mode_from(const void *field_value_buff, size_t buff_size);

//============================> 指定控制信道 <============================

int param_read_selected_tscc_index_into(void *field_value_buff, size_t *buff_size);

/**
 * @brief 设置控制信道扫描方式: 
 *        1. 短搜索：置ctrl_tscc_mode为0即可
 *        2. 长搜索：置ctrl_tscc_mode为1即可；
 *        3. 指定：置ctrl_tscc_mode为2，同时：
 *           - 如果是从写入的控制信道列表中选取，则将其在列表中的索引值置到ctrl_index[0]，索引值必须小于信道列表总项数；
 *           - 如果是手工输入，则将（0x8000 | 输入值）置到ctrl_index[0]（输入值范围为241-800）
 */
int param_write_selected_tscc_index_from(const void *field_value_buff, size_t buff_size);

//============================> 频率 <============================

double param_get_frequence(uint16_t index);

//============================> 用户组群 <============================

int param_read_active_subgroup_into(void *field_value_buff, size_t *buff_size);
int param_write_active_subgroup_from(const void *field_value_buff, size_t buff_size);
uint8_t param_get_subgroup_count(void);
int param_subgroup_name_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);

/******************************************************************************

                                短信相关参数

 ******************************************************************************/

//============================> 预置短信/状态短消息 <============================

uint8_t param_get_preset_short_message_count(void);
int param_short_message_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);
uint8_t param_get_preset_status_message_count(void);
int param_read_status_code_into(void *field_value_buff, size_t *buff_size);

/**
 * @brief 根据预置状态短消息的索引, 获取状态短消息的内容
 * 
 * @param field_value_buff      调用函数时, 保存预置状态短消息的索引
 * @param buff_size             无用
 * @param string_buff           函数返回时, 保存状态短消息的内容
 * @param string_buff_size      函数返回时, 保存状态短消息的内容的大小
 * 
 * @return int                  -1: 获取失败 >=0: UTF8码字的个数
 */
int param_status_msg_index_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);

/**
 * @brief 根据状态码, 获取状态短消息的内容
 * @note 若状态码未保存在预置状态短消息中, 则返回空字符串
 * 
 * @param field_value_buff      调用函数时, 保存状态码
 * @param buff_size             无用
 * @param string_buff           函数返回时, 保存状态短消息的内容
 * @param string_buff_size      函数返回时, 保存状态短消息的内容的大小
 * 
 * @return int                  -1: 获取失败 >=0: UTF8码字的个数
 */
int param_status_code_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);

//============================> 发件箱 <============================

enum
{
    PARAM_MSG_TYPE_REGULAR,            // 普通
    PARAM_MSG_TYPE_STATUS,             // 状态短消息
    PARAM_MSG_TYPE_LONG,               // 长短信
    PARAM_MSG_TYPE_ENCRYPTED,          // 加密
};

uint16_t param_get_msg_sent_count(void);

/**
 * @brief 获取已发送短信的主叫方ID
 * 
 * @param field_value_buff      调用函数时, 保存已发送短信的索引; 函数返回时, 保存主叫方ID
 * @param buff_size             无用
 * @return int                  0: 获取成功; -1: 获取失败
 */
int param_read_msg_sent_calling_party_id_into(void *field_value_buff, size_t *buff_size);

/**
 * @brief 获取已发送短信的被叫方ID
 * 
 * @param field_value_buff      调用函数时, 保存已发送短信的索引; 函数返回时, 保存被叫方ID
 * @param buff_size             无用
 * @return int                  0: 获取成功; -1: 获取失败
 */
int param_read_msg_sent_called_party_id_into(void *field_value_buff, size_t *buff_size);

/**
 * @brief 获取已发送短信的类型
 * 
 * @param field_value_buff      调用函数时, 保存已发送短信的索引; 函数返回时, 保存短信类型
 * @param buff_size             无用
 * @return int                  0: 获取成功; -1: 获取失败
 */
int param_read_msg_type_into(void *field_value_buff, size_t *buff_size);

/**
 * @brief 获取已发送短信内容
 * 
 * @param field_value_buff      调用函数时, 保存已发送短信的索引; 函数返回时, 保存指向短信内容的指针
 * @param buff_size             调用函数时, 保存已发送短信的索引的大小; 函数返回时, 保存短信内容的大小
 * @return int                  -1: 获取失败        0: 普通短信
 *                               1: 状态短信        2: 长短信       3: 加密短信
 */
int param_read_msg_sent_content_into(void *field_value_buff, size_t *buff_size);

/**
 * @brief 将已发送短信的内容转换为字符串
 * 
 * @param field_value_buff      调用函数时, 保存已发送短信的索引
 * @param buff_size             无用
 * @param string_buff           函数返回时, 保存短信内容的字符串
 * @param string_buff_size      函数返回时, 保存短信内容的字符串的大小
 * @return int                  -1: 转换失败 >=0: UTF8码字的个数
 */
int param_msg_sent_content_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);

/**
 * @brief 删除已发送短信
 * 
 * @param msg_sent_index        已发送短信的索引, 有效范围为: [0, (param_get_msg_sent_count() - 1)]
 * @return int                  -1: 失败 0: 成功
 */
int param_delete_msg_sent(uint16_t msg_sent_index);

/**
 * @brief 删除所有已发送短信
 * 
 * @return int                 -1: 失败 0: 成功
 */
int param_delete_all_msg_sent(void);

//============================> 收件箱 <============================

uint16_t param_get_msg_received_count(void);
uint32_t param_get_msg_received_calling_party_id(uint16_t msg_received_index);
uint32_t param_get_msg_received_called_party_id(uint16_t msg_received_index);
int param_msg_received_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);

/**
 * @brief 删除已接收短信
 * 
 * @param msg_received_index    已接收短信的索引, 有效范围为: [0, (param_get_msg_received_count() - 1)]
 * @return int                  -1: 失败 0: 成功
 */
int param_delete_msg_received(uint16_t msg_received_index);

/**
 * @brief 删除所有已接收短信
 * 
 * @return int                 -1: 失败 0: 成功
 */
int param_delete_all_msg_received(void);

/**
 * @brief 获取是否接收到新短信
 */
bool param_has_new_msg(void);

/******************************************************************************

                                工程/调试模式相关

 ******************************************************************************/

int param_read_slot_monitoring_enabled_into(void *field_value_buff, size_t *buff_size);
int param_write_slot_monitoring_enabled_from(const void *field_value_buff, size_t buff_size);
void *param_get_dsp_2p_tune(void);

/**
 * @brief 是否允许打开工程模式
 */
bool param_is_engineer_mode_allowed(void);

/**
 * @brief 设置工程模式开启/关闭
 * 
 * @param on                    true: 打开 false: 关闭
 */
void param_set_engineer_mode(bool on);

/**
 * @brief 是否处于工程模式
 */
bool param_is_engineer_mode_on(void);

/**
 * @brief 设置POC调试模式开启/关闭
 * 
 * @param on                    true: 打开 false: 关闭
 */
void param_set_poc_debug_mode(bool on);

/**
 * @brief 是否处于POC调试模式
 */
bool param_is_poc_debug_mode_on(void);
int param_read_poc_debug_state_into(void *field_value_buff, size_t *buff_size);

int param_call_attempt_count_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);
int param_call_success_count_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);
int param_call_fail_count_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);
int param_ptt_granted_count_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);

/**
 * @brief 获取频率列表的大小
 */
uint8_t param_get_global_freq_list_size(void);

/**
 * @brief 获取指定的全局频率
 */
uint16_t param_get_global_freq(uint8_t index);

/******************************************************************************

                                按键参数相关

 ******************************************************************************/

//============================> 按键相关 <============================

/**
 * @brief 获取调度台键所绑定的调度台ID
 */
uint32_t param_get_dispatch_id(void);

/**
 * @brief 获取按键值所绑定的功能
 */
uint8_t param_keycode_to_keyfunc(uint32_t keycode);

/**
 * @brief 获取波段开关的状态
 */
bool param_get_switch_state(void);

/**
 * @brief 设置波段开关的状态
 * 
 * @note 当波段开关功能开启时, 电台的功率将被设置为4W, 并且在关闭波段开关功能时, 电台的功率将被恢复到开启波段开关功能前的功率。
 * 
 * @param on                    true: 开 false: 关
 */
void param_set_switch_state(bool on);

/**
 * @brief 初始化波段开关的状态，仅在开机时调用
 * 
 * @note 由于在波段开关功能开启的状态时，我们需要将功率默认初始化为4W，并且在关闭波段开关功能时，需要将功率恢复到开启波段开关功能前的功率。
 * 为了实现这一目的，我们需要在UI程序初始化时（UI程序将比主控先启动），将主控维护的运行参数中的`功率`参数初始化为4W，并且将原先的功率值保存起来，
 * 随后在主控进行初始化时，会根据`功率`参数，将功率设置为4W。
 * 
 * 主控完成初始化后，我们又会将`功率`参数恢复到原先的值。这样子，我们就能够实现在波段开关开启时，将功率初始化为4W，同时在波段开关关闭时，将功率恢复到开启波段开关功能前的功率。
 */
void param_init_switch_state(bool on);

/**
 * @brief 获取绑定在数字按键（0–9）上的快捷功能编号
 * 
 * @param  idx                  数字键索引，范围 0–9
 */
uint32_t param_get_shortcut_function(uint8_t num);

/**
 * @brief 检查绑定在数字按键（0–9）上的快捷功能是否可用
 */
bool param_is_shortcut_function_enabled(uint8_t num);

enum
{
    PARAM_SHORTCUT_FUNC_TYPE_CALL = 0x00,       // 呼叫该号码
    PARAM_SHORTCUT_FUNC_TYPE_SWITCH = 0x01,     // 切换组群或守候组
    PARAM_SHORTCUT_FUNC_TYPE_STATUS_MSG = 0x02, // 发送状态短消息（写频软件上标注为"报备"）
};

uint8_t param_get_shortcut_function_type(uint8_t num);

enum
{
    PARAM_SHORTCUT_FUNC_SUBTYPE_SWITCH_SUBGROUP = 0x00,       // 切换组群
    PARAM_SHORTCUT_FUNC_SUBTYPE_SWITCH_STANDBY = 0x01,        // 切换守候组
    PARAM_SHORTCUT_FUNC_SUBTYPE_SWITCH_GROUP_BY_INDEX = 0x02, // 切换号码薄用户
};

uint8_t param_get_shortcut_function_subtype(uint8_t num);

/******************************************************************************

                                主控交互结构体中的其余状态

 ******************************************************************************/

/**
 * @brief 获取待处理的主控状态帧
 * 
 * @note 若缓冲区中存在多个待处理的主控状态帧, 则将读取最早被推入缓冲区队列的主控状态帧。
 * 若缓冲区队列中没有待处理的主控状态帧, 则返回最新的已处理的主控状态帧。
 * 
 * @param field_value_buff      保存主控状态帧的缓冲区
 */
int param_read_pending_mc_status_into(void *field_value_buff, size_t *buff_size);

/**
 * @brief 检查是否有新的主控状态帧
 */
bool param_has_new_mc_status(void);

/**
 * @brief 从缓冲区队列中移除最早被推入的主控状态帧。
 * 
 * @note 此函数用于标记缓冲区队列中最早的主控状态帧为已处理。它更新队列的读索引，
 * 从而移除队列头部的状态帧。如果队列为空（即读索引等于写索引），此函数不执行任何操作，
 * 即重复调用不会导致错误。
 */
void param_dequeue_pending_mc_status(void);

/**
 * @brief 将新的主控状态帧推入缓冲区队列。
 *
 * @details 此函数用于将新的主控状态帧添加到缓冲区队列的尾部。它更新队列的写索引以包含新帧。
 * 如果缓冲区队列已满（即下一个写索引将与读索引重合），则会覆盖最早的帧并更新读索引。
 */
void param_queue_mc_status(const void *field_value_buff);

/**
 * @brief 主控状态帧处理函数, 主要用于保存主控状态帧中的信息
 */
void param_mc_status_handler(const void *field_value_buff);

//============================> 字符编码表 <============================

uint16_t *param_get_unicode_to_gb2312_map(void);
uint16_t *param_get_gb2312_to_unicode_map(void);

//============================> 跳数模式 <============================

enum
{
    PARAM_HOP_MODE_DEFAULT = 0xFF,
    PARAM_HOP_MODE_1H6C = 0x01,
    PARAM_HOP_MODE_2H3C = 0x02,
    PARAM_HOP_MODE_3H2C = 0x03,
    PARAM_HOP_MODE_6H1C = 0x04,
    PARAM_HOP_MODE_4H1C = PARAM_HOP_MODE_6H1C,

    PARAM_HOP_MODE_V6 = 0x0F,
};

/**
 * @brief 设置电台的跳数模式, 当开启K1的波段开关功能后, 电台的跳数模式将由K1的波段开关来控制, 
 * 每当用户旋转K1的波段开关时, UI需要将读取波段开关当前的档位, 共有6个档位, 分别对应: 
 * - 0：单频数字直通（单信道超远距离通信）
 * - 1: 6信道直通集群（单呼、组呼、全呼、强插），即V0
 * - 2: 单频自组网3跳2信道（高功率可持续使用）
 * - 3: PDT自组网3跳1信道中继电台
 * - 4: 模拟直通（开高功率只支持短时间使用）
 * - 5: 群集电台（高架固定自组网基站通信电台）
 * 
 * 当用户切换到: 1, 2, 5 档时, 我们需要将电台的跳数模式设置为对应的值, 然后调用交互接口通知主控。
 * 主控会将此跳数作为一个全局的守候组属性, 配置给协议栈, 而非使用写频参数中的属性。
 * 
 * 当用户切换到: 0, 3, 4 档时, 我们需要将电台的跳数模式设置为0xFF, 然后调用交互接口通知主控。
 * 主控会使用写频参数中的属性。
 * 
 * @param hop_mode              跳数模式, 可设置为以下值:
 * - PARAM_HOP_MODE_DEFAULT: 不启用该参数, 主控将使用写频参数中的属性
 * - PARAM_HOP_MODE_1H6C: 1跳6信道
 * - PARAM_HOP_MODE_2H3C: 2跳3信道
 * - PARAM_HOP_MODE_3H2C: 3跳2信道
 * - PARAM_HOP_MODE_6H1C: 6跳1信道
 * 
 * - PARAM_HOP_MODE_V6: 群集电台模式。在这个模式下, 电台将跟随基站的跳数, 并且在没有收到基站心跳时, 
 *   无法进行呼叫。
 */
int param_set_hop_mode(uint8_t hop_mode);

/******************************************************************************

                                出厂配置

 ******************************************************************************/

//============================> MPT编码方式 <============================

uint16_t param_get_rcode(void);

/******************************************************************************

                                磁力计/加速度计校准参数

 ******************************************************************************/

int param_read_calibration_param_into(void *field_value_buff, size_t *buff_size);

int param_read_mag_calibrate_debug_enabled_into(void *field_value_buff, size_t *buff_size);
int param_write_mag_calibrate_debug_enabled_from(const void *field_value_buff, size_t buff_size);

/******************************************************************************

                                PoC模式相关参数

 ******************************************************************************/

enum
{
    PARAM_OTA_IDLE,             // 空闲
    PARAM_OTA_SYSTEM_UPGRADING, // 系统升级中
    PARAM_OTA_PARAM_UPDATING,   // 空口写频中
};
typedef uint8_t param_ota_state_t;

/**
 * @brief 获取当前的OTA升级状态
 */
param_ota_state_t param_get_ota_state(void);

/**
 * @brief 更新OTA升级状态
 */
void param_set_poc_ota_state(param_ota_state_t ota_state);

/**
 * @brief 获取OTA升级进度
 */
uint8_t param_get_ota_progress(void);

/**
 * @brief 更新OTA升级进度
 */
void param_set_poc_ota_progress(uint8_t progress);

/**
 * @brief 更新GPS状态
 */
void param_update_status_gps(uint8_t gps_state, float longitude, float latitude, uint8_t speed);

/**
 * @brief 更新远端GPS状态
 */
void param_update_status_rmt_gps(float longitude, float latitude);


/**
 * @brief 获取GPS状态
 */
uint8_t param_get_gps_state(void);
float param_get_longitude(void);
float param_get_latitude(void);
uint8_t param_get_speed(void);

/**
 * @brief 获取远端GPS状态
 */
float param_get_rmt_gps_longitude(void);
float param_get_rmt_gps_latitude(void);

/**
 * @brief 更新POC接收场强
 */
void param_set_poc_csq(uint8_t csq);

/**
 * @brief 更新POC注册状态
 */
void param_set_poc_reg_status(uint8_t reg_status);

/**
 * @brief 更新POC待机状态
 */
void param_set_poc_standby_status(uint16_t status);

int param_read_poc_username_into(void *field_value_buff, size_t *buff_size);
char *param_get_poc_username(void);
int param_set_poc_username(const char *username);
int param_read_poc_password_into(void *field_value_buff, size_t *buff_size);
char *param_get_poc_password(void);
int param_set_poc_password(const char *password);

int param_read_poc_server_addr_into(void *field_value_buff, size_t *buff_size);
char *param_get_poc_server_addr(void);
int param_set_poc_server_addr(const char *server_addr);
uint16_t param_get_poc_server_port(void);
void param_set_poc_server_port(uint16_t port);

int param_read_poc_apn_into(void *field_value_buff, size_t *buff_size);
char *param_get_poc_apn(void);
int param_set_poc_apn(const char *apn);
uint8_t param_get_poc_auth_type(void);
int param_read_poc_auth_type_into(void *field_value_buff, size_t *buff_size);
int param_set_poc_auth_type(uint8_t auth_type);
int param_write_poc_auth_type_from(const void *field_value_buff, size_t buff_size);
uint8_t param_get_poc_activation_type(void);
int param_read_poc_activation_type_into(void *field_value_buff, size_t *buff_size);
int param_set_poc_activation_type(uint8_t activation_type);
int param_write_poc_activation_type_from(const void *field_value_buff, size_t buff_size);

void param_set_poc_version(uint8_t major, uint8_t minor, uint8_t patch);
int param_poc_version_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);

bool param_is_sim_ready(void);
bool param_is_registered_on_server(void);
bool param_is_network_ok(void);
bool param_is_poc_running(void);
bool param_is_msvr_conn(void);
bool param_is_poc_service_ok(void);
bool param_is_dx_sync(void);
uint8_t param_get_reg_status(void);

char *param_get_poc_username_input_buff(void);
char *param_get_poc_password_input_buff(void);
char *param_get_poc_server_addr_input_buff(void);
char *param_get_poc_server_port_input_buff(void);
char *param_get_poc_apn_input_buff(void);
void param_set_poc_apn_input_buff(const char *apn);
void param_set_poc_username_input_buff(const char *username);
void param_set_poc_password_input_buff(const char *password);

void param_set_poc_server_ipv4(uint32_t ipv4);
int param_poc_server_ipv4_to_string(const void *field_value_buff, size_t buff_size, void *string_buff, size_t *string_buff_size);

uint8_t param_get_poc_subgroup_index(void);
void param_set_poc_subgroup_index(uint8_t index);

uint8_t param_get_poc_subgroup_index_temp(void);
uint8_t param_set_poc_standby_index_temp(bool inc);

/**
 * @brief 切换至上一个/下一个守候组
 * 
 * @param confirm_switch        1: 若确认切换守候组，则传入参数1
 *                              0: 若不需要切组（回到原始状态），则传入参数0
 *                              0xFF: 在设置跳数后，将参数para[1]置为0xff并调用PFUNC_MAINTAIN_TYPE_SWITCH_WATCH，这时会重新设置一次守候组
 */
void param_switch_poc_group(uint8_t confirm_switch);

/******************************************************************************

                                辅助函数

 ******************************************************************************/

int param_contact_list_add_index(param_contact_list_t *contact_list, uint16_t index);

/**
 * @brief 信道忙告警功能使能
 * 
 * @note 在P1手台上，当信道忙时按下PTT会发出告警；1：开启；0：关闭
 */
bool param_is_ptt_alarm_on_busy_enable(void);

void param_switch_dev_state(void);

// #ifdef __cplusplus
// } /*extern "C"*/
// #endif

#endif
