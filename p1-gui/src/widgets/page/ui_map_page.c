/**
 * @file ui_map_page.c
 * @brief 地图页面实现
 */

#include "ui.h"

/**
 * @brief 地图页面结构体定义
 */
struct s_ui_map_page
{
    ui_page_t page;                     // 页面对象
    HY_MAP hy_set_map;
    lv_obj_t *map_off_btn;
    lv_obj_t *map_tool_btn;
    lv_timer_t *double_click_timer;     // 双击检测定时器
    lv_timer_t *gps_timer;              // GPS定时器
    bool is_button_selection_mode;      // 是否处于按钮选择模式
    uint8_t selected_button_index;      // 当前选中的按钮索引
    lv_timer_t *refresh_timer;          // 刷新定时器
    float    rmt_gps_pos[2];            // 对端设备的经度/纬度
    uint8_t trx_state;
};

// 定义按钮选择模式的按钮索引
#define MAP_BUTTON_TOOL 0
#define MAP_BUTTON_OFF 1
#define MAP_BUTTON_COUNT 2

// 双击检测时间间隔(ms)
#define UI_DOUBLE_CLICK_TIMEOUT 600

// 页面回调函数声明
static void ui_map_page_create_cb(ui_page_t *page);
static void ui_map_page_enter_cb(ui_page_t *page, ui_page_enter_param_t *page_enter_param);
static void ui_map_page_exit_cb(ui_page_t *page, ui_page_exit_param_t *page_exit_param);
static void ui_map_page_destroy_cb(ui_page_t *page);

// 事件处理函数声明
static void ui_map_container_pressed_event_cb(lv_event_t *event);
static void ui_map_container_released_event_cb(lv_event_t *event);
static void ui_map_container_clicked_event_cb(lv_event_t *event);
static void ui_map_container_key_event_cb(lv_event_t *event);
static void ui_map_container_long_pressed_repeat_event_cb(lv_event_t *event);

// 定时器函数声明
static void ui_map_page_poc_refresh_timer_cb(lv_timer_t *timer);
static void ui_listening_state_handler_xv(ui_map_page_t *map_page);
static void ui_calling_state_handler_xv(ui_map_page_t *map_page);


static void onLoaded(lv_event_t * e)
{
    ui_map_page_t *map_page = (ui_map_page_t *)lv_event_get_user_data(e);
    int child_cnt = 0;
    lv_obj_t* child = NULL;
    if(lv_event_get_target(e) != map_page->page.screen){
        return;
    }
    if(lv_event_get_code(e) == LV_EVENT_SCREEN_LOAD_START){
        printf("map_page loaded start\n");
    }else if(lv_event_get_code(e) == LV_EVENT_SCREEN_LOADED) {
        printf("map_page loaded\n");
    }else if(lv_event_get_code(e) == LV_EVENT_SCREEN_UNLOADED){
        printf("map_page unloaded\n");
    }else if(lv_event_get_code(e) == LV_EVENT_SCREEN_UNLOAD_START){
        printf("map_page unloaded start\n");
    }
}

/**
 * @brief 双击检测定时器回调函数
 */
static void ui_double_click_timer_cb(lv_timer_t *timer)
{
    ui_map_page_t *map_page = (ui_map_page_t *)timer->user_data;
    // 定时器超时，退出双击检测状态
    lv_timer_del(map_page->double_click_timer);
    map_page->double_click_timer = NULL;
}

/**
 * @brief 更新按钮选择状态样式
 */
static void ui_update_button_selection(ui_map_page_t *map_page)
{
    if (map_page->is_button_selection_mode) {
        // 根据当前选中的按钮设置样式
        if (map_page->selected_button_index == MAP_BUTTON_TOOL) {
            // 高亮工具按钮
            lv_obj_add_state(map_page->map_tool_btn, LV_STATE_FOCUSED);
            lv_obj_clear_state(map_page->map_off_btn, LV_STATE_FOCUSED);
            lv_event_send(map_page->map_off_btn, LV_EVENT_RELEASED, NULL);
            lv_event_send(map_page->map_tool_btn, LV_EVENT_PRESSED, NULL);
        } else if (map_page->selected_button_index == MAP_BUTTON_OFF) {
            // 高亮复位按钮
            lv_obj_clear_state(map_page->map_tool_btn, LV_STATE_FOCUSED);
            lv_obj_add_state(map_page->map_off_btn, LV_STATE_FOCUSED);
            lv_event_send(map_page->map_tool_btn, LV_EVENT_RELEASED, NULL);
            lv_event_send(map_page->map_off_btn, LV_EVENT_PRESSED, NULL);
        }
    } else {
        // 清除所有按钮的高亮状态
        lv_obj_clear_state(map_page->map_tool_btn, LV_STATE_FOCUSED);
        lv_obj_clear_state(map_page->map_off_btn, LV_STATE_FOCUSED);
        lv_event_send(map_page->map_tool_btn, LV_EVENT_RELEASED, NULL);
        lv_event_send(map_page->map_off_btn, LV_EVENT_RELEASED, NULL);
    }
}

/**
 * @brief 地图页面类定义
 */
const ui_page_class_t ui_map_page_class = {
    .create_cb = ui_map_page_create_cb,
    .enter_cb = ui_map_page_enter_cb,
    .exit_cb = ui_map_page_exit_cb,
    .destroy_cb = ui_map_page_destroy_cb,
    .instance_size = sizeof(ui_map_page_t),
};

/**
 * @brief 构造一个地图页面对象
 * 
 * @return ui_map_page_t* 地图页面实例
 */
static ui_map_page_t *ui_map_page_constructor(void)
{
    ui_page_t *new_page = (ui_page_t *)ui_page_class_create(&ui_map_page_class);
    new_page->page_id = UI_PAGE_ID_MAP;
    new_page->page_flags = UI_PAGE_FLAG_REBUILD;

    ui_map_page_t *new_map_page = (ui_map_page_t *)new_page;
    // 初始化地图页面变量
    new_map_page->double_click_timer = NULL;
    new_map_page->is_button_selection_mode = false;
    new_map_page->selected_button_index = MAP_BUTTON_TOOL;

    return new_map_page;
}

/**
 * @brief 地图页面视图初始化
 * 
 * @param map_page 地图页面
 */
static void ui_map_page_initialize_view(ui_map_page_t *map_page)
{
    // 创建地图屏幕
    lv_obj_t *map_screen = lv_obj_create(NULL);
    lv_obj_clear_flag(map_screen, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_PRESS_LOCK | 
                      LV_OBJ_FLAG_SCROLLABLE | LV_OBJ_FLAG_SCROLL_ELASTIC | LV_OBJ_FLAG_SCROLL_MOMENTUM);
    lv_obj_set_style_bg_color(map_screen, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(map_screen, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    set_wdth_hght(SCREEN_WIDTH,SCREEN_HEIGHT);
	//测试：设置100像素点
	set_pixelspercm(G_PIXEL);

	//申请内存
	Map_Buf_Malloc();

    // lv_obj_add_event_cb(map_screen, onLoaded, LV_EVENT_ALL ,NULL);
    map_page->page.screen = map_screen;
	//创建画布
	lv_obj_t* screen_map_style_canvas_map = lv_canvas_create(map_screen);
	lv_obj_set_pos(screen_map_style_canvas_map, 0, TOP_BAR_HEIGHT);
	lv_obj_set_size(screen_map_style_canvas_map, SCREEN_WIDTH, SCREEN_HEIGHT);
	// 设置纯色不透明背景
	lv_color_t color = lv_color_make(0x00, 0x00, 0x00); // 黑色不透明背景
	lv_canvas_fill_bg(screen_map_style_canvas_map, color, LV_OPA_COVER);
	lv_canvas_set_buffer(screen_map_style_canvas_map,(lv_color_t *)gps_map_info.map_ratio[0].buff, SCREEN_WIDTH, SCREEN_HEIGHT, LV_IMG_CF_TRUE_COLOR);
    map_page->hy_set_map.screen_map_style_canvas_map = screen_map_style_canvas_map;

    lv_obj_t* screen_img_map_cur_point = lv_img_create(map_screen);
	lv_obj_set_pos(screen_img_map_cur_point, (SCREEN_WIDTH/2)-(28/2), (SCREEN_HEIGHT/2)-(28/2) + TOP_BAR_HEIGHT/2);
	lv_obj_set_size(screen_img_map_cur_point, 28, 28);
	lv_obj_set_scrollbar_mode(screen_img_map_cur_point, LV_SCROLLBAR_MODE_OFF);

	//Write style state: LV_STATE_DEFAULT for style_screen_img_map_cur_point_main_main_default
	static lv_style_t style_screen_img_map_cur_point_main_main_default;
	if (style_screen_img_map_cur_point_main_main_default.prop_cnt > 1)
		lv_style_reset(&style_screen_img_map_cur_point_main_main_default);
	else
		lv_style_init(&style_screen_img_map_cur_point_main_main_default);
	lv_style_set_img_recolor(&style_screen_img_map_cur_point_main_main_default, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_img_recolor_opa(&style_screen_img_map_cur_point_main_main_default, 0);
	lv_style_set_img_opa(&style_screen_img_map_cur_point_main_main_default, 255);
	lv_obj_add_style(screen_img_map_cur_point, &style_screen_img_map_cur_point_main_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);
	lv_obj_add_flag(screen_img_map_cur_point, LV_OBJ_FLAG_CLICKABLE);

	lv_img_set_src(screen_img_map_cur_point,"/victel/resource/map_cur_point_red.png");
	lv_img_set_pivot(screen_img_map_cur_point, 0,0);
	lv_img_set_angle(screen_img_map_cur_point, 0);
    for(int i=0; i<MAX_MAP_POINTS; i++){
        map_page->hy_set_map.screen_img_map_cur_point[i] = NULL;
    }
    map_page->hy_set_map.screen_img_map_cur_point[0] = screen_img_map_cur_point;

    lv_obj_t* screen_map_style_cont_scale = lv_obj_create(map_screen);
	lv_obj_set_pos(screen_map_style_cont_scale, ((SCREEN_WIDTH/2)-(100/2)), (SCREEN_HEIGHT-50));
	lv_obj_set_size(screen_map_style_cont_scale, 100, 30);
	lv_obj_set_scrollbar_mode(screen_map_style_cont_scale, LV_SCROLLBAR_MODE_OFF);

	//Write style state: LV_STATE_DEFAULT for style_screen_map_style_cont_scale_main_main_default
	static lv_style_t style_screen_map_style_cont_scale_main_main_default;
	if (style_screen_map_style_cont_scale_main_main_default.prop_cnt > 1)
		lv_style_reset(&style_screen_map_style_cont_scale_main_main_default);
	else
		lv_style_init(&style_screen_map_style_cont_scale_main_main_default);
	lv_style_set_radius(&style_screen_map_style_cont_scale_main_main_default, 0);
	lv_style_set_bg_color(&style_screen_map_style_cont_scale_main_main_default, lv_color_make(0x21, 0x95, 0xf6));
	lv_style_set_bg_grad_color(&style_screen_map_style_cont_scale_main_main_default, lv_color_make(0x21, 0x95, 0xf6));
	lv_style_set_bg_grad_dir(&style_screen_map_style_cont_scale_main_main_default, LV_GRAD_DIR_NONE);
	lv_style_set_bg_opa(&style_screen_map_style_cont_scale_main_main_default, 0);
	lv_style_set_border_color(&style_screen_map_style_cont_scale_main_main_default, lv_color_make(0x21, 0x95, 0xf6));
	lv_style_set_border_width(&style_screen_map_style_cont_scale_main_main_default, 0);
	lv_style_set_border_opa(&style_screen_map_style_cont_scale_main_main_default, 255);
	lv_style_set_pad_left(&style_screen_map_style_cont_scale_main_main_default, 0);
	lv_style_set_pad_right(&style_screen_map_style_cont_scale_main_main_default, 0);
	lv_style_set_pad_top(&style_screen_map_style_cont_scale_main_main_default, 0);
	lv_style_set_pad_bottom(&style_screen_map_style_cont_scale_main_main_default, 0);
	lv_obj_add_style(screen_map_style_cont_scale, &style_screen_map_style_cont_scale_main_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);
    map_page->hy_set_map.screen_map_style_cont_scale = screen_map_style_cont_scale;

    lv_obj_t* screen_map_style_label_scale = lv_label_create(screen_map_style_cont_scale);
	lv_obj_set_pos(screen_map_style_label_scale, 2, 1);
	lv_obj_set_size(screen_map_style_label_scale, 100, 32);
	lv_obj_set_scrollbar_mode(screen_map_style_label_scale, LV_SCROLLBAR_MODE_OFF);
	lv_label_set_text(screen_map_style_label_scale, "0m");
	lv_label_set_long_mode(screen_map_style_label_scale, LV_LABEL_LONG_WRAP);

	//Write style state: LV_STATE_DEFAULT for style_screen_map_style_label_scale_main_main_default
	static lv_style_t style_screen_map_style_label_scale_main_main_default;
	if (style_screen_map_style_label_scale_main_main_default.prop_cnt > 1)
		lv_style_reset(&style_screen_map_style_label_scale_main_main_default);
	else
		lv_style_init(&style_screen_map_style_label_scale_main_main_default);
	lv_style_set_radius(&style_screen_map_style_label_scale_main_main_default, 0);
	lv_style_set_bg_color(&style_screen_map_style_label_scale_main_main_default, lv_color_make(0x21, 0x95, 0xf6));
	lv_style_set_bg_grad_color(&style_screen_map_style_label_scale_main_main_default, lv_color_make(0x21, 0x95, 0xf6));
	lv_style_set_bg_grad_dir(&style_screen_map_style_label_scale_main_main_default, LV_GRAD_DIR_NONE);
	lv_style_set_bg_opa(&style_screen_map_style_label_scale_main_main_default, 0);
	lv_style_set_text_color(&style_screen_map_style_label_scale_main_main_default, lv_color_make(0x00, 0x00, 0x00));
	//lv_style_set_text_font(&style_screen_map_style_label_scale_main_main_default, &lv_font_simsun_15);
	lv_style_set_text_letter_space(&style_screen_map_style_label_scale_main_main_default, 2);
	lv_style_set_text_line_space(&style_screen_map_style_label_scale_main_main_default, 0);
	lv_style_set_text_align(&style_screen_map_style_label_scale_main_main_default, LV_TEXT_ALIGN_CENTER);
	lv_style_set_pad_left(&style_screen_map_style_label_scale_main_main_default, 0);
	lv_style_set_pad_right(&style_screen_map_style_label_scale_main_main_default, 0);
	lv_style_set_pad_top(&style_screen_map_style_label_scale_main_main_default, 0);
	lv_style_set_pad_bottom(&style_screen_map_style_label_scale_main_main_default, 0);
	lv_obj_add_style(screen_map_style_label_scale, &style_screen_map_style_label_scale_main_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);
    map_page->hy_set_map.screen_map_style_label_scale = screen_map_style_label_scale;

    lv_obj_t* screen_map_style_img_scale = lv_img_create(screen_map_style_cont_scale);
	lv_obj_set_pos(screen_map_style_img_scale, 19, 15);
	lv_obj_set_size(screen_map_style_img_scale, 62, 6);
	lv_obj_set_scrollbar_mode(screen_map_style_img_scale, LV_SCROLLBAR_MODE_OFF);

	//Write style state: LV_STATE_DEFAULT for style_screen_map_style_img_scale_main_main_default
	static lv_style_t style_screen_map_style_img_scale_main_main_default;
	if (style_screen_map_style_img_scale_main_main_default.prop_cnt > 1)
		lv_style_reset(&style_screen_map_style_img_scale_main_main_default);
	else
		lv_style_init(&style_screen_map_style_img_scale_main_main_default);
	lv_style_set_img_recolor(&style_screen_map_style_img_scale_main_main_default, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_img_recolor_opa(&style_screen_map_style_img_scale_main_main_default, 0);
	lv_style_set_img_opa(&style_screen_map_style_img_scale_main_main_default, 255);
	lv_obj_add_style(screen_map_style_img_scale, &style_screen_map_style_img_scale_main_main_default, LV_PART_MAIN|LV_STATE_DEFAULT);
	lv_obj_add_flag(screen_map_style_img_scale, LV_OBJ_FLAG_CLICKABLE);
	lv_img_set_src(screen_map_style_img_scale,"/victel/resource/map_scale.png");
	lv_img_set_pivot(screen_map_style_img_scale, 0,0);
	lv_img_set_angle(screen_map_style_img_scale, 0);
    map_page->hy_set_map.screen_map_style_img_scale = screen_map_style_img_scale;

    //创建地图线程
	////printf("map_license_task_create\n");
	map_license_task_create();

	usleep(2000* 1000); //等待地图线程创建完成

	lv_timer_t *gps_timer = lv_timer_create(gps_data_received, 1000, NULL);
    lv_timer_set_repeat_count(gps_timer, -1);
    map_page->gps_timer = gps_timer;

    //set_touch_timer_enabled(false);
	init_map_click_areas(map_screen);

    //Write codes map_tool_btn
    lv_obj_t *map_tool_btn = lv_imgbtn_create(map_screen);
    if(SCREEN_WIDTH == 280)
    {
        lv_obj_set_pos(map_tool_btn, SCREEN_WIDTH-34-40, SCREEN_HEIGHT-210+TOP_BAR_HEIGHT);
        lv_obj_set_size(map_tool_btn, 48, 48);
    }
    if(SCREEN_WIDTH == 800)
    {
        lv_obj_set_pos(map_tool_btn, SCREEN_WIDTH-44-20, SCREEN_HEIGHT-200);
        lv_obj_set_size(map_tool_btn, 44, 44);
    }
    else if(SCREEN_WIDTH == 1280)
    {
        lv_obj_set_pos(map_tool_btn, SCREEN_WIDTH-64-40, SCREEN_HEIGHT-200);
        lv_obj_set_size(map_tool_btn, 64, 64);
    }

    lv_obj_set_scrollbar_mode(map_tool_btn, LV_SCROLLBAR_MODE_OFF);

    //Write style state: LV_STATE_DEFAULT for style_screen_map_tool_btn_default
    static lv_style_t style_screen_map_tool_btn_default;
    if (style_screen_map_tool_btn_default.prop_cnt > 1)
        lv_style_reset(&style_screen_map_tool_btn_default);
    else
        lv_style_init(&style_screen_map_tool_btn_default);
    lv_style_set_text_color(&style_screen_map_tool_btn_default, lv_color_make(0x00, 0x00, 0x00));
    lv_style_set_text_align(&style_screen_map_tool_btn_default, LV_TEXT_ALIGN_CENTER);
    lv_style_set_img_recolor(&style_screen_map_tool_btn_default, lv_color_make(0xff, 0xff, 0xff));
    lv_style_set_img_recolor_opa(&style_screen_map_tool_btn_default, 0);
    lv_style_set_img_opa(&style_screen_map_tool_btn_default, 255);
    lv_obj_add_style(map_tool_btn, &style_screen_map_tool_btn_default, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style state: LV_STATE_PRESSED for style_screen_map_tool_btn_pressed
    static lv_style_t style_screen_map_tool_btn_pressed;
    if (style_screen_map_tool_btn_pressed.prop_cnt > 1)
        lv_style_reset(&style_screen_map_tool_btn_pressed);
    else
        lv_style_init(&style_screen_map_tool_btn_pressed);
    lv_style_set_text_color(&style_screen_map_tool_btn_pressed, lv_color_make(0xFF, 0x33, 0xFF));
    lv_style_set_text_align(&style_screen_map_tool_btn_pressed, LV_TEXT_ALIGN_CENTER);
    lv_style_set_img_recolor(&style_screen_map_tool_btn_pressed, lv_color_make(0x00, 0x00, 0x00));
    lv_style_set_img_recolor_opa(&style_screen_map_tool_btn_pressed, 0);
    lv_style_set_img_opa(&style_screen_map_tool_btn_pressed, 255);
    lv_obj_add_style(map_tool_btn, &style_screen_map_tool_btn_pressed, LV_PART_MAIN|LV_STATE_PRESSED);

    //Write style state: LV_STATE_CHECKED for style_screen_map_tool_btn_checked
    static lv_style_t style_screen_map_tool_btn_checked;
    if (style_screen_map_tool_btn_checked.prop_cnt > 1)
        lv_style_reset(&style_screen_map_tool_btn_checked);
    else
        lv_style_init(&style_screen_map_tool_btn_checked);
    lv_style_set_text_color(&style_screen_map_tool_btn_checked, lv_color_make(0xFF, 0x33, 0xFF));
    lv_style_set_text_align(&style_screen_map_tool_btn_checked, LV_TEXT_ALIGN_CENTER);
    lv_style_set_img_recolor(&style_screen_map_tool_btn_checked, lv_color_make(0x00, 0x00, 0x00));
    lv_style_set_img_recolor_opa(&style_screen_map_tool_btn_checked, 0);
    lv_style_set_img_opa(&style_screen_map_tool_btn_checked, 255);
    lv_obj_add_style(map_tool_btn, &style_screen_map_tool_btn_checked, LV_PART_MAIN|LV_STATE_CHECKED);
    LV_IMG_DECLARE(imgbtn_green);
    LV_IMG_DECLARE(imgbtn_blue);
    lv_imgbtn_set_src(map_tool_btn, LV_IMGBTN_STATE_RELEASED, NULL, "/victel/resource/map_tool_black.png", NULL);
    lv_imgbtn_set_src(map_tool_btn, LV_IMGBTN_STATE_PRESSED, NULL, "/victel/resource/map_tool.png", NULL);
    lv_imgbtn_set_src(map_tool_btn, LV_IMGBTN_STATE_CHECKED_RELEASED, NULL, "/victel/resource/map_tool_black.png", NULL);
    lv_imgbtn_set_src(map_tool_btn, LV_IMGBTN_STATE_CHECKED_PRESSED, NULL, "/victel/resource/map_tool.png", NULL);
    lv_obj_add_flag(map_tool_btn, LV_OBJ_FLAG_CHECKABLE);
    lv_obj_add_event_cb(map_tool_btn, TrackFile_page_event_cb, LV_EVENT_CLICKED, NULL); 
    map_page->map_tool_btn = map_tool_btn;
    
    //Write codes map_off_btn
    lv_obj_t *map_off_btn = lv_imgbtn_create(map_screen);
    if(SCREEN_WIDTH == 280)
    {
        lv_obj_set_pos(map_off_btn, SCREEN_WIDTH-34-40, SCREEN_HEIGHT-120+TOP_BAR_HEIGHT);
        lv_obj_set_size(map_off_btn, 48, 48);
    }
    if(SCREEN_WIDTH == 800)
    {
        lv_obj_set_pos(map_off_btn, SCREEN_WIDTH-44-20, SCREEN_HEIGHT-300);
        lv_obj_set_size(map_off_btn, 44, 44);
    }
    else if(SCREEN_WIDTH == 1280)
    {
        lv_obj_set_pos(map_off_btn, SCREEN_WIDTH-64-40, SCREEN_HEIGHT-400);
        lv_obj_set_size(map_off_btn, 64, 64);
    }
  
    lv_obj_set_scrollbar_mode(map_off_btn, LV_SCROLLBAR_MODE_OFF);

    //Write style state: LV_STATE_DEFAULT for style_screen_map_off_btn_default
    static lv_style_t style_screen_map_off_btn_default;
    if (style_screen_map_off_btn_default.prop_cnt > 1)
        lv_style_reset(&style_screen_map_off_btn_default);
    else
        lv_style_init(&style_screen_map_off_btn_default);
    lv_style_set_text_color(&style_screen_map_off_btn_default, lv_color_make(0x00, 0x00, 0x00));
    lv_style_set_text_align(&style_screen_map_off_btn_default, LV_TEXT_ALIGN_CENTER);
    lv_style_set_img_recolor(&style_screen_map_off_btn_default, lv_color_make(0xff, 0xff, 0xff));
    lv_style_set_img_recolor_opa(&style_screen_map_off_btn_default, 0);
    lv_style_set_img_opa(&style_screen_map_off_btn_default, 255);
    lv_obj_add_style(map_off_btn, &style_screen_map_off_btn_default, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style state: LV_STATE_PRESSED for style_screen_map_off_btn_pressed
    static lv_style_t style_screen_map_off_btn_pressed;
    if (style_screen_map_off_btn_pressed.prop_cnt > 1)
        lv_style_reset(&style_screen_map_off_btn_pressed);
    else
        lv_style_init(&style_screen_map_off_btn_pressed);
    lv_style_set_text_color(&style_screen_map_off_btn_pressed, lv_color_make(0xFF, 0x33, 0xFF));
    lv_style_set_text_align(&style_screen_map_off_btn_pressed, LV_TEXT_ALIGN_CENTER);
    lv_style_set_img_recolor(&style_screen_map_off_btn_pressed, lv_color_make(0x00, 0x00, 0x00));
    lv_style_set_img_recolor_opa(&style_screen_map_off_btn_pressed, 0);
    lv_style_set_img_opa(&style_screen_map_off_btn_pressed, 255);
    lv_obj_add_style(map_off_btn, &style_screen_map_off_btn_pressed, LV_PART_MAIN|LV_STATE_PRESSED);

    //Write style state: LV_STATE_CHECKED for style_screen_map_off_btn_checked
    static lv_style_t style_screen_map_off_btn_checked;
    if (style_screen_map_off_btn_checked.prop_cnt > 1)
        lv_style_reset(&style_screen_map_off_btn_checked);
    else
        lv_style_init(&style_screen_map_off_btn_checked);
    lv_style_set_text_color(&style_screen_map_off_btn_checked, lv_color_make(0xFF, 0x33, 0xFF));
    lv_style_set_text_align(&style_screen_map_off_btn_checked, LV_TEXT_ALIGN_CENTER);
    lv_style_set_img_recolor(&style_screen_map_off_btn_checked, lv_color_make(0x00, 0x00, 0x00));
    lv_style_set_img_recolor_opa(&style_screen_map_off_btn_checked, 0);
    lv_style_set_img_opa(&style_screen_map_off_btn_checked, 255);
    lv_obj_add_style(map_off_btn, &style_screen_map_off_btn_checked, LV_PART_MAIN|LV_STATE_CHECKED);
    lv_imgbtn_set_src(map_off_btn, LV_IMGBTN_STATE_RELEASED, NULL, "/victel/resource/map_off_black.png", NULL);
    lv_imgbtn_set_src(map_off_btn, LV_IMGBTN_STATE_PRESSED, NULL, "/victel/resource/map_off_white.png", NULL);
    lv_imgbtn_set_src(map_off_btn, LV_IMGBTN_STATE_CHECKED_RELEASED, NULL, "/victel/resource/map_off_black.png", NULL);
    lv_imgbtn_set_src(map_off_btn, LV_IMGBTN_STATE_CHECKED_PRESSED, NULL, "/victel/resource/map_off_white.png", NULL);
    lv_obj_add_flag(map_off_btn, LV_OBJ_FLAG_CHECKABLE);
    lv_obj_add_event_cb(map_off_btn, reset_event_cb, LV_EVENT_CLICKED, NULL); 
    map_page->map_off_btn = map_off_btn;

    // 创建定时器
    uint8_t work_mode = param_get_work_mode();
    printf("%s:work_mode=%d\n",__FUNCTION__, work_mode);

    // if (work_mode == PARAM_WORK_MODE_POC)
    // {
        map_page->refresh_timer = lv_timer_create(ui_map_page_poc_refresh_timer_cb, 500, map_page);
    // }

    // 初始化聚焦组
    lv_group_add_obj(map_page->page.group, map_page->page.screen);

    // 配置事件处理器
    lv_obj_add_event_cb(map_page->page.screen, ui_map_container_clicked_event_cb, LV_EVENT_KEY_CLICKED, map_page);
    lv_obj_add_event_cb(map_page->page.screen, ui_map_container_pressed_event_cb, LV_EVENT_KEY_PRESSED, map_page);
    lv_obj_add_event_cb(map_page->page.screen, ui_map_container_released_event_cb, LV_EVENT_KEY_RELEASED, map_page);
    lv_obj_add_event_cb(map_page->page.screen, ui_map_container_long_pressed_repeat_event_cb, LV_EVENT_KEY_LONG_PRESSED_REPEAT, map_page);
    lv_obj_add_event_cb(map_page->page.screen, ui_map_container_key_event_cb, LV_EVENT_KEY, map_page);
}

/**
 * @brief 地图容器按下事件回调
 */
static void ui_map_container_pressed_event_cb(lv_event_t *event)
{
    ui_map_page_t *map_page = (ui_map_page_t *)lv_event_get_user_data(event);
    uint32_t input_keycode = *((uint32_t *)lv_event_get_param(event));

    if (input_keycode == UI_KEYCODE_PTT || input_keycode == UI_KEYCODE_PTT_2 || input_keycode == UI_KEYCODE_PTT_3)
    {
        ui_ptt_event_t ptt_event;
        ui_utils_ptt_event_init(&ptt_event, ui_utils_keycode_to_keyid(input_keycode), UI_KEY_PRESSED, UI_PTT_EVENT_TYPE_DEFAULT, NULL);
        ui_controller_notify_key_event(&ptt_event.key_event);
    }
    else if (input_keycode == UI_KEYCODE_ALARM)
    {
        ui_key_event_t key_event;
        ui_utils_key_event_init(&key_event, ui_utils_keycode_to_keyid(input_keycode), UI_KEY_PRESSED);
        ui_controller_notify_key_event(&key_event);
    }
    else if (input_keycode == UI_KEYCODE_CALL_DISPATCH)
    {
        // 获取调度台ID, 并转换为调度台号码
        uint32_t dispatch_id = param_get_dispatch_id();
        char dispatch_number[20];
        bool ig = ui_utils_id_to_ig(dispatch_id);
        ui_controller_convert_fleet_id_to_num(dispatch_id, ig, dispatch_number, false);

        // 构造一个PTT事件结构体
        ui_ptt_event_t ptt_event;
        ui_utils_ptt_event_init(&ptt_event, ui_utils_keycode_to_keyid(UI_KEYCODE_PTT), UI_KEY_PRESSED, UI_PTT_EVENT_TYPE_DIALED_NUMBER, dispatch_number);
        ui_controller_notify_key_event(&ptt_event.key_event);
    }

    // 当发生"KEY_PRESSED"事件时, 隐藏消息弹窗
    ui_toast_hide(ui_toast_get_instance());
}

/**
 * @brief 地图容器释放事件回调
 */
static void ui_map_container_released_event_cb(lv_event_t *event)
{
    ui_map_page_t *map_page = (ui_map_page_t *)lv_event_get_user_data(event);
    uint32_t input_keycode = *((uint32_t *)lv_event_get_param(event));

    if (input_keycode == UI_KEYCODE_PTT || input_keycode == UI_KEYCODE_PTT_2 || input_keycode == UI_KEYCODE_PTT_3)
    {
        ui_ptt_event_t ptt_event;
        ui_utils_ptt_event_init(&ptt_event, ui_utils_keycode_to_keyid(input_keycode), UI_KEY_RELEASED, UI_PTT_EVENT_TYPE_DEFAULT, NULL);
        ui_controller_notify_key_event(&ptt_event.key_event);
    }
    else if (input_keycode == UI_KEYCODE_CALL_DISPATCH)
    {
        ui_ptt_event_t ptt_event;
        ui_utils_ptt_event_init(&ptt_event, ui_utils_keycode_to_keyid(UI_KEYCODE_PTT), UI_KEY_RELEASED, UI_PTT_EVENT_TYPE_DIALED_NUMBER, NULL);
        ui_controller_notify_key_event(&ptt_event.key_event);
    }
    else if (input_keycode == UI_KEYCODE_ALARM)
    {
        ui_key_event_t key_event;
        ui_utils_key_event_init(&key_event, ui_utils_keycode_to_keyid(input_keycode), UI_KEY_RELEASED);
        ui_controller_notify_key_event(&key_event);
    }
}

/**
 * @brief 地图容器点击事件回调
 */
static void ui_map_container_clicked_event_cb(lv_event_t *event)
{
    ui_map_page_t *map_page = (ui_map_page_t *)lv_event_get_user_data(event);
    uint32_t input_keycode = *(uint32_t *)lv_event_get_param(event);
    
    if (input_keycode == UI_KEYCODE_RETURN || input_keycode == UI_KEYCODE_CLEAR)
    {
        // 配置跳转到待机页面
        ui_page_exit_param_t page_exit_param;
        ui_page_exit_param_init(&page_exit_param, UI_RETURN_TO_PARENT_PAGE);
        ui_page_manager_jump_to_page(UI_PAGE_ID_HOME, &page_exit_param, NULL);
    }
    else if (input_keycode == UI_KEYCODE_CONFIRM || input_keycode == UI_KEYCODE_KNOB_MID) {
        // 旋钮中键点击处理
        if (map_page->is_button_selection_mode) {
            // 如果处于按钮选择模式，则确认选中当前按钮
            if (map_page->selected_button_index == MAP_BUTTON_TOOL) {
                // 点击工具按钮
                lv_event_send(map_page->map_tool_btn, LV_EVENT_CLICKED, NULL);
            } else if (map_page->selected_button_index == MAP_BUTTON_OFF) {
                // 点击复位按钮
                lv_event_send(map_page->map_off_btn, LV_EVENT_CLICKED, NULL);
            }
            
            // 退出按钮选择模式
            map_page->is_button_selection_mode = false;
            ui_update_button_selection(map_page);
        } else {
            // 检测是否是双击
            if (ui_utils_is_timer_running(map_page->double_click_timer)) {
                // 第二次点击，进入按钮选择模式
                ui_utils_delete_timer(map_page->double_click_timer);
                map_page->double_click_timer = NULL;
                
                // 进入按钮选择模式
                map_page->is_button_selection_mode = true;
                map_page->selected_button_index = MAP_BUTTON_TOOL; // 默认选中工具按钮
                ui_update_button_selection(map_page);
            } else {
                // 第一次点击，启动定时器
                map_page->double_click_timer = lv_timer_create(ui_double_click_timer_cb, 
                                                              UI_DOUBLE_CLICK_TIMEOUT, 
                                                              map_page);
                lv_timer_set_repeat_count(map_page->double_click_timer, 1);
            }
        }
    }
    // 处理音量键
    else if (input_keycode == UI_KEYCODE_KNOB_CLOCKWISE || input_keycode == UI_KEYCODE_VOL_UP) {
        if (map_page->is_button_selection_mode) {
            // 在按钮选择模式下，切换选中的按钮
            map_page->selected_button_index = (map_page->selected_button_index + 1) % MAP_BUTTON_COUNT;
            ui_update_button_selection(map_page);
        } else {
            // 放大地图
            enlarge_event_callback();
        }
    }
    else if (input_keycode == UI_KEYCODE_KNOB_ANTICLOCKWISE || input_keycode == UI_KEYCODE_VOL_DOWN) {
        if (map_page->is_button_selection_mode) {
            // 在按钮选择模式下，切换选中的按钮
            map_page->selected_button_index = (map_page->selected_button_index + MAP_BUTTON_COUNT - 1) % MAP_BUTTON_COUNT;
            ui_update_button_selection(map_page);
        } else {
            // 缩小地图
            reduce_event_callback();
        }
    }
}

/**
 * @brief 地图容器长按重复事件回调
 */
static void ui_map_container_long_pressed_repeat_event_cb(lv_event_t *event)
{
    ui_map_page_t *map_page = (ui_map_page_t *)lv_event_get_user_data(event);
    // 在此处添加长按重复事件处理逻辑
}

/**
 * @brief 地图容器按键事件回调
 */
static void ui_map_container_key_event_cb(lv_event_t *event)
{
    uint32_t input_keycode = *(uint32_t *)lv_event_get_param(event);
    ui_map_page_t *map_page = (ui_map_page_t *)lv_event_get_user_data(event);

    if (input_keycode == UI_KEYCODE_VOL_UP || input_keycode == UI_KEYCODE_VOL_DOWN)
    {
        lv_event_send(map_page->page.screen, LV_EVENT_KEY_CLICKED, &input_keycode);
    }
    else if (input_keycode == UI_KEYCODE_NEXT_CONTACT || input_keycode == UI_KEYCODE_PREV_CONTACT)
    {
        lv_event_send(map_page->page.screen, LV_EVENT_KEY_CLICKED, &input_keycode);
    } 
    else if (input_keycode == UI_KEYCODE_KNOB_CLOCKWISE || input_keycode == UI_KEYCODE_KNOB_ANTICLOCKWISE)
    {
        lv_event_send(map_page->page.screen, LV_EVENT_KEY_CLICKED, &input_keycode);
    }
}


/**
 * @brief 地图页面创建回调
 */
static void ui_map_page_create_cb(ui_page_t *page)
{
    ui_map_page_t *map_page = (ui_map_page_t *)page;
    ui_map_page_initialize_view(map_page);
}

/**
 * @brief 地图页面销毁回调
 */
static void ui_map_page_destroy_cb(ui_page_t *page)
{
    ui_map_page_t *map_page = (ui_map_page_t *)page;
    // 清理资源
    lv_obj_del(map_page->page.screen);
}

/**
 * @brief 地图页面进入回调
 */
static void ui_map_page_enter_cb(ui_page_t *page, ui_page_enter_param_t *page_enter_param)
{
    ui_map_page_t *map_page = (ui_map_page_t *)page;
    // 页面进入处理
}

/**
 * @brief 地图页面退出回调
 */
static void ui_map_page_exit_cb(ui_page_t *page, ui_page_exit_param_t *page_exit_param)
{
    ui_map_page_t *map_page = (ui_map_page_t *)page;
    
    // 清理双击检测定时器
    if (ui_utils_is_timer_running(map_page->double_click_timer)) {
        ui_utils_delete_timer(map_page->double_click_timer);
        map_page->double_click_timer = NULL;
    }
    
    // 清理地图资源
    map_cleanup();
    if (map_page->gps_timer != NULL) {
        lv_timer_del(map_page->gps_timer);
        map_page->gps_timer = NULL;
    }
}

/**
 * @brief 获取地图页面的单例对象
 * 
 * @return ui_map_page_t* 地图页面实例
 */
ui_map_page_t *ui_map_page_get_instance(void)
{
    static ui_map_page_t *map_page = NULL;
    
    if (map_page == NULL)
    {
        map_page = ui_map_page_constructor();
    }
    
    return map_page;
}

HY_MAP* ui_map_page_get_hy_set_map(ui_map_page_t* page) {
    return &page->hy_set_map;
}

/**
 * @brief 地图页面参数初始化函数
 */
void ui_map_page_enter_param_init(ui_map_page_enter_param_t *page_enter_param, ui_page_id_t prev_page_id, ui_page_nav_type_t nav_type)
{
    LV_ASSERT_NULL(page_enter_param);

    ui_page_enter_param_init(&page_enter_param->base_param, prev_page_id, nav_type);
}
//todo: 松开PTT键后返回本端位置，在按下PTT键后不再刷新位置
static void ui_map_page_poc_refresh_timer_cb(lv_timer_t *timer)
{
    ui_map_page_t *map_page = (ui_map_page_t *)timer->user_data;
    ui_map_page_status_update_handler(map_page);
}

/**
 * @brief 当通话状态(空闲/通话/讲话/放音)发生变化时, 调用以更新待机页面
 *
 */
void ui_map_page_status_update_handler(ui_map_page_t *map_page)
{
    uint8_t trx_state = param_get_trx_state();
    uint8_t work_mode = param_get_work_mode();
    map_page->rmt_gps_pos[0] = param_get_rmt_gps_longitude();
    map_page->rmt_gps_pos[1] = param_get_rmt_gps_latitude();
    printf("%s:rmt_gps_pos[0]=%.6f, rmt_gps_pos[1]=%.6f\n", __FUNCTION__, map_page->rmt_gps_pos[0], map_page->rmt_gps_pos[1]);

    if (trx_state == PARAM_TRX_STATE_LISTENING)
    {
        ui_map_page_listening_state_handler(map_page);
    }
    else if (trx_state == PARAM_TRX_STATE_CALLING)
    {
        ui_map_page_calling_state_handler(map_page);
    }

    map_page->trx_state = trx_state;
}

void ui_map_page_listening_state_handler(ui_map_page_t *map_page)
{
    // 获取当前设备的状态
    uint8_t work_mode = param_get_work_mode();

    switch (work_mode)
    {
    case PARAM_WORK_MODE_ANALOG_CONVENTIONAL:
        // break;
    case PARAM_WORK_MODE_ANALOG_TRUNKING:
    case PARAM_WORK_MODE_DIGITAL_TRUNKING:
        // break;
    case PARAM_WORK_MODE_DIGITAL_CONVENTIONAL:
    case PARAM_WORK_MODE_ADHOC:
    case PARAM_WORK_MODE_ADHOC_Q:
    case PARAM_WORK_MODE_ADHOC_N:
    case PARAM_WORK_MODE_ADHOC_V:
    case PARAM_WORK_MODE_ADHOC_V25:
        ui_listening_state_handler_xv(map_page);
        break;
    case PARAM_WORK_MODE_POC:
        break;
    default:
        break;
    }
}

void ui_map_page_calling_state_handler(ui_map_page_t *map_page)
{
    // 获取当前设备的状态
    uint8_t work_mode = param_get_work_mode();
    switch (work_mode)
    {
    case PARAM_WORK_MODE_ANALOG_CONVENTIONAL:
        // break;
    case PARAM_WORK_MODE_DIGITAL_CONVENTIONAL:
        // break;
    case PARAM_WORK_MODE_POC:
        // break;
    case PARAM_WORK_MODE_ANALOG_TRUNKING:
    case PARAM_WORK_MODE_DIGITAL_TRUNKING:
        // break;
    case PARAM_WORK_MODE_ADHOC:
    case PARAM_WORK_MODE_ADHOC_Q:
    case PARAM_WORK_MODE_ADHOC_N:
    case PARAM_WORK_MODE_ADHOC_V:
    case PARAM_WORK_MODE_ADHOC_V25:
        ui_calling_state_handler_xv(map_page);
        break;
    default:
        break;
    }
}
//============================> Label Style Update During Listening <============================
static void ui_listening_state_handler_xv(ui_map_page_t *map_page)
{
    uint8_t rmt_gps_state = param_get_gps_state();
    printf("%s:rmt_gps_state=0x%02X, lon=%.6f, lat=%.6f\n", __FUNCTION__, rmt_gps_state, map_page->rmt_gps_pos[0], map_page->rmt_gps_pos[1]);
    if ((rmt_gps_state & 0x01) == 0 || (rmt_gps_state & 0x10) == 0) // 无效GPS数据
    {
        return;
    }

    utl_geo_coord_t coords = {map_page->rmt_gps_pos[0], map_page->rmt_gps_pos[1]};
    ui_map_display_other_person(coords);
}
//============================> Label Style Update During Calling <============================
static void ui_calling_state_handler_xv(ui_map_page_t *map_page)
{
    // 提取通话信息
    bool is_calling_party = param_is_caller();
    uint8_t rmt_gps_state = param_get_gps_state();
    printf("%s:rmt_gps_state=0x%02X, lon=%.6f, lat=%.6f\n", __FUNCTION__, rmt_gps_state, map_page->rmt_gps_pos[0], map_page->rmt_gps_pos[1]);
    if ((rmt_gps_state & 0x01) == 0 || (rmt_gps_state & 0x10) == 0) // 无效GPS数据
    {
        return;
    }

    if (map_page->trx_state == PARAM_TRX_STATE_IDLE)
    {

        if (!is_calling_party)
        {
            utl_geo_coord_t coords = {map_page->rmt_gps_pos[0], map_page->rmt_gps_pos[1]};
            ui_map_display_other_person(coords);
        }
    }
}

/**
 * @brief 设置地图为1km比例尺
 */
void ui_map_set_1km_scale(void)
{
    // 设置比例尺为1km
    scale_count = SCALE_1_1KM;

    // 设置缩放标志，触发地图重绘
    gps_map_info.zoom = 2; // 2表示放大操作
    gps_map_info.show_index = judge_buf_index(scale_count);

    // 重新计算地图范围
    mlp_ratio_cal(m_draw_coord, scale_count);

    // 触发地图重绘
    redraw_all();

    printf("Map scale set to 1KM (scale_count=%d)\n", scale_count);
}

/**
 * @brief 检查坐标是否在当前1km比例尺视口范围内
 *
 * @param coords 要检查的经纬度坐标
 * @return true 坐标在视口范围内
 * @return false 坐标不在视口范围内
 */
bool ui_map_coords_in_viewport(utl_geo_coord_t coords)
{
    // 将经纬度坐标转换为像素坐标
    gfx_point_t pixel_pos = {0};
    map_coord_get_pos(coords, &pixel_pos);

    // 计算地图视口范围 (考虑TOP_BAR_HEIGHT)
    const int viewport_width = SCREEN_WIDTH;
    const int viewport_height = SCREEN_HEIGHT - TOP_BAR_HEIGHT;

    // 检查像素坐标是否在视口范围内
    bool in_viewport = (pixel_pos.x >= 0 && pixel_pos.x <= viewport_width &&
                       pixel_pos.y >= 0 && pixel_pos.y <= viewport_height);

    printf("Coords check: lon=%.6f, lat=%.6f -> pixel(%d,%d), viewport=%dx%d, in_viewport=%s\n",
           coords.longitude, coords.latitude, pixel_pos.x, pixel_pos.y,
           viewport_width, viewport_height, in_viewport ? "true" : "false");

    return in_viewport;
}

/**
 * @brief 移除另一个人的标记
 */
void ui_map_remove_other_person_marker(void)
{
    ui_map_page_t *map_page = ui_map_page_get_instance();
    if (!map_page) {
        printf("Error: Cannot get map page instance\n");
        return;
    }

    // 使用索引1作为其他人的标记 (索引0用于当前位置)
    if (map_page->hy_set_map.screen_img_map_cur_point[1] != NULL) {
        lv_obj_del(map_page->hy_set_map.screen_img_map_cur_point[1]);
        map_page->hy_set_map.screen_img_map_cur_point[1] = NULL;
        printf("Other person marker removed\n");
    }
}

/**
 * @brief 添加另一个人的标记到地图上
 *
 * @param coords 另一个人的经纬度坐标
 */
void ui_map_add_other_person_marker(utl_geo_coord_t coords)
{
    ui_map_page_t *map_page = ui_map_page_get_instance();
    if (!map_page) {
        printf("Error: Cannot get map page instance\n");
        return;
    }

    // 先移除现有的其他人标记
    ui_map_remove_other_person_marker();

    // 将经纬度坐标转换为像素坐标
    gfx_point_t pixel_pos = {0};
    map_coord_get_pos(coords, &pixel_pos);

    // 创建新的标记图像对象
    lv_obj_t *other_person_marker = lv_img_create(map_page->page.screen);
    // pixel_pos.y = SCREEN_HEIGHT - TOP_BAR_HEIGHT - pixel_pos.y; // Y轴翻转

    // 设置标记位置 (减去图像尺寸的一半以居中显示)
    // 注意：不需要加 TOP_BAR_HEIGHT，因为 map_coord_get_pos 返回的坐标已经是相对于地图显示区域的
    lv_obj_set_pos(other_person_marker, pixel_pos.x - 14, SCREEN_HEIGHT - pixel_pos.y - 14 + TOP_BAR_HEIGHT/2);
    lv_obj_set_size(other_person_marker, 28, 28);
    lv_obj_set_scrollbar_mode(other_person_marker, LV_SCROLLBAR_MODE_OFF);

    // 设置样式
    static lv_style_t style_other_person_marker;
    if (style_other_person_marker.prop_cnt > 1)
        lv_style_reset(&style_other_person_marker);
    else
        lv_style_init(&style_other_person_marker);
    lv_style_set_img_recolor(&style_other_person_marker, lv_color_make(0xff, 0xff, 0xff));
    lv_style_set_img_recolor_opa(&style_other_person_marker, 0);
    lv_style_set_img_opa(&style_other_person_marker, 255);
    lv_obj_add_style(other_person_marker, &style_other_person_marker, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_add_flag(other_person_marker, LV_OBJ_FLAG_CLICKABLE);

    // 设置图像源 (使用蓝色标记区分其他人)
    lv_img_set_src(other_person_marker, "/victel/resource/map_cur_point_blue.png");
    lv_img_set_pivot(other_person_marker, 0, 0);
    lv_img_set_angle(other_person_marker, 0);

    // 保存到标记数组
    map_page->hy_set_map.screen_img_map_cur_point[1] = other_person_marker;

    printf("Other person marker added at pixel(%d,%d) for coords(%.6f,%.6f)\n",
           pixel_pos.x, pixel_pos.y, coords.longitude, coords.latitude);
}

/**
 * @brief 显示另一个人的坐标在地图上
 *
 * 功能说明：
 * 1. 切换地图到1km比例尺
 * 2. 如果坐标在当前视口范围内：显示蓝色标记
 * 3. 如果坐标在当前视口范围外：只改变地图中心到该位置，不显示标记
 *
 * @param coords 另一个人的经纬度坐标
 */
void ui_map_display_other_person(utl_geo_coord_t coords)
{
    printf("=== Displaying other person coordinates: lon=%.6f, lat=%.6f ===\n",
           coords.longitude, coords.latitude);

    // 验证坐标有效性
    if (coords.longitude == 0.0 && coords.latitude == 0.0) {
        printf("Warning: Invalid coordinates (0,0), skipping display\n");
        return;
    }

    // 1. 切换地图到1km比例尺
    printf("Step 1: Setting map to 1km scale\n");
    ui_map_set_1km_scale();

    // 等待地图重绘完成
    usleep(100 * 1000); // 等待100ms

    // 2. 检查其他人的坐标是否在当前1km比例尺视口范围内
    printf("Step 2: Checking if coordinates are within viewport\n");
    bool coords_in_viewport = ui_map_coords_in_viewport(coords);

    if (!coords_in_viewport) {
        // 3. 如果坐标不在视口范围内，只改变地图中心，不显示标记
        printf("Step 3: Coordinates not in viewport, changing map center only (no marker)\n");

        // 3a. 检查其他人的坐标是否在当前打开的地图文件覆盖范围内
        int coords_in_mapfile = check_cur_is_in_mapfile(coords);
        printf("Step 3a: Coordinates in current map file: %s\n", coords_in_mapfile ? "YES" : "NO");

        if (!coords_in_mapfile) {
            // 3b. 如果不在当前地图文件范围内，尝试打开包含该坐标的地图文件
            printf("Step 3b: Attempting to open map file for coordinates\n");

            // 检查是否存在包含该坐标的地图文件
            int map_file_exists = 0;
            int check_result = GuiMapCheckMapFileExist(coords.longitude, coords.latitude, &map_file_exists);

            if (check_result == 0 && map_file_exists) {
                printf("Step 3b: Found map file for coordinates, attempting to open\n");

                // 尝试重新打开地图文件
                int reopen_result = gps_check_cur_coord(coords, 1); // 1表示强制重新打开

                if (reopen_result == 1) {
                    printf("Step 3b: Successfully opened map file for coordinates\n");
                } else {
                    printf("Step 3b: Failed to open map file for coordinates\n");
                    printf("Warning: Cannot display other person's coordinates - no map coverage\n");
                    return;
                }
            } else {
                printf("Step 3b: No map file found for coordinates (%.6f,%.6f)\n",
                       coords.longitude, coords.latitude);
                printf("Warning: Cannot display other person's coordinates - no map file available\n");
                return;
            }
        }

        // 3c. 改变地图中心到其他人的位置
        printf("Step 3c: Changing map center to other person's location\n");

        // 保存当前地图中心
        utl_geo_coord_t old_center = m_draw_coord;

        // 设置新的地图中心为其他人的位置
        m_draw_coord = coords;

        // 重新计算地图范围并触发重绘
        mlp_ratio_cal(m_draw_coord, SCALE_1_1KM);
        redraw_all();

        printf("Map center changed from (%.6f,%.6f) to (%.6f,%.6f)\n",
               old_center.longitude, old_center.latitude,
               coords.longitude, coords.latitude);

        // 等待地图重绘完成
        usleep(200 * 1000); // 等待200ms

        printf("=== Map center changed to other person's location (no marker displayed) ===\n");
    } else {
        // 4. 坐标在视口范围内，显示其他人的坐标作为标记
        printf("Step 3: Coordinates are within viewport, keeping current map center\n");
        printf("Step 4: Adding other person marker\n");
        ui_map_add_other_person_marker(coords);

        printf("=== Other person marker displayed within current viewport ===\n");
    }
}

/**
 * @brief 测试函数：显示其他人坐标功能演示
 *
 * 这个函数演示如何使用ui_map_display_other_person功能
 * 可以在地图页面初始化后调用此函数进行测试
 */
void ui_map_test_other_person_display(void)
{
    printf("=== Testing Other Person Display Functionality ===\n");

    // 测试坐标1：深圳附近的坐标 (应该在当前地图范围内)
    utl_geo_coord_t test_coords_1 = {113.935000, 22.541000};
    printf("Test 1: Displaying coordinates near current location\n");
    ui_map_display_other_person(test_coords_1);

    // 先等待地图重绘完成
    printf("Waiting for map redraw to complete...\n");
    usleep(500 * 1000); // 等待500ms让地图重绘完成

    printf("=== Please observe the blue marker on the map for 5 seconds ===\n");
    // 分段等待，避免阻塞UI线程太久
    for(int i = 0; i < 10; i++) {
        usleep(500 * 1000); // 每次等待500ms
        if((i+1) % 2 == 0) {
            printf("Observation time: %d/5 seconds\n", (i+1)/2);
        }
    }

    // 测试坐标2：北京的坐标 (应该不在当前地图范围内，会触发地图文件检查和中心改变)
    // utl_geo_coord_t test_coords_2 = {116.397128, 39.916527};
    // printf("Test 2: Displaying coordinates far from current location (Beijing) - testing map file coverage\n");
    // ui_map_display_other_person(test_coords_2);

    // 等待一段时间让用户观察结果
    // usleep(3000 * 1000); // 等待3秒

    // 测试坐标3：移除其他人标记
    printf("Test 3: Removing other person marker\n");
    ui_map_remove_other_person_marker();

    printf("=== Other Person Display Test Completed ===\n");
}

// 非阻塞测试的状态变量
static lv_timer_t *test_timer = NULL;
static int test_step = 0;
static int observation_counter = 0;

/**
 * @brief 非阻塞测试的定时器回调函数
 */
static void test_timer_callback(lv_timer_t *timer)
{
    switch(test_step) {
        case 0:
            // 步骤0：显示其他人坐标
            printf("=== Non-blocking Test: Displaying other person coordinates ===\n");
            utl_geo_coord_t test_coords = {113.268116,23.232131};
            ui_map_display_other_person(test_coords);
            test_step = 1;
            observation_counter = 0;
            // 设置定时器为500ms间隔，用于观察
            lv_timer_set_period(timer, 500);
            break;

        case 1:
            // 步骤1：观察期间
            observation_counter++;
            if(observation_counter == 1) {
                printf("=== Please observe the blue marker on the map ===\n");
            }
            if(observation_counter % 2 == 0) {
                printf("Observation time: %d/5 seconds\n", observation_counter/2);
            }

            if(observation_counter >= 10) { // 5秒观察时间
                test_step = 2;
            }
            break;

        case 2:
            // 步骤2：移除标记并结束测试
            printf("Test: Removing other person marker\n");
            ui_map_remove_other_person_marker();
            printf("=== Non-blocking Test Completed ===\n");

            // 清理定时器
            lv_timer_del(timer);
            test_timer = NULL;
            test_step = 0;
            break;
    }
}

/**
 * @brief 非阻塞测试函数：显示其他人坐标功能演示
 *
 * 这个函数使用LVGL定时器进行非阻塞测试，不会阻塞UI线程
 * 推荐在实际使用中调用此函数而不是阻塞版本
 */
void ui_map_test_other_person_display_non_blocking(void)
{
    // 如果已经有测试在运行，先停止
    if(test_timer != NULL) {
        lv_timer_del(test_timer);
        test_timer = NULL;
    }

    // 重置状态
    test_step = 0;
    observation_counter = 0;

    // 创建定时器，初始延迟100ms
    test_timer = lv_timer_create(test_timer_callback, 100, NULL);
}