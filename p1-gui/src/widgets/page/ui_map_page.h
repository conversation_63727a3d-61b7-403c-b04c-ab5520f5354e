/**
 * @file ui_map_page.h
 * @brief 地图页面头文件
 */

#ifndef UI_MAP_PAGE_H
#define UI_MAP_PAGE_H

#include <stdint.h>
#include <stdlib.h>
#include <stdbool.h>
#include "lvgl.h"
#include "map_view.h"
#include "utl_geo.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 地图页面结构体
 */
struct s_ui_map_page;
typedef struct s_ui_map_page ui_map_page_t;

/**
 * @brief 地图页面进入参数
 */
typedef struct s_ui_map_page_enter_param
{
    ui_page_enter_param_t base_param; // 基类参数
} ui_map_page_enter_param_t;

/**
 * @brief 获取地图页面的单例对象
 * 
 * @return ui_map_page_t* 地图页面实例
 */
ui_map_page_t *ui_map_page_get_instance(void);

/**
 * @brief 地图页面参数初始化函数
 * 
 * @param param 地图页面进入参数
 * @param prev_page_id 上一个页面ID
 * @param nav_type 页面导航类型
 */
void ui_map_page_enter_param_init(ui_map_page_enter_param_t *param, ui_page_id_t prev_page_id, ui_page_nav_type_t nav_type);

HY_MAP* ui_map_page_get_hy_set_map(ui_map_page_t* page);

/**
 * @brief 当通话状态(空闲/通话/讲话/放音)发生变化时, 调用以更新地图页面
 *
 * @param map_page             地图页面
 */
void ui_map_page_status_update_handler(ui_map_page_t *map_page);
/**
 * @brief 呼叫状态时, 地图页面的处理函数
 *
 * @param map_page             地图页面
 */
void ui_map_page_calling_state_handler(ui_map_page_t *map_page);

/**
 * @brief 监听状态时, 地图页面的处理函数
 *
 * @param map_page             地图页面
 */
void ui_map_page_listening_state_handler(ui_map_page_t *map_page);

/**
 * @brief 讲话状态时, 地图页面的处理函数
 *
 * @param map_page             地图页面
 */
void ui_map_page_speaking_state_handler(ui_map_page_t *map_page);
/**
 * @brief 设置地图为1km比例尺
 */
void ui_map_set_1km_scale(void);

/**
 * @brief 检查坐标是否在当前1km比例尺视口范围内
 *
 * @param coords 要检查的经纬度坐标
 * @return true 坐标在视口范围内
 * @return false 坐标不在视口范围内
 */
bool ui_map_coords_in_viewport(utl_geo_coord_t coords);

/**
 * @brief 显示另一个人的坐标在地图上
 *
 * 功能说明：
 * 1. 切换地图到1km比例尺
 * 2. 如果坐标在当前视口范围内：显示蓝色标记
 * 3. 如果坐标在当前视口范围外：只改变地图中心到该位置，不显示标记
 *
 * @param coords 另一个人的经纬度坐标
 */
void ui_map_display_other_person(utl_geo_coord_t coords);

/**
 * @brief 添加另一个人的标记到地图上
 *
 * @param coords 另一个人的经纬度坐标
 */
void ui_map_add_other_person_marker(utl_geo_coord_t coords);

/**
 * @brief 移除另一个人的标记
 */
void ui_map_remove_other_person_marker(void);

/**
 * @brief 测试函数：显示其他人坐标功能演示
 *
 * 这个函数演示如何使用ui_map_display_other_person功能
 * 可以在地图页面初始化后调用此函数进行测试
 */
void ui_map_test_other_person_display(void);

/**
 * @brief 非阻塞测试函数：显示其他人坐标功能演示
 *
 * 这个函数使用LVGL定时器进行非阻塞测试，不会阻塞UI线程
 * 推荐在实际使用中调用此函数而不是阻塞版本
 */
void ui_map_test_other_person_display_non_blocking(void);

// 外部函数声明 - 地图相关
extern int check_cur_is_in_mapfile(utl_geo_coord_t coord);
extern int GuiMapCheckMapFileExist(double lon, double lat, int *exist);
extern int gps_check_cur_coord(utl_geo_coord_t coord, int reOpen);

extern void map_license_task_create(void);

#ifdef __cplusplus
}
#endif

#endif // UI_MAP_PAGE_H 