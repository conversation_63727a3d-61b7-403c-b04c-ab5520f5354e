#include "map_port.h"
#include <stdlib.h>
#include <signal.h>
#include <sys/stat.h>
//#include "trk_api.h"
#include "mlpfile.h"
#include "gfx_pub.h"
//#include "rtconfig.h"
//#include "map_task.h"
#include "xml_lib.h"
#include "ui.h"
#include "trk_api.h"
//#include "lvgl.h"


#define PRINT_MAP_PORT 0
#define SIGTRACK 33

/*****************************************************************************
*
*
*						地图flag及相关变量汇总
*
*
*****************************************************************************/

//*********** 当前展示的地图比例尺(m_scale是绘制比例尺) *********************
int scale_count = 13;

//************** 当前坐标超出了设定的范围(需刷新改变中心点的地图) ***********
int out_of_range = 0;	//这个变量屏幕为466*466时用不到，代码中全部置1

//************** 当前地图功能模式 *******************************************
page_mode_t m_mode = POS_MODE;

//************** 上一个地图功能模式 *******************************************
page_mode_t last_m_mode = POS_MODE;

//************** 地图全局信息 ************************************************
extern struct gps_map_t gps_map_info;

//************** 当前地图绘制状态 *******************************************
extern draw_status_t map_status;


//*********** 当前的屏幕大小 ********************************************
extern struct rect m_rect;

extern uint8_t m_data_ready;


/*****************************************************************************
*
*
*						轨迹相关
*
*
*****************************************************************************/

//************** 轨迹返航的当前轨迹索引 *************************************
uint16_t num_of_tra = 0;

//************** 正在读取绘制第几条轨迹 *************************************
uint16_t trk_draw_idx;

//************** 记录保存的总轨迹数 *****************************************
static uint16_t trk_rec_idx;

//************** 用于保存记录轨迹的数组 *************************************
static trk_pos_t *ptrk = NULL;

//************** 切换起点终点 ***********************************************
bool revert_pos = false;

//************** 定位成功后刷新时间 *****************************************
uint32_t gps_timer_cnt = 0;

//************** 定位成功后刷新时间 *****************************************
uint32_t timer_redraw_cnt = 0;


//************** 距离 *******************************************************
static double total_dist = 0;

//************** 轨迹文件解析flag *******************************************
bool gpx_parse = false;

//************** 刷新一次，记录一次轨迹 *************************************
static uint16_t trk_idx;

//************** 最新记录轨迹记录 ***********************************************
utl_geo_coord_t m_rec_coord = {0};

//************** 最新返航轨迹记录 ***********************************************
utl_geo_coord_t m_ret_coord = {0};

//************** 最新导航轨迹记录 ***********************************************
utl_geo_coord_t m_nav_coord = {0};

//************** 模拟GPS实时定到位置用 **************************************
int real_time_gps_draw = 1;

//************** gps是否定位成功 ********************************************
static bool gps_fixed = true;


// 轨迹描绘状态标志
static int track_drawing_active = 0;  // 0: 未激活, 1: 已激活



extern int gfx_wdth;
extern int gfx_hght;
extern int record_track_en;
extern int rt_sig;


 /*****************************************************************************
*
*
*						轨迹偏航相关
*
*
*****************************************************************************/


//************** 导航模式下轨迹是否偏航 **************************************
int out_track_flag = 0;

int32_t* trk_point_array = NULL;

 //extern int32_t* trk_get_trk_array(int trk_pointnum);





// 设置地图展示模式
extern void set_map_show_mode(int mode);

// 获取地图展示模式
extern int get_map_show_mode();

int gps_check_cur_coord(utl_geo_coord_t coord, int reOpen);




/********************************************************************
 *						多文件测试
 ********************************************************************/
 
#define MAP_COOK_ISLANDS 0		// 可行
#define MAP_ILE_DE_CLIPPERTON 1 // 不可行(未找到坐标点)
#define MAP_ANDORRA 2			// 可行
#define MAP_AMERICAN_OCEANIA 3	// 不可行(未找到坐标点)
#define MAP_NAURU 4				// 可行
#define MAP_CHINA 5
#define MAP_CONTOUR 6 // 等高线测试

#define CONFIG_MAP_COUNTRY MAP_CHINA

#if (CONFIG_MAP_COUNTRY == MAP_NAURU)

utl_geo_coord_t m_cur_coord = {166.929400, -0.534600};
utl_geo_coord_t m_draw_coord = {166.929400, -0.534600};

#elif (CONFIG_MAP_COUNTRY == MAP_ANDORRA)

utl_geo_coord_t m_cur_coord = {1.528666, 42.50633};
utl_geo_coord_t m_draw_coord = {1.528666, 42.50633}; // 1700


#elif (CONFIG_MAP_COUNTRY == MAP_CHINA)	//给深圳坐标【WGS84(国际通用)】

//utl_geo_coord_t m_cur_coord = {114.17314922872863, 22.283554096758383};
//utl_geo_coord_t m_draw_coord = {114.17314922872863, 22.283554096758383};

// utl_geo_coord_t m_draw_coord = {113.93603572641095,22.528572566870306};
// utl_geo_coord_t m_cur_coord = {113.93603572641095,22.528572566870306};


//utl_geo_coord_t m_cur_coord = {116.46642074682812, 40.01824340098018};
//utl_geo_coord_t m_draw_coord = {116.46642074682812, 40.01824340098018};

//utl_geo_coord_t m_cur_coord = {114.053069,22.545953};
//utl_geo_coord_t m_draw_coord = {114.053069,22.545953};

//utl_geo_coord_t m_cur_coord = {120.053069,26.545953};
//utl_geo_coord_t m_draw_coord = {120.053069,26.545953};

// utl_geo_coord_t m_cur_coord = {	113.94238781931153,22.534010202666003};
// utl_geo_coord_t m_draw_coord = { 113.94238781931153,22.534010202666003};

// utl_geo_coord_t m_cur_coord = {113.9196744, 22.5813983};
// utl_geo_coord_t m_draw_coord = {113.9196744, 22.5813983};
// double g_defLon = 113.9196744;
// double g_defLat = 22.5813983;

#if 0 //汕头
utl_geo_coord_t m_cur_coord = {116.76384371362158,23.454542167805727};
utl_geo_coord_t m_draw_coord = {116.76384371362158,23.454542167805727};

static double g_defLon = 116.7638437;
static double g_defLat = 23.4545421;

#else //深圳
utl_geo_coord_t m_cur_coord = {113.293403,23.224812};
utl_geo_coord_t m_draw_coord = {113.293403,23.224812};
static double g_defLon = 113.293403;
static double g_defLat = 23.224812;
// utl_geo_coord_t m_cur_coord = {113.309364, 23.301767};
// utl_geo_coord_t m_draw_coord = {113.309364, 23.301767};
// static double g_defLon = 113.309364;
// static double g_defLat = 23.301767;
// utl_geo_coord_t m_cur_coord;
// utl_geo_coord_t m_draw_coord;
// static double g_defLon;
// static double g_defLat;

#endif






#elif (CONFIG_MAP_COUNTRY == MAP_COOK_ISLANDS)

utl_geo_coord_t m_cur_coord = {-159.777216, -21.230305};
utl_geo_coord_t m_draw_coord = {-159.777216, -21.230305};

#elif (CONFIG_MAP_COUNTRY == MAP_ILE_DE_CLIPPERTON)

utl_geo_coord_t m_cur_coord = {-109.221875, 10.305452};
utl_geo_coord_t m_draw_coord = {-109.221875, 10.305452};

#elif (CONFIG_MAP_COUNTRY == MAP_AMERICAN_OCEANIA)

utl_geo_coord_t m_cur_coord = {111.000000, 21.000000};
utl_geo_coord_t m_draw_coord = {111.000000, 21.000000};

#elif (CONFIG_MAP_COUNTRY == MAP_CONTOUR)

utl_geo_coord_t m_cur_coord = {112.0000, 22.0000};
utl_geo_coord_t m_draw_coord = {112.0000, 22.0000};

#endif


#if 1
//模拟记录轨迹时的GPS定位得到的坐标经纬度
utl_geo_coord_t trk_myself[] = {

#if 1
	{1.52892115865814,42.506749111980052}, //中心点
	{1.52852115865814,42.506559111980052}, //中心点
	{1.52847115865814,42.506019111980052}, //2
	{1.52852115865814,42.506419111980052}, //3
	{1.52897115865814,42.506619111980052}, //4
	{1.52837115865814,42.506519111980052}, //5
	{1.52872115865814,42.506019111980052}, //6
	{1.52807115865814,42.506519111980052}, //7
	{1.52837115865814,42.506819111980052}, //2
	{1.52862115865814,42.506119111980052}, //3
	{1.52887115865814,42.506219111980052}, //4
	{1.52807115865814,42.506919111980052}, //5
	{1.52862115865814,42.506219111980052}, //6
	{1.52817115865814,42.506919111980052}, //7
	{1.52857115865814,42.506219111980052}, //2
	{1.52882115865814,42.506419111980052}, //3
	{1.52807115865814,42.506519111980052}, //4
	{1.52817115865814,42.506019111980052}, //5
	{1.52872115865814,42.506919111980052}, //6
	{1.52807115865814,42.506419111980052}, //7
#else
	{113.94238781931153,22.534010202666003},
	{113.94238781931153,22.534410202666003},
	{113.94228781931153,22.534210202666003},
	{113.94268781931153,22.534810202666003},
	{113.94288781931153,22.534710202666003},
	{113.94268781931153,22.534310202666003},
	{113.94248781931153,22.534410202666003},
	{113.94288781931153,22.534110202666003},
	{113.94268781931153,22.534210202666003},
	{113.94238781931153,22.534610202666003},
	{113.94268781931153,22.534310202666003},
	{113.94248781931153,22.534210202666003},
	{113.94258781931153,22.534510202666003},
	{113.94228781931153,22.534210202666003},
	{113.94218781931153,22.534810202666003},
	{113.94268781931153,22.534710202666003},
	{113.94248781931153,22.534410202666003},
	{113.94288781931153,22.534510202666003},
	{113.94248781931153,22.534210202666003},
	{113.94228781931153,22.534110202666003},
	


#endif

};
		
//模拟循迹返航的GPS定位得到的坐标经纬度
utl_geo_coord_t trk_myself_return[] = {
			{1.52892115865814,42.506749111980052}, //中心点
	{1.52852115865814,42.506559111980052}, //中心点
	{1.52847115865814,42.506019111980052}, //2
	{1.52852115865814,42.506419111980052}, //3
	{1.52897115865814,42.506619111980052}, //4
	{1.52837115865814,42.506519111980052}, //5
	{1.52872115865814,42.506019111980052}, //6
	{1.52807115865814,42.506519111980052}, //7
	{1.52837115865814,42.506819111980052}, //2
	{1.52862115865814,42.506119111980052}, //3
	{1.52887115865814,42.506219111980052}, //4
	{1.52807115865814,42.506919111980052}, //5
	{1.52862115865814,42.506219111980052}, //6
	{1.52817115865814,42.506919111980052}, //7
	{1.52857115865814,42.506219111980052}, //2
	{1.52882115865814,42.506419111980052}, //3
	{1.52807115865814,42.506519111980052}, //4
	{1.52817115865814,42.506019111980052}, //5
	{1.52872115865814,42.506919111980052}, //6
	{1.52807115865814,42.506419111980052}, //7

};

//模拟导入轨迹后导航的GPS定位得到的坐标经纬度
utl_geo_coord_t trk_myself_navigation[] = {


//	{1.528421,42.506000}, //中心点
//	{1.528471,42.506219}, //中心点
//	{1.528521,42.506319}, //2
//	{1.528571,42.506419}, //3
//	{1.528621,42.506519}, //4
//	
//	{1.528721,42.506719}, //6
//	{1.528821,42.506919}, //7
//	
//	{1.528921,42.507169}, //2	
//	{1.529021,42.507419}, //3
//	
//	{1.529065,42.507519}, //中心点
//	{1.529110,42.507619}, //中心点
//	{1.529155,42.507719}, //2
//	{1.529200,42.507819}, //3
//	{1.529221,42.507919}, //4
//
//	{1.528421,42.506000}, //中心点
//	{1.528471,42.506219}, //中心点
//	{1.528521,42.506319}, //2
//	{1.528571,42.506419}, //3
//	{1.528621,42.506519}, //4
//	
//	{1.528721,42.506719}, //6
//	{1.528821,42.506919}, //7
//	
//	{1.528921,42.507169}, //2	
//	{1.529021,42.507419}, //3
//	
//	{1.529065,42.507519}, //中心点
//	{1.529110,42.507619}, //中心点
//	{1.529155,42.507719}, //2
//	{1.529200,42.507819}, //3
//	{1.529221,42.507919}, //4


	
	
	
	{1.528421,42.506000}, //中心点
	{1.528171,42.506219}, //中心点
	{1.528221,42.506319}, //2
	{1.528171,42.506419}, //3
	{1.528421,42.506519}, //4
	
	{1.528621,42.506719}, //6
	{1.528321,42.506919}, //7
	
	{1.528521,42.507169}, //2	
	{1.529221,42.507419}, //3
	
	{1.529465,42.507519}, //中心点
	{1.529110,42.507619}, //中心点
	{1.529355,42.507719}, //2
	{1.529100,42.507819}, //3
	{1.529021,42.507919}, //4

	{1.528421,42.506000}, //中心点
	{1.528471,42.506219}, //中心点
	{1.528521,42.506319}, //2
	{1.528571,42.506419}, //3
	{1.528621,42.506519}, //4
	
	{1.528721,42.506719}, //6
	{1.528821,42.506919}, //7
	
	{1.528921,42.507169}, //2	
	{1.529021,42.507419}, //3
	
	{1.529065,42.507519}, //中心点
	{1.529110,42.507619}, //中心点
	{1.529155,42.507719}, //2
	{1.529200,42.507819}, //3
	{1.529221,42.507919}, //4


	
};


//模拟实时GPS定位得到的坐标经纬度
utl_geo_coord_t trk_myself_real_time[] = {
			{1.52892115865814,42.506749111980052}, //中心点
	{1.52852115865814,42.506559111980052}, //中心点
	{1.52847115865814,42.506019111980052}, //2
	{1.52852115865814,42.506419111980052}, //3
	{1.52897115865814,42.506619111980052}, //4
	{1.52837115865814,42.506519111980052}, //5
	{1.52872115865814,42.506019111980052}, //6
	{1.52807115865814,42.506519111980052}, //7
	{1.52837115865814,42.506819111980052}, //2
	{1.52862115865814,42.506119111980052}, //3
	{1.52887115865814,42.506219111980052}, //4
	{1.52807115865814,42.506919111980052}, //5
	{1.52862115865814,42.506219111980052}, //6
	{1.52817115865814,42.506919111980052}, //7
	{1.52857115865814,42.506219111980052}, //2
	{1.52882115865814,42.506419111980052}, //3
	{1.52807115865814,42.506519111980052}, //4
	{1.52817115865814,42.506019111980052}, //5
	{1.52872115865814,42.506919111980052}, //6
	{1.52807115865814,42.506419111980052}, //7
};



#endif



/*********************************************************************
 *
 *  功能描述
 *    判断当前应该显示哪个画布
 *
 **********************************************************************/

int judge_buf_index(int scale)
{
	int canvas_index = 0;
	if (scale == 18 || scale == 15)
	{
		canvas_index = 0;
	}
	else if (scale == 17 || scale == 14)
	{
		canvas_index = 1;
	}
	else if (scale == 16 || scale == 13)
	{
		canvas_index = 2;
	}
	
	#if (CONFIGE_ONE_BUF == 1)

	gps_map_info.show_index = 0;
	gps_map_info.show_scale = scale;
	return 0;

	#else
		
	gps_map_info.show_index = canvas_index;
	gps_map_info.show_scale = scale;
	return canvas_index;
	
	#endif
}

/*********************************************************************
 *
 * @brief 提取出读到的经纬度(用于记录|返航模式时提取相关的经纬度，从轨迹数组trk_idx中提取)
 * @param[out] lon 	读到的经度
 * @param[out] lat 	读到的纬度
 * @retval true 成功, false 失败
 *
 **********************************************************************/

bool rec_get_coord(double *lon, double *lat)
{

	printf("Rec_get_coord:trk_draw_idx = %d ,trk_rec_idx = %d \n", trk_draw_idx, trk_rec_idx);
	if (ptrk == NULL || trk_rec_idx == 0)
	{
		trk_draw_idx = 0;
		return false;
	}
	if (trk_draw_idx < trk_rec_idx)
	{
		*lon = (double)ptrk[trk_draw_idx].lon / 1000000;
		*lat = (double)ptrk[trk_draw_idx].lat / 1000000;
		printf("Rec_get_coord:the_trk_draw_idx = %d,lon = %f,lat = %f\n", trk_draw_idx, *lon, *lat);
		trk_draw_idx++;
		return true;
	}
	else if (trk_draw_idx == trk_rec_idx)
	{
		*lon = m_cur_coord.longitude;
		*lat = m_cur_coord.latitude;

		printf("Rec_get_coord:the_trk_draw_idx = %d,lon = %f,lat = %f\n", trk_draw_idx, *lon, *lat);
		trk_draw_idx++;
		return true;
	}
	else
	{
		trk_draw_idx = 0;
		return false;
	}
}

/*********************************************************************
 *
 *  功能描述
 *    设置地图模式
 *
 *  输入参数
 *    mode：		模式
 *
 **********************************************************************/

void map_set_page_mode(uint8_t mode)
{
	last_m_mode = m_mode;
	m_mode = mode;			//设置地图模式
	printf("last_m_mode:0x%02x,m_mode:0x%02x \n",last_m_mode,m_mode);
	gps_timer_cnt = 0;		//计时器清0
	total_dist = 0;	

	if (m_mode & NAV_MODE || m_mode & REC_MODE)
	{
		//如果是导航模式，解析文件flag置1,导航模式是需要解析文件
		if (mode == NAV_MODE)	
		{
			gpx_parse = true;
		}
		//动态申请一个用于保存轨迹的数组,轨迹数可以自定义
#if 0
		if (ptrk == NULL)		
		{
			ptrk = (trk_pos_t *)map_malloc(sizeof(trk_pos_t) * MAX_TRK_NUM);
			if (ptrk == NULL)
			{
				printf("Malloc track buffer failed!\r\n");
			}
		}
#endif
		trk_idx = 0;
	}
	// 切换模式默认设置正确起点，终点
	revert_pos = false;
}



#if 0
void enlarge_event_callback(lv_event_t *e)
{   
//    lv_obj_t *obj = lv_event_get_target(e);
    lv_event_code_t event = lv_event_get_code(e);
    if (LV_EVENT_CLICKED == event)
    {
    	//只有等当前地图绘制完成，才能进行下次缩放
//    	if(gps_map_info.scale_array_done[scale_count - 13] == 1)
//    	{
			scale_count++;
			if(scale_count > 18)
			{
				scale_count = 18;
			}
			printf("MAP_ENLARGE \n");
			gps_map_info.zoom = 2;
			gps_map_info.show_index = judge_buf_index(scale_count);
			out_of_range = redraw_fun(m_cur_coord, scale_count);
			if(out_of_range)
			{
				for(int i = 0; i < 3;i++)
				{
					gps_map_info.map_ratio[i].m_coord_draw = m_draw_coord;
				}
				redraw_all();
			}
//			map_draw(scale_count);
//			map_status = MAP_STATUS;
//    	} 
    }
    if (LV_EVENT_FOCUSED == event)
    {
        //TODO:
    }

}

void reduce_event_callback(lv_event_t *e)
{   
//    lv_obj_t *obj = lv_event_get_target(e);
    lv_event_code_t event = lv_event_get_code(e);
    if (LV_EVENT_CLICKED == event)
    {
    	//只有等当前地图绘制完成，才能进行下次缩放
//    	if(gps_map_info.scale_array_done[scale_count - 13] == 1)
//    	{
			scale_count--;
			if(scale_count < 13)
			{
				scale_count = 13;
			}
			printf("MAP_REDUCE \n");
			gps_map_info.zoom = 1;
			gps_map_info.show_index = judge_buf_index(scale_count);
			out_of_range = redraw_fun(m_cur_coord, scale_count);
			if(out_of_range)
			{
				for(int i = 0; i < 3;i++)
				{
					gps_map_info.map_ratio[i].m_coord_draw = m_draw_coord;
				}
				redraw_all();
			}
//			map_draw(scale_count);
//			map_status = MAP_STATUS;
//    	}
    }
    if (LV_EVENT_FOCUSED == event)
    {
        //TODO:

    }
}

#endif


//地图放大事件
void enlarge_event_callback()
{   
	scale_count++;
	if(scale_count > 18)
	{
		scale_count = 18;
	}
#if PRINT_MAP_PORT
	printf("MAP_ENLARGE \n");
#endif
	gps_map_info.zoom = 2;
	gps_map_info.show_index = judge_buf_index(scale_count);
	ui_map_remove_other_person_marker();
	out_of_range = redraw_fun(m_cur_coord, scale_count);
	//if(out_of_range)
	if(0)
	{
		for(int i = 0; i < 3;i++)
		{
			gps_map_info.map_ratio[i].m_coord_draw = m_draw_coord;
		}
		redraw_all();
	}
	else
	{
		map_draw(scale_count);
	}
}

//地图缩小事件
void reduce_event_callback()
{   
	scale_count--;
	if(scale_count < 13)
	{
		scale_count = 13;
	}
#if PRINT_MAP_PORT
	printf("MAP_REDUCE \n");
#endif
	gps_map_info.zoom = 1;
	gps_map_info.show_index = judge_buf_index(scale_count);
	out_of_range = redraw_fun(m_cur_coord, scale_count);
	//if(out_of_range)
	if(0)
	{
		for(int i = 0; i < 3;i++)
		{
			gps_map_info.map_ratio[i].m_coord_draw = m_draw_coord;
		}
		redraw_all();
	}
	else
	{
		map_draw(scale_count);
	}
}






/**************************************************************
 * @brief 打开选择的轨迹文件，准备解析文件前的准备工作【暂时没用到】
 *
 * @param trail_path 选择的GPX轨迹文件
 * @return void
 *************************************************************/
void prev_trail_event(char *trail_path)
{
#if PRINT_MAP_PORT
	printf("Pprev_trail_event \n");
#endif
	int click_error = 0;

	//根据trail_path【轨迹文件路径】，打开要解析的GPX轨迹文件
	//extern int8_t xml_lib_open_document_by_name(char *file_name);
	//if (!xml_lib_open_document_by_name(trail_path))
	if(0)
	{
#if PRINT_MAP_PORT
		printf("open_document_by_name_fail \n");
#endif
		click_error = 1;
	}
	else
	{
		click_error = 0;
	}
	
	// 如果选择失败，则不跳转，直到点击成功
	if (click_error == 0)
	{
#if PRINT_MAP_PORT
		printf("Enter_succ! \n");
#endif

		// 设置当前为导航模式用于显示轨迹
		extern void map_set_page_mode(uint8_t mode);
		map_set_page_mode(2);

		// 设置当前进入地貌模式
		void set_map_show_mode(int mode);
		set_map_show_mode(0);

		// UI的刷新变量复位
		map_status = IDLE_STATUS;	
	}
}

extern bool m_is_busy;
/**************************************************************
 * @brief 展示轨迹文件的触摸回调函数
 *
 * @param e 按钮触摸事件
 * @return 重绘新比例尺的地图
 *************************************************************/

int map_event_handler_show_trk(char* trk_file)
{
	printf("TEST+get_gpx_name = %s \n",get_gpx_name());
	if(0 == strcmp(trk_file,get_gpx_name()))
	{
		//如果准备点击的文件名与正在记录的文件名一样，则不能打开
		return 0;
	}

	// 设置轨迹描绘进入标志
    track_drawing_active = 1;
	printf("M:map_event_handler_show_trk \n");
	//打开文件
	set_nav_gpx_name(trk_file); //这个参数里面就传入要打开的轨迹文件路径【绝对路径】

	//看看文件是否损坏或不存在
	xml_lib_file_t tmp_file; //对应的是GPX文件
	struct stat file_stat;
	printf("Tset open file %s \n", trk_file);
	tmp_file = xml_lib_fopen(trk_file, O_RDONLY);
	if (tmp_file < 0) {
		printf("trk::could not open %s \n", trk_file);
		int close_ret = xml_lib_fclose(tmp_file);
		tmp_file = 0;
		return 0;
	}
	else
	{
		//获取文件是否为空
		if(-1 == stat(trk_file,&file_stat))
		{
			printf("stat error! \n");
			int close_ret = xml_lib_fclose(tmp_file);
			tmp_file = 0;
			return 0;
		}
		else
		{
			printf("file_stat.st_size = %d \n",file_stat.st_size);
			if(0 == file_stat.st_size)
			{
				int close_ret = xml_lib_fclose(tmp_file);
				tmp_file = 0;
				return 0;
			}
		}
	}

	//设置当前地图模式为导航
	map_set_page_mode(NAV_MODE);

	//清空所有比例尺绘制信息
	memset(gps_map_info.scale_array_done, 0, sizeof(gps_map_info.scale_array_done));


	//获取轨迹边界
	double min_lat,min_lon,max_lat,max_lon;
	if(trk_get_border_new(&min_lat,&min_lon,&max_lat,&max_lon))
	{
		m_draw_coord.latitude = (min_lat + max_lat) / 2;
		m_draw_coord.longitude = (min_lon + max_lon) / 2;
		printf("M:m_draw_coord.lon=%f,m_draw_coord.lat=%f \n",m_draw_coord.longitude,m_draw_coord.latitude);
		scale_count = mlp_calc_scale(min_lat,min_lon,max_lat,max_lon,gfx_wdth);
		//如果计算得出的比例尺小于SCALE_1_1KM，则设置为1km比例尺
		if(scale_count < SCALE_1_1KM)
		{
			scale_count = SCALE_1_1KM;
		}
printf("TEST_0523:scale_count=%d/n",scale_count);
		gps_map_info.show_index = judge_buf_index(scale_count);
	}

	out_of_range = 1;
	m_data_ready |= 0x04;
	m_is_busy = false;

	gps_timer_cnt = 0;

	//假设定到位了
	gps_fixed = true;
	map_draw(scale_count);





	return 1;
}



/*********************************************************************
 *
 *  功能描述
 *    判断当前点是否在安全范围(这个MAP_MOVE宏适用于466屏幕,可以直接返回1)
 *
 **********************************************************************/
int redraw_fun(utl_geo_coord_t cur_coord,int scale)
{

	#if (MAP_CUR_CENTER == 1)

	return 1;

	#endif

	printf("redraw_fun: cur_coord.lon=%f,lat=%f ,scale-13=%d ,scale_array_done = %d \n ",cur_coord.longitude,cur_coord.latitude,scale-13,gps_map_info.scale_array_done[scale-13]);
	//如果当前地图从没绘制过，则不进行判断
	if(gps_map_info.scale_array_done[scale-13] == 0)
	{
		return 0;
	}
	
	gfx_point_t point = {0};
	map_coord_get_pos(cur_coord, &point);
	
	int point_x = point.x - 23;
	int point_y = (m_rect.top + m_rect.height) - point.y - 25;

	printf("redraw_fun: point_x = %d ,point_y = %d \n", point_x, point_y);
	if(point_x <=100 || point_x >= 500 || point_y <= 100 || point_y >= 500)
	{
		printf("redraw_fun: YES! \n");
		return 1;
	}
	return 0;

}


/**************************************************************
 * @brief 清空所有地图绘制信息重新绘制
 *
 * @return 
 *************************************************************/
void redraw_all()
{
	printf("ZA:redraw_all \n");
	//****************** 清空比例尺绘制信息***********************
	memset(gps_map_info.scale_array_done, 0, sizeof(gps_map_info.scale_array_done));
	out_of_range = 1;	//466*466的用不上，可以暂时全置为1
	map_draw(scale_count);
	//map_status = MAP_STATUS;
}


 /**************************************************************
 * @brief 结束轨迹记录的触摸回调函数（适用于记录模式|导航模式|返航模式）
 *
 * @param e 按钮触摸事件
 * @return 结束轨迹记录的函数
 *************************************************************/
void record_mode_stop_handle()
{
#if PRINT_MAP_PORT
	//printf("record_mode_stop_handle \n");
#endif
	// 如果是记录模式
	if (m_mode & REC_MODE)
	{

	}
	else if (m_mode & NAV_MODE)// 如果是导航模式
	{

	}
	else if (m_mode & RET_MODE)// 如果是返航模式
	{

	}

	char *filename = "/victel/resource/track/over.GPX";

	trk_pos_t cur_ptrk;
	cur_ptrk.lon = m_cur_coord.longitude * 1000000;
	cur_ptrk.lat = m_cur_coord.latitude * 1000000;

	printf("record_mode_stop_handlecur_ptrk.lon=%d,lat=%d \n",cur_ptrk.lon,cur_ptrk.lat);

	//把当前经纬度存入文件中(最后一个点)
	start_trk_record(1,cur_ptrk);	
		

	//保存轨迹数组，并将其写入创建的GPX文件中
	//if (trk_write(filename, ptrk, trk_idx))
	if(1)
	{
		//弹出轨迹保存成功提示
	}
	else
	{
		//弹出轨迹保存失败提示
	}

	// 轨迹数清零，防止第二次记录时弹出异常轨迹
	trk_rec_idx = 0; 
	trk_idx = 0;
	num_of_tra = 0;
	gps_timer_cnt = 0;

	//if(ptrk)
	//{
	//	map_free(ptrk);
	//	ptrk = NULL;
	//}

	if(trk_point_array)
	{
		map_free(trk_point_array);
		trk_point_array = NULL;
	}

	//关闭轨迹文件
	trk_close();

//	map_timer_stop();
	extern void clear_start_end_pos();
	clear_start_end_pos();

	//extern void show_map_track();
	//show_map_track();

	printf("ZA:Ready => redraw_all \n");
	map_status = IDLE_STATUS;
	//地图设置为普通模式，不然会一直记录
	m_mode = POS_MODE;
	m_data_ready = 0;
	redraw_all();
//	ui_view_paint2(MAP_VIEW, 0, NULL);

}


 /**************************************************************
 * @brief 结束轨迹查看的触摸回调函数（适用于导航模式）
 *
 * @param e 按钮触摸事件
 * @return 结束轨迹记录的函数
 *************************************************************/
void nav_mode_stop_handle()
{
	printf("nav_mode_stop_handle:last_m_mode=0x%02x \n",last_m_mode);

	    // 设置轨迹退出标志
    track_drawing_active = 0;

	//关闭轨迹文件
	trk_close();
	//如果上一个模式是记录模式，则退出导航模式后，需要返回到记录模式
	if(last_m_mode == REC_MODE)
	{
		//地图设置为普通模式，不然会一直记录
		//设置当前地图模式为记录
		map_set_page_mode(REC_MODE);
	}
	else
	{
			
		// 轨迹数清零，防止第二次记录时弹出异常轨迹
		trk_rec_idx = 0; 
		trk_idx = 0;
		num_of_tra = 0;
		gps_timer_cnt = 0;
		//地图设置为普通模式，不然会一直记录
		m_mode = POS_MODE;
	}



	if(trk_point_array)
	{
		map_free(trk_point_array);
		trk_point_array = NULL;
	}

	extern void clear_start_end_pos();
	clear_start_end_pos();

	printf("ZA:Ready => redraw_all \n");
	map_status = IDLE_STATUS;
	
	m_data_ready = 0;
	if(scale_count < 13)
	{
		scale_count = 13;
		gps_map_info.show_index = judge_buf_index(scale_count);
	}
	redraw_all();

	return ;
}

 /**************************************************************
 * @brief 结束轨迹记录的触摸回调函数[直接打开另一个轨迹]
 *
 * @param e 按钮触摸事件
 * @return 结束轨迹记录的函数
 *************************************************************/
void record_mode_stop_handle_2()
{
	//printf("record_mode_stop_handle \n");
	// 如果是记录模式
	if (m_mode & REC_MODE)
	{

	}
	else if (m_mode & NAV_MODE)// 如果是导航模式
	{

	}
	else if (m_mode & RET_MODE)// 如果是返航模式
	{

	}

	char *filename = "/NAND:/MAP/track/over.GPX";
		
	ptrk[trk_idx].lat = (int32_t)(trk_myself[trk_idx].latitude * 1000000);
	ptrk[trk_idx].lon = (int32_t)(trk_myself[trk_idx].longitude * 1000000);
	trk_idx++;
	trk_rec_idx = trk_idx;
	num_of_tra = trk_rec_idx;

	printf("*the_trk_idx = %d \n", trk_idx);

	//保存轨迹数组，并将其写入创建的GPX文件中
	//if (trk_write(filename, ptrk, trk_idx))
	if(1)
	{
		//弹出轨迹保存成功提示
	}
	else
	{
		//弹出轨迹保存失败提示
	}

	// 轨迹数清零，防止第二次记录时弹出异常轨迹
	trk_rec_idx = 0; 
	trk_idx = 0;
	num_of_tra = 0;
	gps_timer_cnt = 0;

	if(ptrk)
	{
		map_free(ptrk);
		ptrk = NULL;
	}

	if(trk_point_array)
	{
		map_free(trk_point_array);
		trk_point_array = NULL;
	}

	//关闭轨迹文件
	//trk_close();

//	map_timer_stop();
	extern void clear_start_end_pos();
	clear_start_end_pos();

#if PRINT_MAP_PORT
	printf("map_task_close_v2 \n");
#endif
	//map_task_close_v2();

	//****************** 清空比例尺绘制信息***********************
	memset(gps_map_info.scale_array_done, 0, sizeof(gps_map_info.scale_array_done));
	out_of_range = 1;	
	map_status = MAP_STATUS;

#if PRINT_MAP_PORT
	printf("show_map_track \n");
#endif
//	extern void show_map_track();
//	show_map_track();
}





void track_search_folder(char *dirname) {

    // 检查目录是否存在
    struct stat info;
    if (stat(dirname, &info) != 0) {
        // 目录不存在
        if (mkdir(dirname, 0777) != 0) {
            // 创建目录失败
            perror("mkdir");
			return;
        }
        printf("Directory '%s' created successfully.\n", dirname);
    } else if (info.st_mode & S_IFDIR) {
        // 目录存在
        printf("Directory '%s' already exists.\n", dirname);
    }
}

#if 0
#define FM_TRACK_PREFIX "TRACK_"
static char *UIGenTrackFileNameWithSeq(char *buff, uint32_t buffSize)
{
    static int64_t seq = 0;

    return FmGenFileNameWithSeq1(TRACK_FOLDER, FM_TRACK_PREFIX, TRACK_FILE_SUFF,
                                 &seq, buff, buffSize);
}
#endif

/**************************************************************
 * @brief 开始记录轨迹的触摸回调函数
 *
 * @param e 按钮触摸事件
 * @return 开始地图的记录轨迹
 *************************************************************/
void record_mode_start_handle()
{
	//设记录模式下保存轨迹的文件名
	char file_name[128] = "/victel/resource/track/record";
	// UIGenTrackFileNameWithSeq(file_name,sizeof(file_name));

	// 要检查或创建的目录名称
	char *dirname = "/victel/resource/track";  
	track_search_folder(dirname);

//	map_timer_create();
	printf("record_mode_start_handle \n");

	set_gpx_name(file_name);
#if 0
	 //设置当前模式为记录模式
	 if(res_data_map->rtl_flag == 1)
	 	map_set_page_mode(RET_MODE);
		trk_idx++;
		trk_rec_idx = trk_idx;
		num_of_tra = trk_rec_idx;
	 else
	 	map_set_page_mode(REC_MODE);
		gps_timer_cnt = 0;
#else
	map_set_page_mode(REC_MODE);
#endif

	
	//刷新地图
	gps_fixed = true;

	redraw_all();
	////////////ui_view_paint2(MAP_VIEW, 0, NULL);
	//set_rec_starttime();
}


 /**************************************************************
 * @brief 开始轨迹返航的触摸回调函数(先有记录模式才会有返航模式)
 *
 * @param e 按钮触摸事件
 * @return 开始地图的轨迹返航
 *************************************************************/
void return_mode_start_handle()
{
	printf("return_mode_start_handle \n");
	//设置当前模式为返航模式
	map_set_page_mode(RET_MODE);
	
		char *filename = "/NAND:/MAP/track/over.GPX";

	trk_pos_t cur_ptrk;
	cur_ptrk.lon = m_cur_coord.longitude * 1000000;
	cur_ptrk.lat = m_cur_coord.latitude * 1000000;

	printf("record_mode_stop_handlecur_ptrk.lon=%d,lat=%d \n",cur_ptrk.lon,cur_ptrk.lat);

	//把当前经纬度存入文件中(最后一个点)
	int write_num = start_trk_record(1,cur_ptrk);	
	trk_idx = write_num;
	trk_rec_idx = trk_idx;
	num_of_tra = trk_rec_idx;
	
	//刷新地图
	redraw_all();
	gps_fixed = true;

	extern void map_draw_nav();
	map_draw_nav();

}


 /**************************************************************
 * @brief 回到当前点触摸回调函数
 *
 * @param e 按钮触摸事件
 * @return 开始地图的轨迹返航
 *************************************************************/
void return_cur_pos_handle()
{
	printf("return_cur_pos_handle \n");

	//把当前点设置为地图中心点
	m_draw_coord = m_cur_coord;
	
	redraw_all();

	//刷新地图
//	sys_map_event_report(1, 0);
//	canvas_reflash();
//	ui_view_paint2(MAP_VIEW, 0, NULL);

}



 
 /*******************************************
 *
 * @brief 清空地图数据
 *
 ********************************************/
 
 void clrae_all_map()
 {
	 //地图状态重置
	 map_status = IDLE_STATUS;
	 //地图模式重置
	 m_mode = POS_MODE;
	 //超出边缘flag重置
	 out_of_range = 1;
	 
	 //****************** 清空比例尺绘制信息***********************
	 memset(gps_map_info.scale_array_done, 0, sizeof(gps_map_info.scale_array_done));
 
 }


 /*******************************************
  *
  * @brief 清除掉当前所有的模式（用于切换地图展示模式用）
  *
  *******************************************/
 void clear_cur_all_mode()
 {
	 printf("clear_cur_all_mode \n");
 
	 // 中断掉当前绘制
	 //extern void map_task_close_v2(void);
	 //map_task_close_v2();
 
	 // 重置地图刷新
	 clrae_all_map();
 
	 m_data_ready = 0;
 
	 // 计数器清0
	 gps_timer_cnt = 0;
	 gps_fixed = false;
 
 }


 /*******************************************
  * @brief 等高线模式按钮（开启画等高线模式）
  *******************************************/
void _btn_evt_handler_change_mode_contour(void)
 {
	 printf("*_btn_evt_handler_change_mode_contour \n");
	 // 隐藏必要的控件
 
	 // 功能代码部分
	 clear_cur_all_mode();
	 clrae_all_map();

//	 contour_uinit();
 
	 set_map_show_mode(CONTOUR_MODE);
	 gps_map_info.map_show_mode = get_map_show_mode();
 	
	 map_draw(scale_count);
 
 }
 
 /*******************************************
  * @brief 混合模式按钮
  *******************************************/
void _btn_evt_handler_change_mode_mix(void)
 {
	 printf("*_btn_evt_handler_change_mode_mix \n");
	 
	 // 隐藏必要的控件
 
	 // 功能代码部分
	 clear_cur_all_mode();
	 clrae_all_map();

//	 contour_uinit();
 
	 set_map_show_mode(MIX_MODE);
	 gps_map_info.map_show_mode = get_map_show_mode();
 
	 map_draw(scale_count);
 
 }
 
 /*******************************************
  * @brief 地貌模式按钮
  *******************************************/
 void _btn_evt_handler_change_mode_rouline(void)
 {
	 printf("*_btn_evt_handler_change_mode_rouline \n");
	 // 隐藏必要的控件
 
	 // 功能代码部分
	 clear_cur_all_mode();
	 clrae_all_map();

//	 contour_uinit();
 
	 set_map_show_mode(ROUTINE_MODE);
	 gps_map_info.map_show_mode = get_map_show_mode();
 
	 map_draw(scale_count);
 
 }




 /*********************************************************************
 *
 *  功能描述
 *    导航时轨迹是否偏移
 *
 **********************************************************************/

  /*********************************************************************
  *
  *  功能描述
  *    偏移当前点的位置到轨迹上
  *
  **********************************************************************/
 






 /**************************************************************
 * @brief 切换导航时轨迹起点的触摸回调函数
 *
 * @param e 按钮触摸事件
 * @return 切换轨迹的起点终点
 *************************************************************/
void change_start_point_handle()
{
	printf("change_start_point_handle \n");

//	map_status = MAP_STATUS;
	revert_pos = !revert_pos;
	
	//刷新地图
//	ui_view_paint2(MAP_VIEW, 0, NULL);
//	map_draw(scale_count);
	redraw_all();

}


utl_geo_coord_t trk_myself_time[] = {
{113.919674,22.581398},
{113.919674,22.585894},
{113.919827,22.585892},
{113.919980,22.585886},
{113.920132,22.585874},
{113.920284,22.585859},
{113.920436,22.585839},
{113.920586,22.585815},
{113.920736,22.585786},
{113.920885,22.585753},
{113.921033,22.585716},
{113.921179,22.585674},
{113.921324,22.585629},
{113.921467,22.585579},
{113.921608,22.585525},
{113.921747,22.585466},
{113.921885,22.585404},
{113.922020,22.585338},
{113.922153,22.585268},
{113.922283,22.585194},
{113.922411,22.585117},
{113.922536,22.585036},
{113.922659,22.584951},
{113.922778,22.584862},
{113.922894,22.584771},
{113.923008,22.584676},
{113.923117,22.584577},
{113.923224,22.584476},
{113.923327,22.584371},
{113.923426,22.584264},
{113.923522,22.584154},
{113.923614,22.584041},
{113.923702,22.583925},
{113.923786,22.583807},
{113.923866,22.583687},
{113.923941,22.583564},
{113.924013,22.583439},
{113.924080,22.583312},
{113.924143,22.583184},
{113.924202,22.583053},
{113.924256,22.582921},
{113.924306,22.582787},
{113.924350,22.582652},
{113.924391,22.582516},
{113.924427,22.582379},
{113.924458,22.582240},
{113.924484,22.582101},
{113.924505,22.581961},
{113.924522,22.581821},
{113.924534,22.581680},
{113.924541,22.581539},
{113.924544,22.581398},
{113.924541,22.581257},
{113.924534,22.581115},
{113.924522,22.580975},
{113.924505,22.580834},
{113.924484,22.580694},
{113.924458,22.580555},
{113.924427,22.580417},
{113.924391,22.580280},
{113.924350,22.580143},
{113.924306,22.580008},
{113.924256,22.579875},
{113.924202,22.579742},
{113.924143,22.579612},
{113.924080,22.579483},
{113.924013,22.579356},
{113.923941,22.579232},
{113.923866,22.579109},
{113.923786,22.578988},
{113.923702,22.578870},
{113.923614,22.578755},
{113.923522,22.578642},
{113.923426,22.578532},
{113.923327,22.578424},
{113.923224,22.578320},
{113.923117,22.578218},
{113.923008,22.578120},
{113.922894,22.578025},
{113.922778,22.577933},
{113.922659,22.577845},
{113.922536,22.577760},
{113.922411,22.577679},
{113.922283,22.577601},
{113.922153,22.577527},
{113.922020,22.577457},
{113.921885,22.577391},
{113.921747,22.577329},
{113.921608,22.577271},
{113.921467,22.577217},
{113.921324,22.577167},
{113.921179,22.577121},
{113.921033,22.577080},
{113.920885,22.577042},
{113.920736,22.577009},
{113.920586,22.576981},
{113.920436,22.576957},
{113.920284,22.576937},
{113.920132,22.576921},
{113.919980,22.576910},
{113.919827,22.576903},
{113.919674,22.576901},
{113.919521,22.576903},
{113.919368,22.576910},
{113.919216,22.576921},
{113.919064,22.576937},
{113.918912,22.576957},
{113.918761,22.576981},
{113.918612,22.577009},
{113.918463,22.577042},
{113.918315,22.577080},
{113.918169,22.577121},
{113.918024,22.577167},
{113.917881,22.577217},
{113.917740,22.577271},
{113.917600,22.577329},
{113.917463,22.577391},
{113.917328,22.577457},
{113.917195,22.577527},
{113.917064,22.577601},
{113.916937,22.577679},
{113.916811,22.577760},
{113.916689,22.577845},
{113.916570,22.577933},
{113.916453,22.578025},
{113.916340,22.578120},
{113.916230,22.578218},
{113.916124,22.578320},
{113.916021,22.578424},
{113.915922,22.578532},
{113.915826,22.578642},
{113.915734,22.578755},
{113.915646,22.578870},
{113.915562,22.578988},
{113.915482,22.579109},
{113.915406,22.579232},
{113.915335,22.579356},
{113.915267,22.579483},
{113.915204,22.579612},
{113.915146,22.579742},
{113.915092,22.579875},
{113.915042,22.580008},
{113.914997,22.580143},
{113.914957,22.580280},
{113.914921,22.580417},
{113.914890,22.580555},
{113.914864,22.580694},
{113.914842,22.580834},
{113.914826,22.580975},
{113.914814,22.581115},
{113.914806,22.581257},
{113.914804,22.581398},
{113.914806,22.581539},
{113.914814,22.581680},
{113.914826,22.581821},
{113.914842,22.581961},
{113.914864,22.582101},
{113.914890,22.582240},
{113.914921,22.582379},
{113.914957,22.582516},
{113.914997,22.582652},
{113.915042,22.582787},
{113.915092,22.582921},
{113.915146,22.583053},
{113.915204,22.583184},
{113.915267,22.583312},
{113.915335,22.583439},
{113.915406,22.583564},
{113.915482,22.583687},
{113.915562,22.583807},
{113.915646,22.583925},
{113.915734,22.584041},
{113.915826,22.584154},
{113.915922,22.584264},
{113.916021,22.584371},
{113.916124,22.584476},
{113.916230,22.584577},
{113.916340,22.584676},
{113.916453,22.584771},
{113.916570,22.584862},
{113.916689,22.584951},
{113.916811,22.585036},
{113.916937,22.585117},
{113.917064,22.585194},
{113.917195,22.585268},
{113.917328,22.585338},
{113.917463,22.585404},
{113.917600,22.585466},
{113.917740,22.585525},
{113.917881,22.585579},
{113.918024,22.585629},
{113.918169,22.585674},
{113.918315,22.585716},
{113.918463,22.585753},
{113.918612,22.585786},
{113.918761,22.585815},
{113.918912,22.585839},
{113.919064,22.585859},
{113.919216,22.585874},
{113.919368,22.585886},
{113.919521,22.585892},

};


extern void get_cur_coord(double* lon ,double* lat);


//模拟轨迹计数
int trk_count = 0;



/*********************************************************************
 *
 *  功能描述
 *    获取当前经纬度数据（目前是模拟，正式版本需要赋值真实数据）
 *
 *
 **********************************************************************/
utl_geo_coord_t get_cur_gps(int *realData)
{
    utl_geo_coord_t cur_gps_point = {.latitude = 0.0, .longitude = 0.0};
/*
	cur_gps_point.latitude = param_get_latitude();
	cur_gps_point.longitude = param_get_longitude();
*/
#if 1  // 只是测试，需还原
    if (cur_gps_point.longitude == 0.0 && cur_gps_point.latitude == 0.0) {
        if (realData) *realData = 0;
        cur_gps_point.longitude = g_defLon;
        cur_gps_point.latitude = g_defLat;
    } else {
        if (realData) *realData = 1;
    }
#else
    // 给的模拟数组的值
    cur_gps_point.longitude = trk_myself_time[trk_count].longitude;
    cur_gps_point.latitude = trk_myself_time[trk_count].latitude;
#endif

#if PRINT_MAP_PORT
    printf("cur_gps_point_location: %f,%f,trk_idx=%d \n",
           cur_gps_point.latitude, cur_gps_point.longitude, trk_idx);
#endif

    return cur_gps_point;
}



#if 1


/*********************************************************************
 *
 *  功能描述
 *    获取当前经纬度数据（目前是模拟，正式版本需要赋值真实数据）
 *
 *
 **********************************************************************/

//utl_geo_coord_t get_cur_gps(void)
//{
////	utl_geo_coord_t cur_gps_point;
//
//	
//
////	printf("cur_gps_point_location: %f,%f,trk_idx=%d \n", cur_gps_point.latitude, cur_gps_point.longitude,trk_idx);
//	
////	return cur_gps_point;
//}

static uint8_t volatile g_needReloadMapFile = 0;
void GuiMapSetNeedReloadFile()
{
    g_needReloadMapFile = 1;
}


int GuiMapSetDefLocation(double lon, double lat)
{
    g_defLon = lon;
    g_defLat = lat;

    FILE *file = fopen(UI_MAP_LOCATION_FILE, "w");
    if (!file) {
        printf("[%s:%d] [error] save def location failed!!!!\n", __func__,
               __LINE__);
        perror("Failed to open file");
        return -1;
    }

    fprintf(file, "lon:%lf\r\nlat:%lf\r\n", lon, lat);
	fflush(file);
	fdatasync(fileno(file));
    fclose(file);
    return 0;
}


int InitLocationData()
{
    if (access(UI_MAP_LOCATION_FILE, F_OK) != 0) {
        printf("[%s:%d] [warn] file:%s not exist!!!!\n", __func__, __LINE__,
               UI_MAP_LOCATION_FILE);
        return -1;
    }

    FILE *file = fopen(UI_MAP_LOCATION_FILE, "r");
    if (!file) {
        printf("[%s:%d] [error] save def location failed!!!!\n", __func__,
               __LINE__);
        perror("Failed to open file");
        return -1;
    }

    char line[128];
    double lon = 0, lat = 0;
    int hasBeenGet = 0;
    while (fgets(line, sizeof(line), file)) {
        if (sscanf(line, "lon:%lf", &lon) == 1) {
            hasBeenGet = 1;
            continue;
        }
        if (sscanf(line, "lat:%lf", &lat) == 1) {
            hasBeenGet = 1;
            continue;
        }
    }
    fclose(file);

    if (hasBeenGet) {
		m_draw_coord.longitude = lon;
		m_draw_coord.latitude = lat;
		m_cur_coord.longitude = lon;
		m_cur_coord.latitude = lat;
        g_defLon = lon;
        g_defLat = lat;
        printf("[%s:%d] init location lon:%lf, lat:%lf\n", __func__, __LINE__,
               lon, lat);
    }
    return 0;
}



void on_gps_timer1(void)
{
	//return ;
	gfx_point_t pos = {0,0};
	bool redraw = false;
	int isRealGpsData = 0;
	static uint8_t s_hasBeenUpdateGps = 0;
	static uint8_t s_hasBeenReloadMapFile = 0;


	static utl_geo_coord_t last_coord = {0};
    static uint32_t last_update_time = 0;
    static uint32_t no_update_count = 0;

	m_cur_coord = get_cur_gps(&isRealGpsData);
	//打印下当前坐标
	// printf("cur location: %f,%f\n", m_cur_coord.latitude, m_cur_coord.longitude);

	//打印track_drawing_active的值
	// printf("--------------------------track_drawing_active: %d\n", track_drawing_active);

    // 检查坐标是否更新
    if(m_cur_coord.latitude != last_coord.latitude || 
       m_cur_coord.longitude != last_coord.longitude || get_gps_back_status() || track_drawing_active) {
		// printf("-------- %d\n", __LINE__);

		// printf("Map reset requested\n");
        reset_gps_back_status();

        last_coord = m_cur_coord;
        last_update_time = lv_tick_get();
        no_update_count = 0;
    } else {
		// printf("-------- %d\n", __LINE__);
        no_update_count++;
        // 如果2-3秒(2000-3000ms)没有更新则返回
        if(lv_tick_elaps(last_update_time) > 2000 ) {
            return;
        }
    }

	trk_count++;
	gps_timer_cnt++;
	printf("gps_timer_cnt = %d \n",gps_timer_cnt);
	//如果定到位了
    if (gps_fixed)
    {

		//-------------------------------------------------

		//将获取到的现在的经纬度保存到m_cur_coord(这里需要自己写，demo只是模拟的)
		//用于模拟记录轨迹与循迹返航时，当前位置的移动
		m_draw_coord = get_cur_gps(&isRealGpsData);
		m_cur_coord = get_cur_gps(&isRealGpsData);
#if PRINT_MAP_PORT
		printf("new location: %f,%f\n", m_cur_coord.latitude, m_cur_coord.longitude);
#endif

        if (isRealGpsData && !s_hasBeenUpdateGps) {
            // gps 更新后需要重新加载下地图文件
            printf("[%s:%d] [info] first update gps , need reload file!!!!\n",
                   __func__, __LINE__);
            s_hasBeenUpdateGps = 1;
            g_needReloadMapFile = 1;
        }

        // 检测是否需要重新加载地图文件
        if (g_needReloadMapFile) {
            if (gps_check_cur_coord(m_cur_coord, 1)) {
                printf("[%s:%d] [info] reload map successfule!!!!\n", __func__,
                       __LINE__);
                g_needReloadMapFile = 0;
            } else {
                // 找不到对应文件

                // 隔段时间打印下错误
                if (gps_timer_cnt % 500 == 0) {
                    printf(
                        "[%s:%d] [error] not match map file, need "
                        "download!!!!\n",
                        __func__, __LINE__);
                }
            }
        }

        switch((m_mode & (~VIW_MODE))) {
            case POS_MODE:

			/*************************************************************
		    *				        实时GPS模式
		    *************************************************************/
				m_mode = POS_MODE;
                //  15 seconds
                if((gps_timer_cnt % REDRAW_TIME) == 0) {
					//trk_idx++;
                }
		
                break;
		
		    /*************************************************************
		    *				        导航模式
		    *************************************************************/
            case NAV_MODE:
				m_mode = NAV_MODE;
                //  15 seconds
                printf("iqjodhwqui \n");
                if((gps_timer_cnt % REDRAW_TIME) == 0) {
					if(trk_idx < MAX_TRK_NUM) {
						printf("t58tu438ry9 \n");
						//ptrk[trk_idx].lat = (int32_t)(m_cur_coord.latitude * 1000000);
	                    //ptrk[trk_idx].lon = (int32_t)(m_cur_coord.longitude * 1000000);

	                    //trk_idx++;
								
                }

					
                }
                break;
				
			/*************************************************************
		    *				        记录模式
		    *************************************************************/
            case REC_MODE:
                //  15 seconds
				m_mode = REC_MODE;
                if((gps_timer_cnt % REDRAW_TIME) == 0) {

				 if(trk_idx < MAX_TRK_NUM) {
                        //ptrk[trk_idx].lat = (int32_t)(m_cur_coord.latitude * 1000000);
                        //ptrk[trk_idx].lon = (int32_t)(m_cur_coord.longitude * 1000000);

                        // trk_idx++;
                        // trk_rec_idx = trk_idx;
						// num_of_tra = trk_rec_idx;
						//printf("SSS_trk_rec_idx = %d ,trk_idx = %d \n",trk_rec_idx,trk_idx);

                    }				
                }
                break;

			/*************************************************************
		    *				        返航模式
		    *************************************************************/
            case RET_MODE:
                //  15 seconds
                m_mode = RET_MODE;;
                if((gps_timer_cnt % REDRAW_TIME) == 0) {
				
				if(trk_idx < MAX_TRK_NUM) {
					//ptrk[trk_idx].lat = (int32_t)(m_cur_coord.latitude * 1000000);
                    //ptrk[trk_idx].lon = (int32_t)(m_cur_coord.longitude * 1000000);
                    // trk_idx++;
                }			
            }
                break;
            default:
                break;
        }

		//整理当前的经纬度，准备将其存入文件中
		trk_pos_t cur_ptrk;
		cur_ptrk.lon = m_cur_coord.longitude * 1000000;
		cur_ptrk.lat = m_cur_coord.latitude * 1000000;

		
		//printf("On_gps_timer:map_status = %d \n",map_status);
//		if(map_status == IDLE_STATUS || map_status == FINISH_STATUS)
        {
            map_coord_get_pos(m_cur_coord, &pos);
			//printf("On_gps_timer:cur_pos.x = %d ,y = %d \n",pos.x,pos.y);
			//需要有位移，才能
			if(((abs(pos.x - gfx_wdth/2) > 3) || (abs(pos.y - gfx_hght/2) >3 ) || real_time_gps_draw) && ((gps_timer_cnt % REDRAW_TIME) == 1) && (gps_timer_cnt >= REDRAW_TIME)) 
			{
                redraw = true;
			}
			if(redraw) 
			{
				//如果是记录模式，或者是返航模式，或者是导航模式,则记录轨迹到文件中
				if((REC_MODE == m_mode) || (RET_MODE == m_mode) || (NAV_MODE == m_mode))
				{
					//printf("ADD:cur_ptrk.lon=%d,lat=%d \n",cur_ptrk.lon,cur_ptrk.lat);
					//把当前经纬度存入文件中
					int write_nums = start_trk_record(gps_timer_cnt,cur_ptrk);

					if(REC_MODE == m_mode)
					{
						 trk_idx = write_nums;
                         trk_rec_idx = trk_idx;
						 num_of_tra = trk_rec_idx;
						 //printf("ZA:trk_idx = %d \n",trk_idx);
					}
					else if((RET_MODE == m_mode) || (NAV_MODE == m_mode))
					{
						//trk_idx++;
					}
				}
				if(m_mode == REC_MODE || m_mode == POS_MODE)
				{
				 	out_of_range = redraw_fun(m_cur_coord, scale_count);
					printf("nnnn_out_of_range = %d \n",out_of_range);
					if(out_of_range)
					{
						memset(gps_map_info.scale_array_done, 0, sizeof(gps_map_info.scale_array_done));
					}
					
				}
				else if(m_mode == RET_MODE || m_mode == NAV_MODE)
				{
					out_of_range = redraw_fun(m_cur_coord, scale_count);
					printf("nnnn_out_of_range = %d \n",out_of_range);
					memset(gps_map_info.scale_array_done, 0, sizeof(gps_map_info.scale_array_done));
				}

				map_draw(scale_count);
            }
        }


		
    }
}









void set_map_mode(uint8_t mode)
{
	m_mode = mode;
}

uint8_t get_map_mode(void)
{
	return m_mode;
}




static uint8_t cur_page = 0;

void set_map_page(uint8_t page)
{
	cur_page = page;
}

uint8_t get_map_page(void)
{
	return cur_page;
}

void map_cleanup(void)
{
    printf("开始清理地图资源和状态...\n");

    // 0. 首先停止所有相关线程
    track_drawing_active = 0;  // 停止轨迹描绘线程

    // 在释放资源前等待所有地图相关线程安全退出
    // 等待地图任务线程完成
    printf("等待地图线程安全退出...\n");
    
    // 1. 中断当前绘制操作
    map_task_close_v2();

	map_license_task_close();
    
    // 2. 清除状态标志
    map_status = IDLE_STATUS;
    m_mode = POS_MODE;
    last_m_mode = POS_MODE;
    out_of_range = 1;
    m_data_ready = 0;
    gps_timer_cnt = 0;
    timer_redraw_cnt = 0;
	record_track_en = 0;
    gps_fixed = false;
    real_time_gps_draw = 1;
    
    // 3. 清空比例尺绘制信息
    memset(gps_map_info.scale_array_done, 0, sizeof(gps_map_info.scale_array_done));
    
    // 4. 清理所有画布的建筑物名称和等高线数据
    for (int i = 0; i < MPS_MAP_BUFF_MAX_NUM; i++) {
        clean_map_building_contour(i);
    }
    
    // 5. 关闭所有打开的地图文件
    map_close_files();
    
    // 6. 完全清理轨迹相关状态和内存
    trk_cleanup();  // 调用轨迹模块的完整清理函数

    clear_start_end_pos();
	ui_map_remove_other_person_marker();

    // 7. 重置map_port.c中的轨迹索引和计数器
    trk_rec_idx = 0;
    trk_idx = 0;
    num_of_tra = 0;
    revert_pos = false;
    gpx_parse = false;
    out_track_flag = 0;

    // 8. 释放map_port.c中的轨迹相关内存
    if (ptrk) {
        map_free(ptrk);
        ptrk = NULL;
    }

    if (trk_point_array) {
        map_free(trk_point_array);
        trk_point_array = NULL;
    }
    
    // 9. 重置gps_map_info结构体中的状态
    gps_map_info.is_updata_done = 0;
    gps_map_info.draw_index = 0;
    gps_map_info.draw_scale = 0;
    gps_map_info.is_need_calculate = 0;
    gps_map_info.show_index = 0;
    gps_map_info.last_show_index = 0;
    gps_map_info.zoom = 0;
    gps_map_info.map_show_mode = ROUTINE_MODE;
    
    // 10. 重置地图绘制相关标志
    m_is_busy = false;  // 确保设置为不忙，防止锁死
    
    // 11. 禁用信号处理器和事件回调
    signal(SIGUSR1, SIG_IGN);
    signal(SIGUSR2, SIG_IGN);
    signal(rt_sig, SIG_IGN);
    
    // 12. 释放地图缓冲区
    for (int i = 0; i < MPS_MAP_BUFF_MAX_NUM; i++) {
        gps_map_info.map_ratio[i].done = 0;
		gps_map_info.map_ratio[i].show = 0;
        gps_map_info.map_ratio[i].cur_point_show = 0;
    }

    // 13. 释放地图缓冲区内存
#if (CONFIGE_ONE_BUF == 1)
    if (gps_map_info.map_ratio[0].buff) {
		lv_mem_free(gps_map_info.map_ratio[0].buff);
		printf("map_ratio[0].buff malloc memory free\n");
        gps_map_info.map_ratio[0].buff = NULL;
    }
#else
    for (int i = 0; i < MPS_MAP_BUFF_MAX_NUM; i++) {
        if (gps_map_info.map_ratio[i].buff) {
			free(gps_map_info.map_ratio[i].buff);
			printf("map_ratio[%d].buff malloc memory free\n", i);
            gps_map_info.map_ratio[i].buff = NULL;
        }
    }
#endif

    // 14. 重置volatile变量
    g_needReloadMapFile = 0;

    printf("地图资源和状态清理完成\n");
}


#endif
