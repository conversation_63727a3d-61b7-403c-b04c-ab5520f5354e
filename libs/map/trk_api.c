﻿/**
 * @file
 * @brief ��ȡ�켣�ӿ�
 *
 * <AUTHOR> @copyright 2023 by 
 */

/*--------------------------------------------------------------------
                            INCLUDES
--------------------------------------------------------------------*/
#include "trk_api.h"
#include "xml_lib.h"
#include "stdio.h"
#include "stdlib.h"
#include <stdint.h>
#include <string.h>
#include <assert.h>
//#include <view_stack.h>
//#include "app_ui.h"
//#include "app_defines.h"
//#include "system_app.h"
#include "algo_trackjudge.h"
#include "utl_geo.h"
#include "gfx_pub.h"
#include "mlpfile.h"




#define map_malloc      malloc



/*--------------------------------------------------------------------
                            LITERAL CONSTANTS
--------------------------------------------------------------------*/
#define TRK_DEBUG_SUPPORT   0
//#define TRK_FILE_DIR        "storage/sd0/C/track/"

#ifdef CONFIG_MASS_STORAGE_DISK_NAME
#define INPUTREC_DEFAULT_FILE_PATH "/" CONFIG_MASS_STORAGE_DISK_NAME ":/track/"
#else
#define INPUTREC_DEFAULT_FILE_PATH "/victel/resource/track"
#endif

#ifdef CONFIG_MASS_STORAGE_DISK_NAME
#define TMP_FILE_NAME "/" CONFIG_MASS_STORAGE_DISK_NAME ":/MAP/track/data.tmp"
#else
#define TMP_FILE_NAME "/victel/resource/track/data.tmp"
#endif

#ifndef int32
typedef signed int int32;
#endif



//#define TMP_FILE_NAME       "storage/sd0/C/data.tmp"

/*--------------------------------------------------------------------
                                TYPES
--------------------------------------------------------------------*/

/*--------------------------------------------------------------------
                           MEMORY CONSTANTS
--------------------------------------------------------------------*/

static xml_lib_file_t m_file; //对应的是GPX文件


/*--------------------------------------------------------------------
                                MACROS
--------------------------------------------------------------------*/
#if TRK_DEBUG_SUPPORT
    #define dbg_//printf(fmt, ...) SYS_LOG_ERR(fmt, ##__VA_ARGS__)
#else
    #define dbg_//printf(fmt, ...)
#endif

/*--------------------------------------------------------------------
                             PROCEDURES
--------------------------------------------------------------------*/
//#pragma warning( disable : 4996)

/**
 * @brief 初始化
 */

#if 0

void trk_init(struct vfscan *fs, int index)
{
    if (!xml_lib_open_document(fs, index)) {
        dbg_//printf("trk::could not open track file\n");
        return;
    }
}

#endif


//extern int8_t xml_lib_parse(xml_lib_file_t *file);

/*******************************************************
*xml_lib_parse函数，传入data.tmp文件句柄
*如果函数返回ture，则代表data.tmp
*如果函数返回false,则代表解析失败，需要关闭文件句柄
*******************************************************/
bool gpx_to_tmp(void)
{
	bool ret = xml_lib_parse(m_file);
	if(!ret)
	{
		/*** 如果解析失败，关闭data.tmp文件 ***/
		//printf("trk::could not parse document \n");
        xml_lib_fclose(m_file);
		m_file = 0;
		memset(&m_file, 0, sizeof(m_file));
		return false;
	}
	else
	{
		/*** 如果解析成功，打开data.tmp文件 ***/
		//printf("trk::parse document success \n");
		//fs_file_t_init(&m_file);
		//ret = xml_lib_fopen(&m_file,TMP_FILE_NAME, FS_O_READ);
		m_file = xml_lib_fopen(TMP_FILE_NAME,O_RDONLY);
		return true;
	}

}




void trk_parse(void)
{
   	int ret;
	//fs_file_t_init(&m_file);

	//打开data.tmp文件，用于存放GPX解析出来的轨迹数据
   	//ret = xml_lib_fopen(&m_file,TMP_FILE_NAME, FS_O_RDWR);
	m_file = xml_lib_fopen(TMP_FILE_NAME,O_RDONLY);
	//fs_seek(&m_file,0,FS_SEEK_SET);
	xml_lib_fseek(m_file, 0,SEEK_SET);
	if (m_file < 0) {
        //printf("trk::could not open temp file\n");
        return;
    }

	if(m_file == 0)
	{
		//printf("trk::could not open temp file\n");
		return;
	}

	//清空data.tmp文件中的数据
//	if(fs_truncate(&m_file,0) >= 0)
//	{
//		//printf("Have_clear_the_data.tmp \n");
//	}

	//将GPX文件的数据解析并存储到data.tmp文件
	bool res = gpx_to_tmp();
	//printf("gpx_to_tmp = %d \n",res );

}

#if 1


/**
 * @brief 获取轨迹的边界
 * @param[out] min_lon 最小经度
 * @param[out] min_lat 最小纬度
 * @param[out] max_lon 最大经度
 * @param[out] max_lat 最大纬度
 * @retval true 成功, false 失败
 */

bool trk_get_border(double* min_lon, double* min_lat, double* max_lon, double* max_lat)
{
    double lat, lon;

    int32_t buffer[2];
    *min_lat = 90;
    *max_lat = -90;
    *min_lon = 180;
    *max_lon = -180;

    if (m_file == 0)
    {
        return false;
    }
    while (xml_lib_fread(m_file, buffer, sizeof(buffer)))
    {
        lon = (double)buffer[0] / 1000000;
        lat = (double)buffer[1] / 1000000;
		
        //printf("read:lon = %f, lat = %f \r\n", lon, lat);

        if (*min_lat > lat) {
            *min_lat = lat;
        }
        if (*max_lat < lat) {
            *max_lat = lat;
        }
        if (*min_lon > lon) {
            *min_lon = lon;
        }
        if (*max_lon < lon) {
            *max_lon = lon;
        }
    }
    //xml_lib_fseek(&m_file, 0, FS_SEEK_SET);
	xml_lib_fseek(m_file, 0,SEEK_SET);
	
    //printf("trk: border %f,%f,%f,%f\r\n", *min_lon, *min_lat, *max_lon, *max_lat);
    return true;
}

#endif



/**
 * @brief 连续获取经纬度
 * @param[out] lon 纬度
 * @param[out] lat 经度
 * @retval true 成功, false 失败
 */
bool trk_get_coordinate(double* lon, double* lat)
{
    int32_t buffer[2];
    if (m_file == 0)
    {
    	//printf("**m_filefilepNULL \n");
        return false;
    }
    if (xml_lib_fread(m_file, buffer, sizeof(buffer)))
    {

    	//printf("**xml_lib_fread_success ！ \n");
        *lon = (double)buffer[0] / 1000000;
        *lat = (double)buffer[1] / 1000000;

        return true;
    }
    else
    {
    	//printf("**xml_lib_fseek ！\n");
        //xml_lib_fseek(&m_file, 0, FS_SEEK_SET);
		xml_lib_fseek(m_file, 0 ,SEEK_SET);
        return false;
    }
}








bool trk_write(const char *pname, trk_pos_t *ptrk, uint32_t len)
{
#if 1
    char buffer[128];
    xml_lib_file_t file;
    uint32_t i;
    const char *pstr;
    char *pdata;
    if(len <= 2) {
        //printf("track:: too few points!\n");
        return false;
    }
//    snprintf(buffer, 128, "/NAND:/track/%s.gpx", pname);
	snprintf(buffer, 128, "%s", pname);
	//printf("***JJJJJ_the_buffer = %s \n",buffer);



	//fs_file_t_init(&file);

    //xml_lib_fopen(&file,buffer, FS_O_WRITE | FS_O_CREATE);
	file = xml_lib_fopen(buffer,O_RDONLY | O_CREAT);
    if(file == 0)
	{
        //printf("track:: write file failed!\n");
        return false;
    }

	//清空data.tmp文件中的数据
//	if(fs_truncate(&file,0) >= 0)
//	{
//		//printf("Have_clear_the_data.GPX \n");
//	}
	
	//printf("*djoasjdioc \n");
    pdata = (char *)map_malloc(256);
    if(pdata == NULL) {
        xml_lib_fclose(file);
        //printf("track:: malloc buffer failed!\n");
        return false;
    }
    pstr = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n";
    xml_lib_fwrite(file, pstr, strlen(pstr));
    pstr = "<gpx xmlns=\"http://www.topografix.com/GPX/1/1\">\r\n";
    xml_lib_fwrite(file, pstr, strlen(pstr));
    pstr = "  <trk xmlns=\"\">\r\n";
    xml_lib_fwrite(file, pstr, strlen(pstr));
    snprintf(pdata, 256, "    <name>trk_%s</name>\r\n", pname);
    xml_lib_fwrite(file, pdata, strlen(pdata));
    pstr = "    <trkseg>\r\n";
    xml_lib_fwrite(file, pstr, strlen(pstr));
    for(i = 0; i < len; i++) {
        snprintf(pdata, 256, "      <trkpt lat=\"%f\" lon=\"%f\"/>\r\n", (float)ptrk[i].lat / 1000000, (float)ptrk[i].lon / 1000000);
        xml_lib_fwrite(file, pdata, strlen(pdata));
		//printf("*IOJHOIAHUI \n");
    }
    pstr = "    </trkseg>\r\n";
    xml_lib_fwrite(file, pstr, strlen(pstr));
    pstr = "  </trk>\r\n";
    xml_lib_fwrite(file, pstr, strlen(pstr));
    pstr = "</gpx>\r\n";
    xml_lib_fwrite(file, pstr, strlen(pstr));
    map_free(pdata);
    xml_lib_fclose(file);
    //printf("trk_write:finished \n");
#endif
    return true;
}

/*********************************************************************
*
*  功能描述
*    判断是否偏移轨迹
*
*  输入参数
*    lonx：				当前经度
*    laty：				当前纬度
*    trackpoints：		轨迹数组
*    trackpoints：		轨迹点数
*
**********************************************************************/
//传入经度纬度,统一用5位处理
int navi_trackjudge(int32_t lonx, int32_t laty,int32_t trackpoints[],int track_points)
{
	int dis_tmp = 0;

	//printf("11_lonx = %d ,laty = %d ,track_points = %d \n",lonx,laty,track_points);
	
	//经纬度为0过滤判断
	if(lonx == 0 && laty == 0)
	{
		//printf("TTTT \n");
		return 0;
	}
	
	//loadgpsstore,传入的轨迹，loadgpslen轨迹大小
	dis_tmp = trackjudge(trackpoints, track_points, lonx, laty);
	//printf("FFF_the_dis_tmp = %d \n",dis_tmp);
	
	if (dis_tmp != 0)//偏离轨迹
	{
		//navi_offtrackdata.WhetherinTrack = 0;
		//navi_offtrackdata.DirectionAngle = get_DirectionAngle(trackpoints, track_points, lonx, laty);
		//navi_offtrackdata.NearestDistance = dis_tmp;
	}
	else
	{
		//navi_offtrackdata.WhetherinTrack = 1;	
		
	}
	return dis_tmp;
}



/***************************************************
*
* 功能描述： 用于解析文件之后，获取文件的经纬度数据，将其存入数组中（数组最后要释放）
*
* 		 trk_pointnum     解析出的轨迹数
*
****************************************************/

int32_t* trk_get_trk_array(int trk_pointnum)
{
	//printf("trk_get_trk_array \n");
//    int32_t buffer[2];
	double lon = 0;
	double lat = 0;
	int trackpoints_index = 0;

	//申请空间，trk_pointnum是经纬度组数量，而trk_pointarray是单个点，故需*2
	int32_t* trk_pointarray = (int32_t *)map_malloc(trk_pointnum * 2 * sizeof(int32_t));
	if(trk_pointarray == NULL)
	{
		//printf("BBB_malloc_error %d\n", trk_pointnum * 2 * sizeof(int32_t));
		return NULL;
	}

	while(trk_get_coordinate(&lon,&lat))
	{
		trk_pointarray[trackpoints_index] = lon * 100000;       //获取经度
		trk_pointarray[trackpoints_index+1] = lat * 100000;     //获取纬度
		trackpoints_index += 2;
		//printf("BBB_the_trackpoints_index = %d \n",trackpoints_index);
	}
	for(int i = 0; i < trk_pointnum * 2 ; i++)
	{
		//printf("BB_the_trk_pointarray[%d] = %d \n",i,trk_pointarray[i]);
	}

	//data.tmp文件光标回到开头位置
	xml_lib_fseek(m_file, 0, SEEK_SET);

	return trk_pointarray;
}


/***************************************************
*
*	功能描述：返回当前导航是否处于偏航状态
*		track_points：  经纬度数组
*		cur_coord：     当前经纬度
*
***************************************************/

int map_nav_offest(int32_t* track_points ,utl_geo_coord_t cur_coord)
{
	//printf("AA_map_nav_angle \n");

	//离最近的点偏移了多少米？
	int offset = 0;

    if (NULL == track_points)
    {
        return 0;
    }
//	//通过解析出来的轨迹点数，获取到轨迹数组
//	int32_t* track_points = trk_get_trk_array(write_point_count);


	//获取当前经纬度最近的轨迹点
	int32_t lonx = (int32_t)(cur_coord.longitude * 100000);
	int32_t laty = (int32_t)(cur_coord.latitude * 100000);

	//printf("AA_the_lonx = %d ,laty = %d \n",lonx,laty);

	//获取解析得到的轨迹点个数
	int trk_num = get_parse_num();

	offset = navi_trackjudge(lonx,laty,track_points,trk_num*2);
	//printf("MMM_the_offset = %d \n",offset);

	return offset;

}


/***************************************************
*
*	功能描述：返回倾斜角度
*
*		coord_1：上一个经纬度
*		coord_2：当前经纬度
*
***************************************************/

extern void map_coord_get_pos(utl_geo_coord_t coord, gfx_point_t *pos);

double slope_to_angle(utl_geo_coord_t coord_1,utl_geo_coord_t coord_2) {

	//printf("slope_to_angle \n");
	//定义2个坐标
	gfx_point_t pos_1 = {0};
	gfx_point_t pos_2 = {0};

	//通过经纬度获取到当前位置
	map_coord_get_pos(coord_1, &pos_1);
	map_coord_get_pos(coord_2, &pos_2);

	//printf("Pos1:x = %d ,y = %d \n",pos_1.x,pos_1.y);
	//printf("Pos2:x = %d ,y = %d \n",pos_2.x,pos_2.y);

	//计算倾斜角度
    double angle = atan2((double)(pos_2.y - pos_1.y), (double)(pos_2.x - pos_1.x)) * (180.0 / PI);
	//printf("Angle:x = %.02f \n",angle);

    return angle;
}



/***************************************************
*
*	功能描述：返回地图的倾斜角度（这是最终调用的接口，会根据实时位置的变化返回倾斜角度)
*		track_points：  经纬度数组
*		cur_coord：     当前经纬度
*
***************************************************/

double map_nav_angle(int32_t* track_points ,utl_geo_coord_t cur_coord)
{
	//printf("AA_map_nav_angle \n");

    if (NULL == track_points)
        return 0;
//	//通过解析出来的轨迹点数，获取到轨迹数组
//	int32_t* track_points = trk_get_trk_array(write_point_count);

	int trk_num = get_parse_num();

	//获取当前经纬度最近的轨迹点
	int32_t lonx = (int32_t)(cur_coord.longitude * 100000);
	int32_t laty = (int32_t)(cur_coord.latitude * 100000);

	//printf("AA_the_lonx = %d ,laty = %d \n",lonx,laty);

	//通过轨迹数组，轨迹数量，当前的经纬度，获取最近的轨迹点index
	extern int findtrackindex(int32_t trackpoints[], int trackpointnum, float lonx, float laty);
	int32_t track_points_index = findtrackindex(track_points,trk_num*2,lonx,laty);
	//printf("AA_the_track_points_index = %d \n",track_points_index);

	//特殊情况：如果当前点离轨迹数组中的最后一个点最近，则需要计算这个点与上一个点的轨迹点，否则数组越界
    //以2222222.GPX为例：假如最近点的index为8，一共有10个数据（共trk_num，5个点），则下面会越界，需要往前移动2个数据（1个点）
	if(track_points_index >= trk_num*2 - 2) { track_points_index = trk_num*2 -4;}

	//printf("11_the_track_points_index = %d \n",track_points_index);

	utl_geo_coord_t coord_1 = {0};
	utl_geo_coord_t coord_2 = {0};

	//获取离当前位置最近的轨迹点，以及下一个轨迹点
	coord_1.longitude = (double)track_points[track_points_index] / 100000;
	coord_1.latitude  = (double)track_points[track_points_index+1] / 100000;
	coord_2.longitude = (double)track_points[track_points_index+2]   / 100000;
	coord_2.latitude  = (double)track_points[track_points_index+3] / 100000;
	//printf("AA_the_coord_1.lon = %.6f,lat = %.6f \n",coord_1.longitude,coord_1.latitude);
	//printf("AA_the_coord_2.lon = %.6f,lat = %.6f \n",coord_2.longitude,coord_2.latitude);


	//对这2个点进行求角度的操作，获取其倾斜角度
	double map_nav_angle = slope_to_angle(coord_1,coord_2);
	//printf("AA_the_map_nav_angle = %f\n",map_nav_angle);

	return map_nav_angle;
}


/******************************************************************
*
*				
*				  距离20米的获取交点坐标判断
*
*
*******************************************************************/

#include <stdio.h>
#include <math.h>
#include <stdlib.h>

#define PI_F 3.1415926
//#define R_EARTH 6371000 // 地球半径，单位米



#define LIMIT_20_MILE 20

#define LIMIT_30_MILE 30


double toRadians(double degree) {
    return degree * PI_F / 180.0;
}

double haversineDistance(utl_geo_coord_t p1, utl_geo_coord_t p2) {
    double dLat = toRadians(p2.latitude - p1.latitude);
    double dLon = toRadians(p2.longitude - p1.longitude);
    double a = sin(dLat / 2) * sin(dLat / 2) +
               cos(toRadians(p1.latitude)) * cos(toRadians(p2.latitude)) *
               sin(dLon / 2) * sin(dLon / 2);
    double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    return R_EARTH * c;
}

// 检查给定的经纬度是否在指定的范围内
bool isWithinRange(utl_geo_coord_t point, utl_geo_coord_t minCoord, utl_geo_coord_t maxCoord) {
	//printf("point:longitude=%f,latitude=%f \n",point.longitude,point.latitude); 
	//printf("minCoord:longitude=%f,latitude=%f \n",minCoord.longitude,minCoord.latitude); 
	//printf("maxCoord:longitude=%f,latitude=%f \n",maxCoord.longitude,maxCoord.latitude); 
    return (point.longitude >= minCoord.longitude && point.longitude <= maxCoord.longitude &&
            point.latitude >= minCoord.latitude && point.latitude <= maxCoord.latitude);
}

void GpsDistanceToSegment(utl_geo_coord_t point, utl_geo_coord_t segStart, utl_geo_coord_t segEnd, double* distance, utl_geo_coord_t* intersection) {
    double lat1 = toRadians(point.latitude);
    double lon1 = toRadians(point.longitude);
    double lat2 = toRadians(segStart.latitude);
    double lon2 = toRadians(segStart.longitude);
    double lat3 = toRadians(segEnd.latitude);
    double lon3 = toRadians(segEnd.longitude);

    double dLon = lon3 - lon2;
    double dLat = lat3 - lat2;

    if (fabs(dLon) < 1e-10 && fabs(dLat) < 1e-10) { // The segment is actually a point
        *distance = haversineDistance(point, segStart);
        intersection->longitude = segStart.longitude;
        intersection->latitude = segStart.latitude;
        return;
    }

    double t = ((lat1 - lat2) * dLat + (lon1 - lon2) * dLon) / (dLat * dLat + dLon * dLon);
    t = fmax(0, fmin(1, t)); // Clamp t to the range [0, 1]

    double closestLat = lat2 + t * dLat;
    double closestLon = lon2 + t * dLon;

    double distLat = lat1 - closestLat;
    double distLon = lon1 - closestLon;

    *distance = sqrt(distLat * distLat + distLon * distLon) * R_EARTH;

    intersection->longitude = closestLon * 180.0 / PI_F;
    intersection->latitude = closestLat * 180.0 / PI_F;
    //printf("longitude=%f,latitude=%f \n",intersection->longitude,intersection->latitude); 
    //printf("closestLon=%f,closestLat=%f \n",closestLon,closestLat);
    
    
    utl_geo_coord_t max_coord={0};
    utl_geo_coord_t min_coord={0};
    
    if(segStart.longitude > segEnd.longitude)
    {
    	max_coord.longitude = segStart.longitude;
    	min_coord.longitude = segEnd.longitude;
    	//printf("11minCoord:longitude=%f,latitude=%f \n",min_coord.longitude,min_coord.latitude); 
		//printf("11maxCoord:longitude=%f,latitude=%f \n",max_coord.longitude,max_coord.latitude); 
	}
	else
	{
		max_coord.longitude = segEnd.longitude;
    	min_coord.longitude = segStart.longitude;
	}
	if(segStart.latitude > segEnd.latitude)
	{
		max_coord.latitude = segStart.latitude;
    	min_coord.latitude = segEnd.latitude;
    		//printf("22minCoord:longitude=%f,latitude=%f \n",min_coord.longitude,min_coord.latitude); 
	//printf("22maxCoord:longitude=%f,latitude=%f \n",max_coord.longitude,max_coord.latitude); 
	}
	else
	{
		max_coord.latitude = segEnd.latitude;
    	min_coord.latitude = segStart.latitude;
	}
	
	int same_flag = 0;
	if(0 == memcmp(intersection,&segStart,sizeof(segStart)) || (0 == memcmp(intersection,&segEnd,sizeof(segEnd))))
	{
		same_flag = 1;
	}
	
	if(0 == isWithinRange(*intersection, min_coord, max_coord) || same_flag)
	{
		*distance = -1;
        intersection->longitude = -200;
        intersection->latitude = -200;
	}

}









/***************************************************
*
*	功能描述：返回地图的倾斜角度（这是最终调用的接口，会根据实时位置的变化返回倾斜角度)
*		track_points：  经纬度数组
*		cur_coord：     当前经纬度
*
***************************************************/

void map_trk_offset(int32_t* track_points ,utl_geo_coord_t* cur_coord)
{
	//printf("AA_map_nav_angle \n");

    if (NULL == track_points)
        return ;
//	//通过解析出来的轨迹点数，获取到轨迹数组
//	int32_t* track_points = trk_get_trk_array(write_point_count);

	int trk_num = get_parse_num();

	//获取当前经纬度最近的轨迹点
	int32_t lonx = (int32_t)(cur_coord->longitude * 100000);
	int32_t laty = (int32_t)(cur_coord->latitude * 100000);

	//printf("AA_the_lonx = %d ,laty = %d \n",lonx,laty);

	//通过轨迹数组，轨迹数量，当前的经纬度，获取最近的轨迹点index
	extern int findtrackindex(int32_t trackpoints[], int trackpointnum, float lonx, float laty);
	int32_t track_points_index = findtrackindex(track_points,trk_num*2,lonx,laty);
	//printf("AA_the_track_points_index = %d \n",track_points_index);

	//特殊情况：如果当前点离轨迹数组中的最后一个点最近，则需要计算这个点与上一个点的轨迹点，否则数组越界
    //以2222222.GPX为例：假如最近点的index为8，一共有10个数据（共trk_num，5个点），则下面会越界，需要往前移动2个数据（1个点）
	if(track_points_index >= trk_num*2 - 2) { track_points_index = trk_num*2 -4;}

	//printf("11_the_track_points_index = %d \n",track_points_index);

	utl_geo_coord_t coord_1 = {0};
	utl_geo_coord_t coord_2 = {0};
	utl_geo_coord_t coord_3 = {0};

	//获取离当前位置最近的轨迹点，以及下一个轨迹点
	coord_1.longitude = (double)track_points[track_points_index] 	/ 100000;	//最近中心点
	coord_1.latitude  = (double)track_points[track_points_index+1] 	/ 100000;
	
	coord_2.longitude = (double)track_points[track_points_index+2]  / 100000;	//最近中心点下一个点
	coord_2.latitude  = (double)track_points[track_points_index+3] 	/ 100000;
	
	coord_3.longitude = (double)track_points[track_points_index-2] 	/ 100000;	//最近中心点上一个点
	coord_3.latitude  = (double)track_points[track_points_index-1] 	/ 100000;
	
	//printf("AA_the_coord_1.lon = %.6f,lat = %.6f \n",coord_1.longitude,coord_1.latitude);
	//printf("AA_the_coord_2.lon = %.6f,lat = %.6f \n",coord_2.longitude,coord_2.latitude);
	//printf("AA_the_coord_3.lon = %.6f,lat = %.6f \n",coord_3.longitude,coord_3.latitude);


	//前一个线段的距离
	double distance1 = 0;
	utl_geo_coord_t intersection1 = {0};

	//后一个线段的距离
	double distance2 = 0;
	utl_geo_coord_t intersection2 = {0};

	//获取当前点距离前一个线段的距离，以及交点坐标
	GpsDistanceToSegment(*cur_coord,coord_3,coord_1,&distance1,&intersection1);

	//获取当前点距离后一个线段的距离，以及交点坐标
	GpsDistanceToSegment(*cur_coord,coord_1,coord_2,&distance2,&intersection2);

	//printf("MMM_the_distance1=%f,intersection1.lon=%f,lat=%f \n",distance1,intersection1.longitude,intersection1.latitude);

	//printf("MMM_the_distance2=%f,intersection2.lon=%f,lat=%f \n",distance2,intersection2.longitude,intersection2.latitude);


	if(distance1 <= LIMIT_20_MILE && distance1 >= 0)
	{
		if(distance1 < distance2 || -1 == distance2)
		{
			cur_coord->longitude = intersection1.longitude;
			cur_coord->latitude = intersection1.latitude;
			//printf("111_cur_coord.lon=%f,lat=%f \n",cur_coord->longitude,cur_coord->latitude);
			
//			return distance1;
		}
		
	}
	else if(distance2 <= LIMIT_20_MILE && distance2 >= 0)
	{
		if(distance2 < distance1 || -1 == distance1)
		{
			cur_coord->longitude = intersection2.longitude;
			cur_coord->latitude = intersection2.latitude;
			//printf("222_cur_coord.lon=%f,lat=%f \n",cur_coord->longitude,cur_coord->latitude);

//			return distance2;
		}
	}
















	
//	if((distance1 <= LIMIT_20_MILE && distance1 >= 0) && (distance2 <= LIMIT_20_MILE && distance2 >= 0))
//	{
//		//如果前一个距离最短，则应该采用这个交点作为当前经纬度
//		if(distance1 < distance2 && distance1 != -1)
//		{
//			cur_coord->longitude = intersection1.longitude;
//			cur_coord->latitude = intersection1.latitude;
//			//printf("111_cur_coord.lon=%f,lat=%f \n",cur_coord->longitude,cur_coord->latitude);
//		}
//		else if(distance1 > distance2 && distance2 != -1)
//		{
//			cur_coord->longitude = intersection2.longitude;
//			cur_coord->latitude = intersection2.latitude;
//			//printf("222_cur_coord.lon=%f,lat=%f \n",cur_coord->longitude,cur_coord->latitude);
//		}
//	}


		return ;
}





/********************************************************
*
*
*		获得点与线段的最短距离
*
*
*********************************************************/

void Get_GpsDistance_yt(utl_geo_coord_t point, utl_geo_coord_t segStart, utl_geo_coord_t segEnd, double* distance) {
	double lat1 = toRadians(point.latitude);
	double lon1 = toRadians(point.longitude);
	double lat2 = toRadians(segStart.latitude);
	double lon2 = toRadians(segStart.longitude);
	double lat3 = toRadians(segEnd.latitude);
	double lon3 = toRadians(segEnd.longitude);

	double dLon = lon3 - lon2;
	double dLat = lat3 - lat2;

	utl_geo_coord_t intersection = {0};

	if (fabs(dLon) < 1e-10 && fabs(dLat) < 1e-10) { // The segment is actually a point
		*distance = haversineDistance(point, segStart);
		intersection.longitude = segStart.longitude;
		intersection.latitude = segStart.latitude;
		return;
	}

	double t = ((lat1 - lat2) * dLat + (lon1 - lon2) * dLon) / (dLat * dLat + dLon * dLon);
	t = fmax(0, fmin(1, t)); // Clamp t to the range [0, 1]

	double closestLat = lat2 + t * dLat;
	double closestLon = lon2 + t * dLon;

	double distLat = lat1 - closestLat;
	double distLon = lon1 - closestLon;

	*distance = sqrt(distLat * distLat + distLon * distLon) * R_EARTH;

	intersection.longitude = closestLon * 180.0 / PI_F;
	intersection.latitude = closestLat * 180.0 / PI_F;
	//printf("longitude=%f,latitude=%f \n",intersection.longitude,intersection.latitude); 
	//printf("closestLon=%f,closestLat=%f \n",closestLon,closestLat);
	
	
	utl_geo_coord_t max_coord={0};
	utl_geo_coord_t min_coord={0};
	
	if(segStart.longitude > segEnd.longitude)
	{
		max_coord.longitude = segStart.longitude;
		min_coord.longitude = segEnd.longitude;
		//printf("11minCoord:longitude=%f,latitude=%f \n",min_coord.longitude,min_coord.latitude); 
		//printf("11maxCoord:longitude=%f,latitude=%f \n",max_coord.longitude,max_coord.latitude); 
	}
	else
	{
		max_coord.longitude = segEnd.longitude;
		min_coord.longitude = segStart.longitude;
	}
	if(segStart.latitude > segEnd.latitude)
	{
		max_coord.latitude = segStart.latitude;
		min_coord.latitude = segEnd.latitude;
		//printf("22minCoord:longitude=%f,latitude=%f \n",min_coord.longitude,min_coord.latitude); 
		//printf("22maxCoord:longitude=%f,latitude=%f \n",max_coord.longitude,max_coord.latitude); 
	}
	else
	{
		max_coord.latitude = segEnd.latitude;
		min_coord.latitude = segStart.latitude;
	}
	
	int same_flag = 0;
	if(0 == memcmp(&intersection,&segStart,sizeof(segStart)) || (0 == memcmp(&intersection,&segEnd,sizeof(segEnd))))
	{
		same_flag = 1;
	}
	
	if(0 == isWithinRange(intersection, min_coord, max_coord) || same_flag)
	{
		*distance = -1;
//		intersection.longitude = -200;
//		intersection.latitude = -200;
	}

}




/***************************************************
*
*	功能描述：返回地图的倾斜角度（这是最终调用的接口，会根据实时位置的变化返回倾斜角度)
*		track_points：  经纬度数组
*		cur_coord：     当前经纬度
*
***************************************************/

double map_trk_offset_yt(int32_t* track_points ,utl_geo_coord_t cur_coord)
{
	//printf("AA_map_nav_angle \n");

    if (NULL == track_points)
        return -1;
//	//通过解析出来的轨迹点数，获取到轨迹数组
//	int32_t* track_points = trk_get_trk_array(write_point_count);

	int trk_num = get_parse_num();

	//获取当前经纬度最近的轨迹点
	int32_t lonx = (int32_t)(cur_coord.longitude * 100000);
	int32_t laty = (int32_t)(cur_coord.latitude * 100000);

	//printf("AA_the_lonx = %d ,laty = %d \n",lonx,laty);

	//通过轨迹数组，轨迹数量，当前的经纬度，获取最近的轨迹点index
	extern int findtrackindex(int32_t trackpoints[], int trackpointnum, float lonx, float laty);
	int32_t track_points_index = findtrackindex(track_points,trk_num*2,lonx,laty);
	//printf("AA_the_track_points_index = %d \n",track_points_index);

	//特殊情况：如果当前点离轨迹数组中的最后一个点最近，则需要计算这个点与上一个点的轨迹点，否则数组越界
    //以2222222.GPX为例：假如最近点的index为8，一共有10个数据（共trk_num，5个点），则下面会越界，需要往前移动2个数据（1个点）
	if(track_points_index >= trk_num*2 - 2) { track_points_index = trk_num*2 -4;}

	//printf("11_the_track_points_index = %d \n",track_points_index);

	utl_geo_coord_t coord_1 = {0};
	utl_geo_coord_t coord_2 = {0};
	utl_geo_coord_t coord_3 = {0};

	//获取离当前位置最近的轨迹点，以及下一个轨迹点
	coord_1.longitude = (double)track_points[track_points_index] 	/ 100000;	//最近中心点
	coord_1.latitude  = (double)track_points[track_points_index+1] 	/ 100000;
	
	coord_2.longitude = (double)track_points[track_points_index+2]  / 100000;	//最近中心点下一个点
	coord_2.latitude  = (double)track_points[track_points_index+3] 	/ 100000;
	
	coord_3.longitude = (double)track_points[track_points_index-2] 	/ 100000;	//最近中心点上一个点
	coord_3.latitude  = (double)track_points[track_points_index-1] 	/ 100000;
	
	//printf("AA_the_coord_1.lon = %.6f,lat = %.6f \n",coord_1.longitude,coord_1.latitude);
	//printf("AA_the_coord_2.lon = %.6f,lat = %.6f \n",coord_2.longitude,coord_2.latitude);
	//printf("AA_the_coord_3.lon = %.6f,lat = %.6f \n",coord_3.longitude,coord_3.latitude);


	//前一个线段的距离
	double distance1 = 0;
	Get_GpsDistance_yt(cur_coord,coord_3,coord_1,&distance1);

	//后一个线段的距离
	double distance2 = 0;
	Get_GpsDistance_yt(cur_coord,coord_1,coord_2,&distance2);

	//printf("AA_the_distance1 = %f \n",distance1);
	//printf("AA_the_distance2 = %f \n",distance2);

	//找到最小值距离
	if(distance1 < distance2)
	{
		if(distance1 > 0)
		{

			return distance1;
		}
		else
		{
			return distance2;
		}
	}
	else if(distance1 > distance2)
	{
		if(distance2 > 0)
		{
			return distance2;
		}
		else
		{
			return distance1;
		}
	}
	else if(distance1 < 0 && distance2 < 0)
	{
		return GpsDistance5Bit(coord_1.longitude,coord_1.latitude,cur_coord.longitude,cur_coord.latitude);
	}
	else
	{
		return 0;
	}
}
	

//*************** ADD：0107_轨迹相关 ******************************//


//记录模式下的tmp文件
xml_lib_file_t tmp_file;

int gpx_record_num = 0;

extern utl_geo_coord_t m_cur_coord;

//当前要保存的GPX文件名
static char cur_record_filename[52] = {0};

//当前存放轨迹点的临时文件名
static char cur_temp_filename[52] = {0};

//设置要保存的GPX文件名 相对路径 
void set_gpx_name(/*char* gpx_name,*/char* tmp_name)
{
	//1.赋值GPX文件名
	////printf("JJ_the_cur_record_filename = %s,gpx_name = %s \n",cur_record_filename,gpx_name);
    //printf("JJ_the_tmp_name = %s \n",tmp_name);
	//memset(cur_record_filename,0,sizeof(cur_record_filename));
	//memcpy(cur_record_filename,gpx_name,strlen(gpx_name));
    memset(cur_temp_filename,0,sizeof(cur_temp_filename));
	memcpy(cur_temp_filename,tmp_name,strlen(tmp_name));
}

//清空的GPX文件名
void clear_gpx_name()
{
	//1.赋值GPX文件名
	memset(cur_record_filename,0,sizeof(cur_record_filename));

	//2.赋值TEMP文件名
	memset(cur_temp_filename,0,sizeof(cur_temp_filename));
}


//获取要保存的GPX文件名
char* get_gpx_name()
{
    //return cur_record_filename;
    return cur_temp_filename;
}


/*************************************************************
*【临时文件用于进入地图后展示轨迹，退出运动或结束记录后，删掉】
*
*	功能描述：保存记录的经纬度点到临时文件
*		参数：ptrk：当前经纬度位置
*
*************************************************************/
int trk_record_temp(trk_pos_t ptrk)
{
//	xml_lib_file_t tmp_file;
	char buffer[128];
	
	/********** 打开文件 **********/
	snprintf(buffer, 128, "%s", cur_temp_filename);
	////printf("cur_temp_filename:%s \n",cur_temp_filename);
	//如果tmp文件句柄为空，则还未打开
	if(tmp_file ==0)
	{
		//printf("have_not_open_the_tmp_file \n");
		//可追加方式打开tmp文件
		//fs_file_t_init(&tmp_file);
        //创建，并且清除所有数据
		tmp_file= xml_lib_fopen(buffer, O_CREAT|O_TRUNC);

        //清空tmp_file所有数据
        //ftruncate(tmp_file, 0);
		
		if(tmp_file > 0)
		{
			//printf("creat_file_success \n");
			xml_lib_fclose(tmp_file);
			tmp_file = xml_lib_fopen(buffer, O_APPEND | O_RDWR);
			if(0 < tmp_file)
			{
				//printf("FS_O_APPEND_success \n");
			}
			else
			{
				//printf("FS_O_APPEND_fail \n");
				return 0;
			}
		}
		else if(tmp_file < 0)
		{
			//printf("Lopen_tmp_file:FS_O_CREATE | FS_O_APPEND \n");
			return 0;
		}
	}
	else
	{
		////printf("OPEN_temp_success \n");
	}

	/********** 写入数据 **********/
	xml_lib_fwrite(tmp_file, &ptrk.lon, sizeof(ptrk.lon));
	xml_lib_fwrite(tmp_file, &ptrk.lat, sizeof(ptrk.lat));

	/********** 写入个数加1 **********/
	gpx_record_num++;
	
	////printf("have_write_lon=%d,lat=%d,gpx_record_num=%d \n",ptrk.lon,ptrk.lat,gpx_record_num);
	xml_lib_fseek(tmp_file, 0, SEEK_SET);

	return gpx_record_num;
}



/**
 * @brief 记录模式下tmp文件连续获取经纬度
 * @param[out] lon 纬度
 * @param[out] lat 经度
 * @retval true 成功, false 失败
 */
bool record_get_coordinate(double* lon, double* lat)
{
	static bool has_returned_error = false;
	int32_t buffer[2];

	if (tmp_file == 0)
	{
		//printf("tmp_file_filep=NULL ");
		return false;
	}
	// //printf("K_the_has_returned_error = %d \n",has_returned_error);
	if (xml_lib_fread(tmp_file, buffer, sizeof(buffer)))
	{
		// //printf("tmp_file_fread_success! \n");
		*lon = (double)buffer[0] / 1000000;
		*lat = (double)buffer[1] / 1000000;
		has_returned_error = false; // 重置错误标志
        ////printf("record_get_coordinate_lon=%f,lat=%f \n",*lon,*lat);
		return true;
	}
	else
	{
		if (!has_returned_error)
		{
			*lon = m_cur_coord.longitude;
			*lat = m_cur_coord.latitude;
			// //printf("tmp_file_fseek ！");
			has_returned_error = true; // 设置错误标志
			return true;
		}
		xml_lib_fseek(tmp_file, 0, SEEK_SET);
		return false;
	}
}

/**
 * @brief 重置记录模式坐标读取的错误状态
 */
static void reset_record_coordinate_state(void)
{
	// 通过调用一次函数并忽略结果来重置静态变量
	// 这是访问静态变量的一种方式
	double dummy_lon, dummy_lat;
	// 这里我们需要一个更直接的方式来重置静态变量
	// 由于静态变量在函数内部，我们在trk_cleanup中处理文件重置时会自然重置状态
}

/*************************************************************
*
*	功能描述：每秒触发1次，将当前坐标，存入GPX文件中
*	参数：		  cur_time:	计时器秒数
*			  ptrk    :	当前经纬度位置
*			  mode    : 处于什么状态(运动优先级>地图优先级)
*
*************************************************************/
int start_trk_record(int cur_time,trk_pos_t ptrk)
{

	//1.如果当前没有设置要保存的gpx文件名，直接返回
	char zeros[52];
	memset(zeros,0,52);
    ////printf("ADD:start_trk_record=%s \n",cur_temp_filename);
	int tmp_num = 0;
	if(memcmp(cur_temp_filename,zeros,52) == 0)
	{
		return tmp_num;
	}

	////printf("start_trk_record:cur_time=%d,ptrk.x=%d,ptrk.y=%d \n",cur_time,ptrk.lon,ptrk.lat);


    if(cur_time % 2 == 1)
    {
        //保存轨迹点到tmp文件，每10秒1次，用于展示在
        tmp_num = trk_record_temp(ptrk);
        ////printf("SPORT_VIEW:start_trk_record:tmp_num = %d \n",tmp_num);
    }	

	return tmp_num;
}




//************** 返航模式下已经读出来的轨迹点数量 **************************
static int ret_read_num = 0;

//************** 轨迹返航的当前轨迹索引 *************************************
extern uint16_t num_of_tra;

extern uint8_t get_map_mode(void);

/**
 * @brief 返航模式下tmp文件连续获取经纬度
 * @param[out] lon 纬度
 * @param[out] lat 经度
 * @retval true 成功, false 失败
 */
bool return_get_coordinate(double* lon, double* lat)
{
	static bool has_returned_error = false;
	int32_t buffer[2];

	if (tmp_file == 0)
	{
		//printf("tmp_file_filep=NULL ");
		return false;
	}
	//printf("K_the_has_returned_error = %d \n",has_returned_error);
	if (xml_lib_fread(tmp_file, buffer, sizeof(buffer)) && ret_read_num < num_of_tra)
	{
		//printf("tmp_file_fread_success! \n");
		*lon = (double)buffer[0] / 1000000;
		*lat = (double)buffer[1] / 1000000;
		has_returned_error = false; // 重置错误标志
		ret_read_num++;
		//printf("MM_the_ret_read_num = %d,num_of_tra=%d ",ret_read_num,num_of_tra);
		return true;
	}
	else
	{
		if (!has_returned_error)
		{
			*lon = m_cur_coord.longitude;
			*lat = m_cur_coord.latitude;
			//printf("tmp_file_fseek ！");
			has_returned_error = true; // 设置错误标志
			return true;
		}
		ret_read_num = 0;
		xml_lib_fseek(tmp_file, 0, SEEK_SET);
		return false;
	}
}


void trk_close(void)
{
	//printf("H_trk_close:m_file=%d ,m_mode=0x%02x \n",m_file,get_map_mode());
	if(NAV_MODE == get_map_mode())
	{
		if (m_file != 0) {
			//printf("trk_close:m_file \n");
			fseek(m_file,0,SEEK_SET);
			//printf("trk_close:xml_lib_fclose \n");
			int close_ret = xml_lib_fclose(m_file);
			//printf("JJJ_the_close_ret = %d \n",close_ret);
			m_file = 0;
			memset(&m_file, 0, sizeof(m_file));
		}
	}
	if(REC_MODE == get_map_mode() || RET_MODE == get_map_mode())
	{
		if (tmp_file != 0) {
			//printf("trk_close:tmp_file \n");
			//fseek(tmp_file,0,SEEK_SET);
			int close_ret = xml_lib_fclose(tmp_file);
			//printf("JJJ_the_close_ret = %d \n",close_ret);
			tmp_file = 0;
			//memset(&tmp_file, 0, sizeof(tmp_file));

			//4.删除临时文件夹
			//nand_unlink(cur_temp_filename);

			//5.清空gpx文件名
			//clear_gpx_name();
		}
	}

}



//轨迹点数组【动态申请】
int32_t* lat_lon_buf_yt = NULL;

//读取到的轨迹点数量
int read_num = 0;

//起点计数
//int start_pos_count = 0;

#define PERIOD_POINT_NUMS 100 //每次读100个数据出来【50个轨迹点】

//当前要打开的GPX文件名
static char cur_nav_filename[52] = {0};

//设置要打开的GPX文件名 相对路径 
void set_nav_gpx_name(char* tmp_name)
{
	//1.赋值GPX文件名
	////printf("JJ_the_cur_record_filename = %s,gpx_name = %s \n",cur_record_filename,gpx_name);
    //printf("set_nav_gpx_name = %s \n",tmp_name);
	//memset(cur_record_filename,0,sizeof(cur_record_filename));
	//memcpy(cur_record_filename,gpx_name,strlen(gpx_name));
    memset(cur_nav_filename,0,sizeof(cur_nav_filename));
	memcpy(cur_nav_filename,tmp_name,strlen(tmp_name));
}

//清空打开的GPX文件名
void clear_nav_gpx_name()
{
	//1.赋值GPX文件名
	memset(cur_nav_filename,0,sizeof(cur_nav_filename));
}

/**
 * @brief 清理所有轨迹相关状态和内存
 * @note 用于地图退出时完全重置轨迹状态，防止数据残留
 */
void trk_cleanup(void)
{
    printf("开始清理轨迹相关状态和内存...\n");

    // 1. 关闭所有轨迹文件并重置文件句柄
    if (m_file != 0) {
        xml_lib_fclose(m_file);
        m_file = 0;
        memset(&m_file, 0, sizeof(m_file));
        printf("m_file 文件句柄已关闭并重置\n");
    }

    if (tmp_file != 0) {
        xml_lib_fclose(tmp_file);
        tmp_file = 0;
        printf("tmp_file 文件句柄已关闭并重置\n");
    }

    // 2. 释放导航模式轨迹缓冲区
    if (lat_lon_buf_yt != NULL) {
        map_free(lat_lon_buf_yt);
        lat_lon_buf_yt = NULL;
        printf("lat_lon_buf_yt 缓冲区已释放\n");
    }

    // 3. 重置所有轨迹计数器和状态变量
    read_num = 0;
    ret_read_num = 0;
    gpx_record_num = 0;
    printf("轨迹计数器已重置: read_num=%d, ret_read_num=%d, gpx_record_num=%d\n",
           read_num, ret_read_num, gpx_record_num);

    // 4. 清空文件名缓存
    clear_gpx_name();
    clear_nav_gpx_name();

    // 5. 重置静态错误标志（通过关闭文件自然重置）
    // 注意：静态变量has_returned_error会在下次文件操作时自然重置

    printf("轨迹相关状态和内存清理完成\n");
}



/**
 * @brief 导航模式下tmp文件连续获取经纬度
 * @param[out] lon 纬度
 * @param[out] lat 经度
 * @retval true 成功, false 失败
 */
//bool nav_get_coordinate(double* lon, double* lat)
extern utl_geo_coord_t m_cur_coord;
bool trk_get_coordinate_new(double* lon, double* lat)
{

#if 0
	int32_t buffer[2];
	if (m_file.filep == NULL)
	{
		//    	//printf("**m_filefilepNULL \n");
		return false;
	}

	if (xml_lib_fread(&m_file, buffer, sizeof(buffer)))
	{

		//    	//printf("**xml_lib_fread_success 锛  \n");
		*lon = (double)buffer[0] / 1000000;
		*lat = (double)buffer[1] / 1000000;

		//		xml_lib_fseek(&m_file, 10*2*sizeof(int32_t), FS_SEEK_CUR);

		return true;
	}
	else
	{
		//    	//printf("**xml_lib_fseek 锛乗n");
		xml_lib_fseek(&m_file, 0, FS_SEEK_SET);
		return false;
	}
#else


	int32_t buffer[2];
	int32_t point_num = 0;

	if (NULL == lat_lon_buf_yt)
	{
		lat_lon_buf_yt = map_malloc(sizeof(int32_t) * PERIOD_POINT_NUMS);
		if (NULL == lat_lon_buf_yt)
		{
			//printf("lat_lon_buf_yt malloc fail \n");
			return false;
		}
		//start_pos_count = 0;
		//printf("kkkkk \n");
		//璺宠繃鍓 5涓暟鎹 
		//fs_file_t_init(&m_file);
		printf("trk:ready open file %s \n", cur_nav_filename);
		m_file = xml_lib_fopen(cur_nav_filename, O_RDONLY);
		//int ret = fs_open(&m_file, cur_nav_filename, FS_O_READ);
		if (m_file < 0) {
			//printf("trk::could not open %s \n", cur_nav_filename);
			return false;
		}

		//获取路线文件的轨迹点数
		//xml_lib_fread(&m_file, &point_num, sizeof(point_num));

		//xml_lib_fseek(&m_file, 4 * sizeof(int32_t), FS_SEEK_CUR);

		//每次都初始化为极大值
		//shortest_distance = R_EARTH;

		//重置轨迹偏航
		//track_offset_flag = 0;
		////printf("GGY_the_shortest_distance = %d \n", shortest_distance);
	}



	if (0 == read_num % PERIOD_POINT_NUMS)
	{

		//printf("trk_get_coordinate_yt:read_num=%d \n", read_num);

		//娓呯┖
		memset(lat_lon_buf_yt, 0, sizeof(int32_t) * PERIOD_POINT_NUMS);

		//涓€娆℃€ц鍙 100涓粡绾害
		xml_lib_fread(m_file, (void*)lat_lon_buf_yt, sizeof(int32_t) * PERIOD_POINT_NUMS);
#if 0
		//每次读点都找到最近的距离点
		double distance = map_trk_offset_yt(lat_lon_buf_yt, m_cur_coord, point_num);
		//printf("M_distance=%f,shortest_distance=%f \n", distance, shortest_distance);

		if (shortest_distance > distance)
		{
			shortest_distance = distance;
		}
		//printf("N_distance=%f,shortest_distance=%f \n", distance, shortest_distance);
#endif

		//澶嶄綅0
		read_num = 0;
	}

	*lon = (double)lat_lon_buf_yt[read_num] / 1000000;
	*lat = (double)lat_lon_buf_yt[read_num + 1] / 1000000;


	// printf("ZA_TEST=>*lon=%f,*lat=%f \n",*lon,*lat);

	//璧嬪€ 

	read_num += 2;


	if (0 != *lon && 0 != *lat)
	{
		return true;
	}
	else
	{
		// printf("22222 \n");
		xml_lib_fseek(m_file, 0, SEEK_SET);
		map_free(lat_lon_buf_yt);
		lat_lon_buf_yt = NULL;
		read_num = 0;
#if 0
		//如果大于设置的偏航值，则偏航
		if (shortest_distance > LIMIT_30_MILE)
		{
			track_offset_flag = 1;
		}
		else
		{
			track_offset_flag = 0;
		}
#endif


		//每次记得关闭
		int close_ret = xml_lib_fclose(m_file);
		m_file = 0;
		//printf("CLOSE_RET = %d \n", close_ret);
		//start_pos_count = 0;
		return false;
	}

#endif


}


/*****************************************************
*				
*			new
*
******************************************************/
int trk_parse_yt(char* file_path)
{
	return 1;
}


/*****************************************************************
*
*	Function: trk_get_border_new
*	
*	Description: 获取轨迹的边界
*	
*	Parameters: 最小经度指针min_lon，最小纬度指针min_lat，最大经度指针max_lon，最大纬度指针max_lat
*	Return: 成功返回true，失败返回false
*
*****************************************************************/
bool trk_get_border_new(double* min_lon, double* min_lat, double* max_lon, double* max_lat)
{
    double lat, lon;

    *min_lat = 90;
    *max_lat = -90;
    *min_lon = 180;
    *max_lon = -180;
	//printf("ZA_TEST=>cur_nav_filename=%s \n",cur_nav_filename);
	//m_file = xml_lib_fopen(cur_nav_filename, O_RDONLY);
    //if (m_file < 0)
    //{
    //    return false;
    //}
    //xml_lib_fseek(m_file, 0, SEEK_SET);

    while (trk_get_coordinate_new(&lon, &lat))
    {
        ////printf("read:lon = %f, lat = %f \r\n", lon, lat);

        if (*min_lat > lat) {
            *min_lat = lat;
        }
        if (*max_lat < lat) {
            *max_lat = lat;
        }
        if (*min_lon > lon) {
            *min_lon = lon;
        }
        if (*max_lon < lon) {
            *max_lon = lon;
        }
    }
    xml_lib_fseek(m_file, 0, SEEK_SET);

    //printf("trk: border %f,%f,%f,%f\r\n", *min_lon, *min_lat, *max_lon, *max_lat);
    return true;
}
