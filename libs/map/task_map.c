﻿/**

 * @file
 * @brief map task
 * <AUTHOR> @copyright (c) 2023
 */

/*--------------------------------------------------------------------
							  INCLUDES
--------------------------------------------------------------------*/
#include <stdint.h>
#include <signal.h>
#include <pthread.h>
#include <sys/time.h>
#include <sys/stat.h>
#include "mlp_tree.h"
#include "gfx_pub.h"
#include "mlpfile.h"
#include "map_api.h"
#include "utl_geo.h"
#include <math.h>
#include "gfx_pub.h"
#include "mlp_tree.h"
#include "trk_api.h"
#include "xml_lib.h"
//#include "uart_dma_test.h"
#include "map_port.h"
#include "lvgl.h"
#include "ui.h"


#define PRINT_TASK_MAP 1
/*--------------------------------------------------------------------
						  LITERAL CONSTANTS
--------------------------------------------------------------------*/
#define TASK_NAME "TaskMap"
#define TASK_LICENSE_NAME "TaskLicenseMap"
#define SIGTRACK 33

//地图线程的事件枚举类型
enum Event_Cb_Map 
{ 
	MAP_DRAW 	= 0, 	//地图绘制事件
	MAP_TRACK 	= 1, 	//轨迹绘制事件
	MAP_PARSE 	= 2, 	//轨迹解析事件
	MAP_OTHER 	= 4 	//补充后台地图绘制事件
};



/*--------------------------------------------------------------------
								TYPES
--------------------------------------------------------------------*/
typedef uint8_t task_msg_t;
enum
{
	MSG_MAP_DATA,  // 绘制地图
	MSG_TACK_DATA, // 绘制轨迹
	MSG_GPX_PARSE, // 解析GPX文件
	MSG_MAP_DATA_2  // 补充绘制地图
};


/*--------------------------------------------------------------------
							MEMORY CONSTANTS
--------------------------------------------------------------------*/
#if 0
#define CONFIG_TASK_MAP_STACK_SIZE (5120)

static struct k_thread task_map_data;
static K_KERNEL_STACK_DEFINE(task_map_stack, CONFIG_TASK_MAP_STACK_SIZE);

k_tid_t task_map_tid = NULL;

#endif

/*****************************************************************************
*
*
*						地图flag及相关变量汇总
*
*
*****************************************************************************/

//*********** 当前正在绘制的缓冲区 ******************************************
lv_color_t* map_buf = NULL;

//*********** 当前地图绘制的中心点 ******************************************
extern utl_geo_coord_t m_draw_coord;

//*********** 当前的经纬度 *************************************************
extern utl_geo_coord_t m_cur_coord;

//*********** 当前地图绘制的最小\最大边界************************************
extern utl_geo_coord_t m_coord_min;
extern utl_geo_coord_t m_coord_max;


//*********** 当前地图的屏幕大小 ********************************************
extern struct rect m_rect;

//*********** 当前地图绘制状态(用于在paint刷新中判断各种模式) ***************
uint8_t m_data_ready = 0;

//*********** 当前地图后台是否正在绘制 **************************************
bool m_is_busy = false;


//*********** 地图全局信息 **************************************************
struct gps_map_t gps_map_info;

//*********** 信号量（加载UI,再做画线任务） *********************************



//*********** 当前展示的地图比例尺(m_scale是绘制比例尺) *********************
extern int scale_count;

//*********** 当前坐标超出了设定的范围(需刷新改变中心点的地图) **************
extern int out_of_range; 

//*********** 授权码的名字 **************************************************
extern char license_name[40];

//*********** 关闭授权任务 **************************************************
void map_license_task_close(void);

//*********** 地图授权状态 **************************************************
int no_license_flag = 0;

//*********** 地图绘制中断 **************************************************
int interrupt_flag = 0;

//*********** 地图当前状态 **************************************************
draw_status_t map_status;

//************** 切换起点终点 ***********************************************
extern bool revert_pos;
//************** 授权码 ***********************************************
uint8_t code[64] = {0X3E,0XF3,0XBF,0X21,0XEB,0X95,0X5D,0XE3,0X97,0X84,0X02,0XFE,0X66,0X36,0XE7,0X68,0X12,0X26,0XC3,0X74,0X5F,0XE8,0X05,0XEF,0XE8,0X12,0X18,0X9E,0XE9,0XF4,0X16,0X39,0X45,0X02,0XBC,0XCC,0X0F,0X21,0X39,0XB2,0XDB,0XA9,0X83,0XE6,0X8A,0X92,0X1D,0XAB,0X55,0X96,0X2D,0XED,0XFB,0X18,0XA8,0X41,0X8E,0X3C,0X66,0XA9,0X11,0XCF,0X7B,0XB4};







/*****************************************************************************
*
*
*						轨迹相关
*
*
*****************************************************************************/

//*********** 轨迹颜色 ******************************************************
static gfx_color_t g_color = GFX_RGB(255,0,0);

//*********** 轨迹宽度 ******************************************************
static int16_t g_width = 10;

//*********** 记录|返航|导航模式用于读取轨迹的函数指针 **********************
static mlp_get_coord_fun_t g_fun;

//*********** 轨迹起点 ******************************************************
static gfx_point_t g_start;

//*********** 轨迹终点 ******************************************************
static gfx_point_t g_end;

//*********** 轨迹返航的当前轨迹索引 ****************************************
extern uint16_t num_of_tra;

//************** 轨迹返航时的错误颜色：红色 *********************************
gfx_color_t error_color = GFX_RGB(255, 0, 0);

int rt_sig;


//绘制的第几个buffer
static int buffer_index = 0;

int get_draw_buffer_index()
{
	return buffer_index;
}

void set_draw_buffer_index(int index)
{
	buffer_index = index;
}


extern int gfx_wdth;
extern int gfx_hght;


extern int judge_buf_index(int scale);

/*****************************************************************************
*
* --------------------------   OVER  ----------------------------------------						
*
*****************************************************************************/




/*--------------------------------------------------------------------
								MACROS
--------------------------------------------------------------------*/

/*--------------------------------------------------------------------
							PROCEDURES
--------------------------------------------------------------------*/

//*********** 是否打开路网与水域绘制 **********
#define LANDUSE_ENABLE 1
#define WATER_ENABLE 1


/*********************************************************************
 *
 *  功能描述
 *    设置road,water,landuse地图显示的颜色与粗细
 *
 **********************************************************************/
void set_map_line_size_color(void)
{

	set_motorway_roads_color(12, HIGHWAY_COLOR);
	set_trunk_roads_color(8, PEN_COLOR);
	set_primary_roads_color(6, PEN_COLOR);
	set_other_roads_color(4, GFX_RGB(139, 141, 161));

	set_water_color(1, GFX_RGB(157, 204, 255));

	set_commercial_landuse_color(1, GFX_RGB(240, 217, 252));
	set_heath_landuse_color(1, GFX_RGB(255, 223, 223));
	set_park_landuse_color(1, GFX_RGB(176, 232, 207));
	set_other_landuse_color(1, GFX_RGB(225, 235, 242));
}

extern bool rec_get_coord(double *lon, double *lat);


/**************************************************************************
 *
 *		  			地图的3种类展示API
 *
 **************************************************************************/

// 地图展示模式 0 - 地貌  1 - 等高线  2 - 混合
int map_show_mode = 0;

// 设置地图展示模式
void set_map_show_mode(int mode)
{
	map_show_mode = mode;
}

// 获取地图展示模式
int get_map_show_mode()
{
	return map_show_mode;
}


/************************************************************************
 *
 *						多文件打开相关
 *
 ************************************************************************/
 #include <stdio.h>
 #include <dirent.h>

#define MAX_FILES 20		  // 假设你想要最多读取20个文件夹
char map_path[MAX_FILES][50]; // MAP文件夹下面的地图文件夹
int map_num = 0;

char map_trk_path[50] = {0};

char map_name[MAX_FILES][20] = {0};

int lon = 0;
int lat = 0;

/*********************************************************************
 *
 *  功能描述
 *    搜寻准备打开的文件夹
 *
 *  输入参数
 *    path：文件夹路径
 *
 **********************************************************************/
int search_map_dir(char *path)
{

#if 1

	// 要扫描的文件夹路径
	int err = 0;
	int map_num = 0;
	//struct fs_dir_t dir;
	// 打开文件夹
	DIR* dir = opendir(path);
	if (NULL == dir)
	{
		printf("Unable to open %s ", path);
		return 0;
	}
	int path_length = strlen(path);
    struct stat st;

	// 扫描文件夹
	while (map_num < MAX_FILES)
	{
		struct dirent* entry;

		//打印dir
	//	printf("-----------------dir = %s \n",dir);
		usleep(100 * 1000);

		entry = readdir(dir);

		if (NULL == entry)
		{
			printf("Unable to read directory\n");
			break;
		}

		if (entry->d_name[0] == '\0')
		{
			break;
		}

        if (strcmp(entry->d_name, ".") == 0 ||
            strcmp(entry->d_name, "..") == 0) {
            continue;
        }

        // 使用 memcpy 复制路径到 map_trk_path
		memset(map_trk_path, 0, 50); 					//清空地图file文件夹名
		memcpy(map_trk_path, path, path_length + 1); 	// 注意加上终止的 '\0'
		strncat(map_trk_path, "/", 1);
#if PRINT_TASK_MAP
		printf("HHH_the_map_trk_path = %s \n", map_trk_path);
#endif

		char tmp[50];
		memcpy(tmp, entry->d_name, 40);
		// 知道了国家名字
#if PRINT_TASK_MAP
		printf("*Country_name = %s \n", tmp); // 修改这里，使用正确的格式化字符串
#endif
		strncat(map_trk_path, tmp, 30);

		strncpy(map_path[map_num], map_trk_path, sizeof(tmp) + 1);
		strncpy(map_name[map_num], entry->d_name, 40);

        // 确保是目录
        if (stat(map_path[map_num], &st) == -1 || !S_ISDIR(st.st_mode)) {
            continue;
        }

#if PRINT_TASK_MAP
		printf("**the_trk_path = %s \n", map_trk_path);
		printf("**the_map_name = %s \n", entry->d_name);
#endif
		map_num++;
	}
	closedir(dir);

	// 打印动态字符串数组
	for (int i = 0; i < map_num; i++)
	{
#if PRINT_TASK_MAP
		printf("map_path[%d] = %s \n", i, map_path[i]);
#endif
	}
	for (int i = 0; i < map_num; i++)
	{
#if PRINT_TASK_MAP
		printf("map_name[%d] = %s \n", i, map_name[i]);
#endif
	}
	return map_num;
#endif
	return 1;
}

extern mlp_map_module_t m_modules[7];

int gps_map_mult_res_check_open(int index,utl_geo_coord_t coord)
{
	char map_file[128];
	int ret = 0;

	sprintf(map_file,"%s/%s",map_path[index],"landuse.mlp");//_compress
	printf("map_file = %s \n",map_file);
	m_modules[1].mlp_fh = xml_lib_fopen(map_file,O_RDONLY);
	sprintf(map_file,"%s/%s",map_path[index], "landuse.idx");
	printf("map_file = %s \n",map_file);
	m_modules[1].idx_fh = xml_lib_fopen(map_file,O_RDONLY);
	m_rect.height = gfx_hght;
	m_rect.width = gfx_wdth;
	if(1 == location_is_in_mapfile(m_rect,coord))
	{
		sprintf(map_file,"%s/%s",map_path[index], "roads.mlp");
		printf("map_file = %s \n",map_file);
		m_modules[0].mlp_fh = xml_lib_fopen(map_file,O_RDONLY);

		sprintf(map_file,"%s/%s",map_path[index], "roads.idx");
		printf("map_file = %s \n",map_file);
		m_modules[0].idx_fh = xml_lib_fopen(map_file,O_RDONLY);

		sprintf(map_file,"%s/%s",map_path[index],"water.mlp");
		printf("map_file = %s \n",map_file);
		m_modules[2].mlp_fh = xml_lib_fopen(map_file,O_RDONLY);

		sprintf(map_file,"%s/%s",map_path[index],"water.idx");
		printf("map_file = %s \n",map_file);
		m_modules[2].idx_fh = xml_lib_fopen(map_file,O_RDONLY);

		//获取已经找到的文件
		//memset(map_open_file,0,50);
		//memcpy(map_open_file,map_path[index],strlen(map_path[index]));

		//暂时先默认在多文件读取时，也初始化dbf文件，后续同一处理		
		sprintf(map_file,"%s/%s",map_path[index],"landuse.db");
		m_modules[1].db_hndl = db_init(map_file);
		return 0;
	}
	else
	{
		//printf("BHBHBHBHBH \n");
		xml_lib_fclose(m_modules[1].mlp_fh);
		m_modules[1].mlp_fh = 0;
		xml_lib_fclose(m_modules[1].idx_fh);
		m_modules[1].idx_fh = 0;

		return -1;
	}
}

//总共有多少个地图区域
static int mult_res_total = 0;

//第几个图形区域
static int mult_res_active = 0;

//资源打开状态
int mult_open_status(utl_geo_coord_t coord,int total)
{
	mult_res_total = total;
	//printf("YYY_the_mult_res_total = %d \n",mult_res_total);
	//如果搜索MAP文件夹没有国家地图
	if(mult_res_total == 0)
	{
		return -1;
	}

	//如果搜索MAP文件夹有超过1个国家地图
	if(mult_res_total >= 1)
	{
		//如果当前国家的地图文件打开了
		if(0 == gps_map_mult_res_check_open(mult_res_active,coord))
		{
			return 0;
		}

		//如果没有打开地图文件，则需要打开相应国家的地图文件
		for(int i = 0; i < mult_res_total ; i++)
		{
			if(0 == gps_map_mult_res_check_open(i,coord))
			{
				//printf("YYYYYYYYYYYYYYYY \n");
				mult_res_active = i;
				return 0;
			}
		}
	}
	return -2;
}

/********************************************************
*
*	功能描述：	检查当前经纬度是否在
*
*		参数： 当前位置
*
********************************************************/
int check_cur_is_in_mapfile(utl_geo_coord_t coord)
{
//	int reselt = 1
	m_rect.height = gfx_hght;
	m_rect.width = gfx_wdth;
	
	int ret =  location_is_in_mapfile(m_rect,coord);
	//printf("check_cur_is_in_mapfile:ret = %d \n",ret);
	if(ret == 1)
	{
		return 1;
	}
	else
	{
		mult_res_active = 0;
		mult_res_total = 0;
		return 0;
	}
}


//关闭地图文件
void map_close_files()
{
	//printf("map_close_files \n");
	mlp_map_closefiles();
	//printf("map_close_files_over \n");
}


int get_map_open_file_nums()
{
	//return open_file_nums;
}


static uint8_t cur_coor_in(char *dir ,utl_geo_coord_t coord)
{
	char map_file[128];
	int ret = 0;
	
	file_t m_cur_file = 0;

	sprintf(map_file,"%s/%s",dir, "landuse.idx");
	printf("map_file = %s \n",map_file);
	m_cur_file = xml_lib_fopen(map_file,O_RDONLY);
	m_rect.height = gfx_hght;
	m_rect.width = gfx_wdth;
	
	ret = location_is_in_mapfile_new(m_rect,coord,m_cur_file);
	
	xml_lib_fclose(m_cur_file);
	m_cur_file = 0;
	
	return ret;
}


/*********************************************************************
 *
 *  功能描述
 *    打开地图多文件路径
 *
 *  输入参数
 *    file_name：文件夹路径
 *	 coord：		需要搜寻的坐标点，即m_draw_corrd
 *
 **********************************************************************/
 
int mlp_map_open_multi_file(char *file_name, utl_geo_coord_t coord)
{
	// 遍历MAP文件夹
	gps_map_info.mult_res_total = search_map_dir(file_name);

	printf("mult_res_total = %d \n",gps_map_info.mult_res_total);

	// 获取地图文件打开状态
	int ret = mult_open_status(coord,gps_map_info.mult_res_total);
	return ret;
}



/********************************************************
*
*	功能描述：	如果重新定位后，原经纬度不在打开的区域，则需重新打开对应区域文件
*
*		参数：			 map文件夹路径
*       当前经纬度： 经纬度

*	  返回值： 1 - 成功         0 - 失败
*
********************************************************/

int map_reopen_files(char* file_name,utl_geo_coord_t coord)
{
#if PRINT_TASK_MAP
	printf("map_reopen_files_name = %s \n",file_name);
#endif
	if (access(file_name, F_OK) != 0) return -1;
	return mlp_map_open_multi_file(file_name,coord);
}



/********************************************************
*
*	功能描述：	检查当前经纬度是否在,不在则进行文件搜索
*
*		参数： 当前位置
*	  返回值： 1 - 成功         0 - 失败
*
********************************************************/

#define UI_MAP_FILE_DIR "/victel/map"//用户自己填写

int gps_check_cur_coord(utl_geo_coord_t coord, int reOpen)
{
	int ret = 0;

	if (!reOpen) {
		int ret = check_cur_is_in_mapfile(coord);
		if(ret)
		{
			return 1;
		}
	}

	if(0 == ret)
	{
		map_close_files();
		int open_flag = map_reopen_files(UI_MAP_FILE_DIR,coord);
		if(open_flag == 0)
		{
			return 1;
		}
		else
		{
			return 0;
		}
	}
	return 0;
}

int GuiMapCheckMapFileExist(double lon, double lat, int *exist)
{
    utl_geo_coord_t coord;
    coord.longitude = lon;
    coord.latitude = lat;

    printf("[%s:%d] [info] check coord file, lon:%lf, lat:%lf !!!!\n",
           __FUNCTION__, __LINE__, coord.longitude, coord.latitude);
    if (access(UI_MAP_FILE_DIR, F_OK) != 0) {
        *exist = 0;
        return 0;
    }

    DIR *dir = opendir(UI_MAP_FILE_DIR);
    if (dir == NULL) {
        printf("[%s:%d] [error] reload map successfule!!!!\n", __FUNCTION__,
               __LINE__);
        return -1;
    }

	int dirNum = 0;
	int hasBeenFindFile = 0;
    struct dirent *entry;
    struct stat st;
    char dirPath[256];
    while ((entry = readdir(dir)) != NULL) {
        if (strcmp(entry->d_name, ".") == 0 ||
            strcmp(entry->d_name, "..") == 0) {
            continue;
        }

        snprintf(dirPath, sizeof(dirPath), "%s/%s", UI_MAP_FILE_DIR,
                 entry->d_name);
        if (stat(dirPath, &st) == -1 || !S_ISDIR(st.st_mode)) {
            continue;
        }
		++dirNum;
#if 1
        printf("[%s:%d] [info] check dir:%s, idx:%d!!!!\n", __FUNCTION__, __LINE__,
               dirPath, dirNum);
#endif
        if (cur_coor_in(dirPath, coord)) {
            *exist = 1;
			hasBeenFindFile = 1;
            break;
        }
    }

	if (!hasBeenFindFile) {
        *exist = 0;
	}

    closedir(dir);

    return 0;
}

/*********************************************************************
 *
 *  功能描述
 *    控制下一个即将绘制的画布index
 *
 *  输入参数
 *    count：当前是哪个画布
 *	 flag:  放大or缩小
 *
 *  返回值
 *	 返回下一个即将绘制的画布index
 *
 **********************************************************************/
int num_change(int count, int flag)
{
	int ret = 0;
	if (flag == 1)
	{
		count++;
		if (count == 3)
		{
			count = 0;
		}
	}
	else
	{
		count--;
		if (count == -1)
		{
			count = 2;
		}
	}
	ret = count;
	return ret;
}



/*********************************************************************
 *
 *  功能描述
 *    地图绘制函数
 *
 *  输入参数
 *    coord：		当前画布经纬度范围
 *    index：		要绘制的画布index
 *    zoom： 		通过放大or缩小触发的
 *    draw_flag：是否需要整个刷新
 *    scale：     当前展示的比例尺
 *
 **********************************************************************/

// ... existing code ...

// 修改时间统计宏定义
#define TIMEIT_START() do { \
    struct timeval _t_start, _t_end; \
    gettimeofday(&_t_start, NULL)
    
#define TIMEIT_END(tag) \
    gettimeofday(&_t_end, NULL); \
    long _ms = (_t_end.tv_sec - _t_start.tv_sec) * 1000 + (_t_end.tv_usec - _t_start.tv_usec) / 1000; \
    printf("[%s] use time: %ldms\n", tag, _ms); \
} while(0)


static void gfx_clear_hy(gfx_color_t color, int index)
{
    const size_t pixel_count = gfx_wdth * gfx_hght;

    if(gps_map_info.map_ratio[index].buff) {
        // RGB565格式：使用uint16_t指针，每个像素2字节
        uint16_t* buffer = (uint16_t*)gps_map_info.map_ratio[index].buff;

        // color已经是RGB565格式的gfx_color_t (uint16_t)，直接使用
        const uint16_t rgb565_color = color;

        // 内存块批量操作优化 - 调整为RGB565
        const size_t block_size = 64; // 64像素/次（128字节）
        const size_t blocks = pixel_count / block_size;

        // 批量填充主要区块
        for(size_t b = 0; b < blocks; b++) {
            uint16_t* blk = buffer + b*block_size;
            for(size_t i = 0; i < block_size; i++) {
                blk[i] = rgb565_color;
            }
        }

        // 处理剩余像素
        for(size_t i = blocks*block_size; i < pixel_count; i++) {
            buffer[i] = rgb565_color;
        }
    }
}




// ... existing code ...

// 调用处修改为RGB888黑色（0x000000）
// fill_background(0x000000, index);


void map_draw_line(tree_rect_t coord, int index, int scale)
{
	//绘制耗时
	struct timeval starttime;
	struct timeval endtime;
	// printf("map_draw_line:index = %d ,scale = %d ,out_of_range = %d \n", index, scale,out_of_range);
	gettimeofday(&starttime,NULL);

	gps_map_info.draw_index = index;
	gps_map_info.draw_scale = scale;
	// 1.如果绘制比例尺大于1km比例尺，或小于20m比例尺，则不绘画----------
	if (scale < SCALE_1_1KM || scale > SCALE_1_20M)
	{
#if PRINT_TASK_MAP
		printf("GO_OUT \n");
#endif
		if(scale > SCALE_1_20M){ mlp_ratio_cal(m_draw_coord, scale_count - 1);}
		
		//比例尺太大了，也应该记录一下地图的相关信息
		gps_map_info.map_ratio[index].m_coord_draw = m_draw_coord;
	
		return;
	}

	#if (CONFIGE_ONE_BUF == 1)

	gps_map_info.scale_array_done[scale - 13] = 0;

	#endif

	//int num = get_map_open_file_nums();
#if PRINT_TASK_MAP
	//printf("NOW_HAVE_OPEN_%d_FILES \n",num);
#endif

#if PRINT_TASK_MAP
	printf("NNN_the_scale_array_done[%d]=%d \n",scale - 13,gps_map_info.scale_array_done[scale - 13]);
#endif
	//无需绘制的情况：如果当前比例尺已经绘制，且在安全范围内，则无需刷新
	if(gps_map_info.scale_array_done[scale - 13] == 1/* && out_of_range == 0*/)
	{
		//ui_view_paint2(MAP_VIEW, 0, NULL);
#if PRINT_TASK_MAP
		printf("map_draw_line:No_need_draw! \n");
#endif
		return ;
	}
	
	// 2.要绘制的画布缓冲区，并记录该index------------------------------

	map_buf = gps_map_info.map_ratio[index].buff;
	gps_map_info.draw_index = index;
	set_draw_buffer_index(index);

	if(map_buf == NULL)
	{
#if PRINT_TASK_MAP
		printf("map_draw_line:map_buf = NULL \n");
#endif
		return ;
	}

	// 3.如果需要刷新，首先要全擦-----------------------------

	// gfx_clear(BKGD_COLOR); 

	gfx_clear_hy(BKGD_COLOR, index);


	// 4.当前比例尺的画布绘制状态置0，只有画完了置1---------------------
	gps_map_info.scale_array_done[scale - 13] = 0;
	int another_index =  scale <= 15 ?	scale - 13 +3 : scale - 13 -3;
	
	// 1个buffer负责2个比例尺，当前比例尺绘制了，则另一个比例尺需要置0
#if PRINT_TASK_MAP
	printf("MM_the_another_index = %d \n",another_index);
#endif
	gps_map_info.scale_array_done[another_index] = 0;


	// 5.画布准备开始绘制，先将画布的绘制状态等等置0---------------------
	
	// TIMEIT_START();
	clean_map_building_contour(index);
	// TIMEIT_END("clean_map_building_contour");

	coord.box[0] = m_coord_min.longitude;
	coord.box[1] = m_coord_min.latitude;
	coord.box[2] = m_coord_max.longitude;
	coord.box[3] = m_coord_max.latitude;
#if PRINT_TASK_MAP
	printf("HHH_the_get_map_show_mode = %d \n", get_map_show_mode());
#endif

	// 6.开始绘制：如果是等高线模式，则不进行路网水域土地的回调--------------------
	if (get_map_show_mode() != CONTOUR_MODE)
	{
		landuse_callback(&coord); //绘制土地
		water_callback(&coord); 	//绘制水域
		loads_callback(&coord);	//绘制道路
	}

	if (get_map_show_mode() != CONTOUR_MODE)
	{
		// contour_callback(&coord); //绘制等高线
	}


	// 9.绘制完成，对应画布与比例尺的绘制状态置1，并刷新页面
	gps_map_info.map_ratio[index].done = 1;
	gps_map_info.scale_array_done[scale - 13] = 1;
	
	m_data_ready |= 0x01;

	gettimeofday(&endtime,NULL);    
	uint32_t delta_ms = (endtime.tv_sec-starttime.tv_sec)*1000+(endtime.tv_usec-starttime.tv_usec)/1000;
	printf("map_draw_line_over delta_ms:%d,scale=%d \n",delta_ms,scale);

//	ui_view_paint2(MAP_VIEW, 0, NULL);
}


//gfx_draw_line(100,100,200,200,g_color,g_width);



/***************************************************
 *
 *	描述：用于记录6个比例尺，哪些是绘画完成的
 *
 *   参数：scale 比例尺
 *
 ***************************************************/
void func_done(int scale)
{
	for (int i = 0; i < 6; i++)
	{
		if (scale - 13 - 1 <= i && scale - 13 + 1 >= i)
		{
			gps_map_info.scale_array_done[i] = 1;
		}
		else
		{
			gps_map_info.scale_array_done[i] = 0;
		}
	}
}




//绘制前先计算地图绘制的中心点
void compute_map_center(int index ,int range_flag)
{

	// 如果是放大缩小按钮触发，则不用计算
	if(gps_map_info.zoom != 0)
	{
#if PRINT_TASK_MAP
		printf("compute_map_center: gps_map_info.zoom = %d \n",gps_map_info.zoom);
#endif
		gps_map_info.zoom = 0;
		return ;
	}

	// 如果准备绘制的buf有记录，且边缘flag为0，则用地图绘制中心点m_draw_coord用原来的
#if PRINT_TASK_MAP
	printf("compute_map_center: m_coord_draw.lon=%f,lat=%f \n",m_draw_coord.longitude,m_draw_coord.latitude);
#endif
	if(gps_map_info.map_ratio[index].m_coord_draw.longitude != 0 && range_flag == 0)
	{
		m_draw_coord = gps_map_info.map_ratio[index].m_coord_draw;
	}
	else
	{
		gps_map_info.map_ratio[index].m_coord_draw = m_draw_coord;
	}
#if PRINT_TASK_MAP
	printf("compute_map_center: gps_map_info.map_ratio[%d].m_coord_draw.lon=%f,lat=%f \n",index,gps_map_info.map_ratio[index].m_coord_draw.longitude,gps_map_info.map_ratio[index].m_coord_draw.latitude);
#endif
}














extern void Map_reply_id_addr(unsigned char* recv,void(*p)(void *, uint16_t));


extern void task_to_paint();




void map_draw(int scale);

//地图的线程
static pthread_t map_thread;

static int thread_id1 = 1;

//监听事件组
//EVENT_DISPATCHER *g_eventDispatch;


/**************************************************************s
*
*				地图绘制【绘制土地，道路，水域】
*
**************************************************************/
 void EventCb_map_draw(int sig)
{
#if PRINT_TASK_MAP
    // printf("EventCb_map_draw %s %d\n", (char *)data, dataLen);
#endif
	if (m_is_busy == false)
	{
		m_is_busy = true;

		tree_rect_t m_coord = {0};

		int map_draw_index = gps_map_info.show_index;
		
		gps_map_info.is_updata_done = 0; // 开始新一轮刷新
		map_draw_line(m_coord, map_draw_index, scale_count);
		gps_map_info.is_updata_done = 1; // 本轮刷新结束
#if PRINT_TASK_MAP
		printf("AAA_the_map_ratio.[%d].m_coord_draw.lon=%f,lat=%f \n",map_draw_index,gps_map_info.map_ratio[map_draw_index].m_coord_draw.longitude,gps_map_info.map_ratio[map_draw_index].m_coord_draw.latitude);
#endif
		m_data_ready |= 0x01;
		m_is_busy = false;
	}
	map_status = MAP_STATUS;
#if PRINT_TASK_MAP
	printf("ZA:refresh \n");
#endif
	task_to_paint();
	//ui_view_paint2(MAP_VIEW, 0, NULL);
}




/**************************************************************
*
*		地图后台绘制【绘制当前比例尺左右2边的地图】
*
**************************************************************/
 void EventCb_map_draw_supp(int sig)
{
#if PRINT_TASK_MAP
    // printf("EventCb_map_draw_supp %s %d\n", (char *)data, dataLen);
#endif

	if (m_is_busy == false)
	{
		m_is_busy = true;

		// 当前展示的地图index
		int cur_map = gps_map_info.show_index;
		// 即将绘制的地图位于当前展示地图的左or右
		int right_left = 0;
		// 即将绘制的地图的比例尺
		int again_scale = 0;

		int ready_draw_index = 0;
		
		
		for(int i = 0;i < 6;i++)
		{
#if PRINT_TASK_MAP
			printf("YES_the_scale_array_done[%d] = %d \n",i,gps_map_info.scale_array_done[i]);
#endif
		}

		if (gps_map_info.zoom != 2) {
			if (gps_map_info.scale_array_done[scale_count - 12] == 0) {
				right_left = 2;
				again_scale = scale_count + 1;
			} else if (gps_map_info.scale_array_done[scale_count - 14] == 0) {
				right_left = 1;
				again_scale = scale_count - 1;
			} else {
				m_is_busy = false;
				return ;
			}
		} 
		else  {
			if (gps_map_info.scale_array_done[scale_count - 14] == 0) {
				right_left = 1;
				again_scale = scale_count - 1;
			} else if (gps_map_info.scale_array_done[scale_count - 12] == 0) {
				right_left = 2;
				again_scale = scale_count + 1;
			} else {
				m_is_busy = false;
				return ;
			}
		} 

#if PRINT_TASK_MAP
		printf("MSG_MAP_DATA_2: right_left = %d ,again_scale = %d \n,scale_count = %d ,cur_map = %d \n",right_left,again_scale,scale_count,cur_map);
#endif
		//确定当前准备绘制的地图index
		ready_draw_index = num_change(cur_map, right_left);

		//确定当前地图的中心点坐标与经纬度范围
//				compute_map_center(ready_draw_index, out_of_range);
		m_coord_min = m_coord_max = m_draw_coord;
		mlp_ratio_cal(m_draw_coord, again_scale);

#if PRINT_TASK_MAP
		printf("the_ready_draw_index = %d ,m_draw_coord.lon = %f ,lat = %f \n", ready_draw_index, m_draw_coord.longitude, m_draw_coord.latitude);
		printf("the_ready_draw_index = %d ,m_coord_min.lon = %f ,lat = %f \n", ready_draw_index, m_coord_min.longitude, m_coord_min.latitude);
		printf("the_ready_draw_index = %d ,m_coord_max.lon = %f ,lat = %f \n", ready_draw_index, m_coord_max.longitude, m_coord_max.latitude);
#endif
		tree_rect_t m_coord = {0};
		gps_map_info.is_updata_done = 0; // 开始新一轮刷新
		//printf("TEST_draw_other:again_scale=%d \n",again_scale);
		map_draw_line(m_coord, ready_draw_index, again_scale);
		gps_map_info.is_updata_done = 1; // 本轮刷新结束

		//将各地图回复到原来的样子
		m_coord_min = m_coord_max = m_draw_coord;
		mlp_ratio_cal(m_draw_coord, scale_count);
		

		m_is_busy = false;
	}
	//ui_view_paint2(MAP_VIEW, 0, NULL);
}

//绘制其它的地图【向地图线程发送补充绘制的消息】
void draw_other_map(int map_index)
{

#if (CONFIGE_ONE_BUF == 1)

	return ;

#else

	//当前画面绘制完成后，且轨迹也绘制完成后，才会进行绘制补偿
	if(gps_map_info.scale_array_done[scale_count - 13] != 0 && gps_map_info.map_ratio[map_index].cur_point_show)
	{
		//如果更高|更低一级比例尺已经绘制，则无需补充绘制
		int other_scale = scale_count;
		if(other_scale == 13)
		{
			other_scale++;
		}
		else if(other_scale == 18)
		{
			other_scale--;
		}
#if PRINT_TASK_MAP
		printf("map_index=%d,scale_array_done[scale_count-12]=%d \n",other_scale - 12,gps_map_info.scale_array_done[other_scale - 12]);
		printf("map_index=%d,scale_array_done[scale_count-14]=%d \n",other_scale - 14,gps_map_info.scale_array_done[other_scale - 14]);
#endif
		if(gps_map_info.scale_array_done[other_scale - 12] && gps_map_info.scale_array_done[other_scale - 14])
		{
#if PRINT_TASK_MAP
			printf("NO_send_msg \n");
#endif
		}
		else
		{	
			map_draw_again();
		}
	}
#endif
}


/**************************************************************
*
*				轨迹绘制【记录|返航|导航模式】
*
**************************************************************/
 void EventCb_map_track(int sig)
{
    //printf("EventCb_map_track %s %d\n", (char *)data, dataLen);
	if(m_is_busy == false )
	{
		double lat, lon;
		uint32_t cnt;
		gfx_point_t last_point;
		gfx_point_t final_last_point;
		gfx_point_t cur_point;
		utl_geo_coord_t coord;
		m_is_busy = true;
		cnt = 0;

		/*********************** 绘制前先计算轨迹相关信息 *******************************/
		int cur_map_index = gps_map_info.show_index;
		//  printf("AAA_the_cur_map_index = %d ,scale_count = %d \n",cur_map_index,scale_count);
		m_draw_coord = gps_map_info.map_ratio[cur_map_index].m_coord_draw;
		//  printf("AAA_the_map_ratio.[%d].m_coord_draw.lon=%f,lat=%f \n",cur_map_index,gps_map_info.map_ratio[cur_map_index].m_coord_draw.longitude,gps_map_info.map_ratio[cur_map_index].m_coord_draw.latitude);
		m_coord_min = m_draw_coord;
		mlp_ratio_cal(m_draw_coord, scale_count);
		/********************************  OVER  ************************************/

		//  printf("AAA_the_m_coord_min.lon = %f ,lat = %f \n", m_coord_min.longitude, m_coord_min.latitude);
		//  printf("AAA_the_m_coord_max.lon = %f ,lat = %f \n", m_coord_max.longitude, m_coord_max.latitude);

		gps_map_info.map_ratio[cur_map_index].cur_point_show = 0;
		map_buf = gps_map_info.map_ratio[cur_map_index].buff;
		switch (get_map_mode())
		{
		case NAV_MODE:
			/***********************************
			 * 
			 * 		这部分是导航的轨迹[暂定蓝色]
			 * 
			************************************/
			while (g_fun(&lon, &lat))
			{
				coord.longitude = lon;
				coord.latitude = lat;
				map_coord_get_pos(coord, &cur_point);
				if (cnt > 0)
				{
					// printf("MSG_TACK_DATA_NAV:AAAA_%d_x1 = %d ,y1 = %d ,x2 = %d ,y2 = %d ,m_rect.top + m_rect.height = %d ,cnt = %d \n", cur_map_index, last_point.x, (m_rect.top + m_rect.height) - last_point.y, cur_point.x, (m_rect.top + m_rect.height) - cur_point.y, m_rect.top + m_rect.height, cnt);
					gfx_draw_line(last_point.x, (m_rect.top + m_rect.height) - last_point.y, cur_point.x, (m_rect.top + m_rect.height) - cur_point.y, g_color, g_width);
				}
				else
				{
					g_start = cur_point;
				}
				cnt++;
				last_point = cur_point;
				final_last_point = last_point;
			}
			/***********************************
			 * 
			 * 		这部分是记录的轨迹[暂定绿色]
			 * 
			************************************/
			g_color = GFX_RGB(0, 255, 0);	//绿色
			g_width = 8;					//宽度为8
			m_data_ready &= 0xFD;
			while (record_get_coordinate(&lon, &lat))
			{
				coord.longitude = lon;
				coord.latitude = lat;
				map_coord_get_pos(coord, &cur_point);
				if (cnt > 0)
				{
					//printf("MSG_TACK_DATA_REC:AAAA_%d_x1 = %d ,y1 = %d ,x2 = %d ,y2 = %d ,m_rect.top + m_rect.height = %d ,cnt = %d \n", cur_map_index, last_point.x, (m_rect.top + m_rect.height) - last_point.y, cur_point.x, (m_rect.top + m_rect.height) - cur_point.y, m_rect.top + m_rect.height, cnt);
					gfx_draw_line(last_point.x, (m_rect.top + m_rect.height) - last_point.y, cur_point.x, (m_rect.top + m_rect.height) - cur_point.y, g_color, g_width);
				}
				else
				{
					//g_start = cur_point;
				}
				cnt++;
				last_point = cur_point;
			}
			break;

		
		//记录模式
		case REC_MODE:
			while (g_fun(&lon, &lat))
			{
				coord.longitude = lon;
				coord.latitude = lat;
				map_coord_get_pos(coord, &cur_point);
				if (cnt > 0)
				{
					// printf("MSG_TACK_DATA:AAAA_%d_x1 = %d ,y1 = %d ,x2 = %d ,y2 = %d ,m_rect.top + m_rect.height = %d ,cnt = %d \n", cur_map_index, last_point.x, (m_rect.top + m_rect.height) - last_point.y, cur_point.x, (m_rect.top + m_rect.height) - cur_point.y, m_rect.top + m_rect.height, cnt);
					gfx_draw_line(last_point.x, (m_rect.top + m_rect.height) - last_point.y, cur_point.x, (m_rect.top + m_rect.height) - cur_point.y, g_color, g_width);
				}
				else
				{
					g_start = cur_point;
				}
				cnt++;
				last_point = cur_point;
				final_last_point = last_point;
			}

			break;
		//返航模式
		case RET_MODE:
			while (g_fun(&lon, &lat))
			{
				coord.longitude = lon;
				coord.latitude = lat;
				map_coord_get_pos(coord, &cur_point);
				if (cnt > 0)
				{
					// printf("MSG_TACK_DATA:BBBB_%d_x1 = %d ,y1 = %d ,x2 = %d ,y2 = %d ,m_rect.top + m_rect.height = %d ,cnt = %d ,num_of_tra = %d \n", cur_map_index, last_point.x, (m_rect.top + m_rect.height) - last_point.y, cur_point.x, (m_rect.top + m_rect.height) - cur_point.y, m_rect.top + m_rect.height, cnt,num_of_tra);
					if (num_of_tra == cnt) // 如果走错的话，则需要标记为红色
					{
						gfx_draw_line(last_point.x, (m_rect.top + m_rect.height) - last_point.y, cur_point.x, (m_rect.top + m_rect.height) - cur_point.y, error_color, g_width);
					}
					else
					{
						gfx_draw_line(last_point.x, (m_rect.top + m_rect.height) - last_point.y, cur_point.x, (m_rect.top + m_rect.height) - cur_point.y, g_color, g_width);
					}
				}
				else
				{
					g_start = cur_point;
				}
				cnt++;
				last_point = cur_point;
				final_last_point = last_point;
			}
			break;
		}
		if (cnt >= 2)
		{
			g_end = final_last_point;
			g_start.y = (m_rect.top + m_rect.height) - g_start.y;
			g_end.y = (m_rect.top + m_rect.height) - g_end.y;
		}

		
		//************************* 画虚线 **************************
		if ((get_map_mode() & NAV_MODE) || (get_map_mode() & RET_MODE))
		{
			gfx_point_t start, end, cur;
			gfx_color_t start_color, end_color;

			map_coord_get_pos(m_cur_coord, &cur);
			// printf("LL_the_cur.x = %d,y = %d \n", cur.x, cur.y);
			int point_x = cur.x;
			int point_y = (m_rect.top + m_rect.height) - cur.y;
			map_trk_get_pos(&start, &end);
			// printf("LL_the_start.x = %d,y = %d \n", start.x, start.y);
			// printf("LL_the_end.x = %d,y = %d \n", end.x, end.y);

			if(!revert_pos)
			{
				end_color = GFX_RGB(255, 0, 0);
				start_color = GFX_RGB(0, 255, 0);
			}
			else
			{
				start_color = GFX_RGB(255, 0, 0);
				end_color = GFX_RGB(0, 255, 0);

			}
			
			gfx_color_set(start_color);
			gfx_fill_circle(start.x, start.y, 6);
			gfx_color_set(end_color);
			gfx_fill_circle(end.x, end.y, 6);
			gfx_draw_dash_line(point_x, point_y, start.x, start.y, start_color, 4, 12);
			gfx_draw_dash_line(point_x, point_y, end.x, end.y, end_color, 4, 12);
			
		}
		map_status = FLUSH_STATUS;
		out_of_range = 0;
		m_data_ready |= 0x02;
		m_is_busy = false;
	}
}

void func(void *tx_buffer, uint16_t length)
{
	printf("func \n");
}

//地图的线程函数
 void *attach_function_map(void *arg)
{
	int thread_id = *(int *)arg;
	tree_rect_t m_coord = {0};
	char path[20];
	int no_license_flag = 0;
	int SIGUSR3 = SIGRTMIN + 1;
	rt_sig = SIGUSR3;
#if PRINT_TASK_MAP
    printf("线程 %d 启动\n", thread_id);
#endif
	//2.给地图线程创建一个监听事件数组
	//g_eventDispatch = LibEventDispatcherCreate();
    //EVENT_HANDLE *handle =  LibEventDispatcherAddLister(g_eventDispatch, MAP_DRAW, EventCb_map_draw);
	//EVENT_HANDLE *handle1 =  LibEventDispatcherAddLister(g_eventDispatch, MAP_OTHER, EventCb_map_draw_supp);
	//EVENT_HANDLE *handle2 =  LibEventDispatcherAddLister(g_eventDispatch, MAP_TRACK, EventCb_map_track);	//添加轨迹绘制事件
	signal(SIGUSR1, EventCb_map_draw);
    signal(SIGUSR2, EventCb_map_draw_supp);
	signal(SIGUSR3, EventCb_map_track);

#if PRINT_TASK_MAP
	printf("map:task_map_thread \n");
#endif

	//----------------------1.获取存储的授权码--------------------------------------
	// extern int nvram_config_get(const char *name, void *data, int max_len);
	// nvram_config_get(license_name, code, 64);
	// for (int i = 0; i < 64; i++)
	// {
	// 	printf("x_code[%d] = 0x%02x \n", i, code[i]);
	// }
	
	//3.获取授权码--------------------------------------
	//GuiMapGetLicenseLoopTask(code, sizeof(code));

/*
	uint8_t device_id[40] = {0};
	Map_reply_id_addr(device_id, func);
	for(int i = 0;i < 40;i++)
	{
		printf("%02x ",device_id[i]);
	}
*/

	//计算好要渲染的矩形宽高	
    m_rect.left = 0; 
	m_rect.top = 0;
 	m_rect.width = gfx_wdth;
 	m_rect.height = gfx_hght;

#if 1
	int init_ret = mlp_map_init(road_color_callback, landuse_color_callback, water_color_callback, contour_color_callback, code);

	printf("ZA:init_ret = %d \n",init_ret);
	if (init_ret == 0)
	{
		printf("error: CHECK LISENCE FAIL!! \n");
		no_license_flag = 4; // 可以提示【地图校验失败】
		//map_task_close();
		pthread_exit(NULL);
	}
	printf("[%s:%d] Ui Map CHECK_LISENCE ok!!!!!\n", __func__, __LINE__);
#endif

	//4.多文件打开-------------------------------
	printf("NNN_m_draw_coord.lon=%f ,lat=%f \n",m_draw_coord.longitude,m_draw_coord.latitude);
	set_map_line_size_color();

    //MapFileCleanDownTmpFile();  // 清除下载临时文件，防止下载中途断电导致的文件遗留
#if 0
    int open_status = mlp_map_open_multi_file(UI_MAP_FILE_DIR, m_draw_coord);
    if (open_status == 0)
	{
		no_license_flag = 0;
		printf(" Open_map_resource_success! \n");
	}
	else if (open_status == -1)
	{
		no_license_flag = 2; // 可以提示【没有地图文件】
		// 没有地图文件
		printf(" Have_no_map_file! \n");
		// 关闭地图任务
		//map_task_close();
		return;
	}
	else
	{
		no_license_flag = 3; // 可以提示【请放入正确的地图文件】
		// 没有正确的地图文件
		printf(" No_correct_map_file! \n");
		// 关闭地图任务
	//	map_task_close();
		return;
	}
#endif
//printf("ZA:Sleep11 \n");

	//5.地图的初始化-------------------------------

	//printf("ZA:Sleep \n");

	
	//休眠1s
	//sleep(1000);
	//printf("ZA:Sleep0 \n");
	map_draw(scale_count);
	//printf("ZA:Sleep1 \n");


/*
	//根据比例尺，计算地图绘制的经纬度范围等数据
 	m_coord_min = m_coord_max = m_draw_coord;
 	//printf("m_draw_coord = %f \n",m_draw_coord.longitude);
 	mlp_ratio_cal(m_draw_coord, scale_count);
 	
 	//printf("22m_coord_min.lon=%f,lat=%f \n",m_coord_min.longitude,m_coord_min.latitude);
	//printf("22m_coord_max.lon=%f,lat=%f \n",m_coord_max.longitude,m_coord_max.latitude);

	//根据计算好的数据，调用map_draw_line来绘制地图
	int map_draw_index = gps_map_info.show_index;
	gps_map_info.is_updata_done = 0; // 开始新一轮刷新
	map_draw_line(m_coord, map_draw_index, scale_count);

*/


}



/*********************************************************************
 *
 *	功能描述：创建地图串口授权任务
 *
 **********************************************************************/
void map_license_task_create(void)
{
	if (map_thread == 0)
	{
		if (pthread_create(&map_thread, NULL, attach_function_map, &thread_id1) != 0) {
			perror("创建线程 1 失败");
			exit(1);
		}
    }
}

void map_license_task_close(void)
{
	if (map_thread != 0)
	{
		pthread_cancel(map_thread);
		pthread_join(map_thread, NULL);
		map_thread = 0;
	}
}




















#if 0
/*********************************************************************
 *
 *  功能描述：地图绘制任务
 *
 **********************************************************************/
static void task_map_thread(void)
{
	return;


	//printf("map:task_map_thread \n");
	int ready_draw_index = 0;
	uint8_t code[64] = {0};

	//----------------------1.获取存储的授权码--------------------------------------
	extern int nvram_config_get(const char *name, void *data, int max_len);
	nvram_config_get(license_name, code, 64);
	for (int i = 0; i < 64; i++)
	{
		//printf("x_code[%d] = 0x%02x \n", i, code[i]);
	}
	int is_empty = 1; // 假设数组元素全为0时，flag为1
	for (int j = 0; j < 64; j++)
	{
//		if (code[j] != 0)
//		{
//			is_empty = 0; // 找到一个非0元素，flag置0
//			break;
//		}
	}
	if (is_empty)
	{
		////printf("RUTCD \n");
		// map_task_close();
		//no_license_flag = 1;
		//return;
	}
	else
	{
		no_license_flag = 0;
	}
	tree_rect_t m_coord = {0};

	//----------------------2.多文件打开-------------------------------
	char *path = DATABASE_PATH "MAP/map";
	int open_status = mlp_map_open_multi_file(path, m_draw_coord);
	if (open_status == 0)
	{
//		no_license_flag = 0;
//		LV_LOG_USER(" Open_map_resource_success! \n");
	}
	else if (open_status == -1)
	{
		no_license_flag = 2; // 可以提示【没有地图文件】
		// 没有地图文件
		LV_LOG_USER(" Have_no_map_file! \n");
		// 关闭地图任务
		//  map_task_close();
//		return;
	}
	else
	{
//		no_license_flag = 3; // 可以提示【请放入正确的地图文件】
		// 没有正确的地图文件
		LV_LOG_USER(" No_correct_map_file! \n");
		// 关闭地图任务
		//  map_task_close();
//		return;
	}

	//----------------------3.地图的初始化-------------------------------
	set_map_line_size_color();
	struct app_msg msg = {0};
	int ret;
	int err;
	int init_ret = 0;
#if (LANDUSE_ENABLE == 1 && WATER_ENABLE == 1)
	init_ret = mlp_map_init(road_color_callback, landuse_color_callback, water_color_callback, contour_color_callback, code);
#elif (LANDUSE_ENABLE == 1 && WATER_ENABLE == 0)
	init_ret = mlp_map_init(road_color_callback, landuse_color_callback, NULL, contour_color_callback, code);
#elif (LANDUSE_ENABLE == 0 && WATER_ENABLE == 1)
	init_ret = mlp_map_init(road_color_callback, NULL, water_color_callback, contour_color_callback, code);
#elif (LANDUSE_ENABLE == 0 && WATER_ENABLE == 0)
	init_ret = mlp_map_init(road_color_callback, NULL, NULL, NULL, code);
#endif

	if (init_ret == 0)
	{
		//printf("CHECK_LISENCE_FAIL!! \n");
		no_license_flag = 4; // 可以提示【地图校验失败】
		map_task_close();
		return;
	}

	//----------------------4.监听消息----------------------------------
	err = msg_manager_add_listener(TASK_NAME, os_current_get());
	if (!err)
	{
		SYS_LOG_ERR("failed to add listener %s", TASK_NAME);
	}	

	//休眠1s
	k_sleep(K_MSEC(1000));
	map_draw(scale_count);


	for (;;)
	{
		ret = receive_msg(&msg, OS_FOREVER); // 500ms_reflash
		if (ret != true)
		{
			//printf("receive_msg_error \n");
			continue;
		}
		//printf(" MAPTASK_RECV: %d \n",msg.type);
		switch (msg.type)
		{

			/**************************1.地图绘制任务**********************/

		case MSG_MAP_DATA:
			if (m_is_busy == false)
			{
				m_is_busy = true;
				int map_draw_index = gps_map_info.show_index;
				
				gps_map_info.is_updata_done = 0; // 开始新一轮刷新
				map_draw_line(m_coord, map_draw_index, scale_count);
				gps_map_info.is_updata_done = 1; // 本轮刷新结束
				//printf("AAA_the_map_ratio.[%d].m_coord_draw.lon=%f,lat=%f \n",map_draw_index,gps_map_info.map_ratio[map_draw_index].m_coord_draw.longitude,gps_map_info.map_ratio[map_draw_index].m_coord_draw.latitude);
				
				m_data_ready |= 0x01;
				m_is_busy = false;
			}
			map_status = MAP_STATUS;
			//ui_view_paint2(MAP_VIEW, 0, NULL);
			break;

			/**************************2.地图轨迹绘制任务**********************/

		case MSG_TACK_DATA:
			if(m_is_busy == false )
			{
				double lat, lon;
				uint32_t cnt;
				gfx_point_t last_point;
				gfx_point_t cur_point;
				utl_geo_coord_t coord;
				m_is_busy = true;
				cnt = 0;

				/*********************** 绘制前先计算轨迹相关信息 *******************************/
				int cur_map_index = gps_map_info.show_index;
				//printf("AAA_the_cur_map_index = %d ,scale_count = %d \n",cur_map_index,scale_count);
				m_draw_coord = gps_map_info.map_ratio[cur_map_index].m_coord_draw;
				//printf("AAA_the_map_ratio.[%d].m_coord_draw.lon=%f,lat=%f \n",cur_map_index,gps_map_info.map_ratio[cur_map_index].m_coord_draw.longitude,gps_map_info.map_ratio[cur_map_index].m_coord_draw.latitude);
				m_coord_min = m_draw_coord;
				mlp_ratio_cal(m_draw_coord, scale_count);
				/********************************  OVER  ************************************/

				//printf("AAA_the_m_coord_min.lon = %f ,lat = %f \n", m_coord_min.longitude, m_coord_min.latitude);
				//printf("AAA_the_m_coord_max.lon = %f ,lat = %f \n", m_coord_max.longitude, m_coord_max.latitude);

				gps_map_info.map_ratio[cur_map_index].cur_point_show = 0;
				map_buf = gps_map_info.map_ratio[cur_map_index].buff;
				switch (get_map_mode())
				{
				case NAV_MODE:
				case REC_MODE:
					while (g_fun(&lon, &lat))
					{
						coord.longitude = lon;
						coord.latitude = lat;
						map_coord_get_pos(coord, &cur_point);
						if (cnt > 0)
						{
							//printf("MSG_TACK_DATA:AAAA_%d_x1 = %d ,y1 = %d ,x2 = %d ,y2 = %d ,m_rect.top + m_rect.height = %d ,cnt = %d \n", cur_map_index, last_point.x, (m_rect.top + m_rect.height) - last_point.y, cur_point.x, (m_rect.top + m_rect.height) - cur_point.y, m_rect.top + m_rect.height, cnt);
							gfx_draw_line(last_point.x, (m_rect.top + m_rect.height) - last_point.y, cur_point.x, (m_rect.top + m_rect.height) - cur_point.y, g_color, g_width);
						}
						else
						{
							g_start = cur_point;
						}
						cnt++;
						last_point = cur_point;
					}

					break;

				case RET_MODE:
					while (g_fun(&lon, &lat))
					{
						coord.longitude = lon;
						coord.latitude = lat;
						map_coord_get_pos(coord, &cur_point);
						if (cnt > 0)
						{
							//printf("MSG_TACK_DATA:BBBB_%d_x1 = %d ,y1 = %d ,x2 = %d ,y2 = %d ,m_rect.top + m_rect.height = %d ,cnt = %d ,num_of_tra = %d \n", cur_map_index, last_point.x, (m_rect.top + m_rect.height) - last_point.y, cur_point.x, (m_rect.top + m_rect.height) - cur_point.y, m_rect.top + m_rect.height, cnt,num_of_tra);
							if (num_of_tra == cnt) // 如果走错的话，则需要标记为红色
							{
								gfx_draw_line(last_point.x, (m_rect.top + m_rect.height) - last_point.y, cur_point.x, (m_rect.top + m_rect.height) - cur_point.y, error_color, g_width);
							}
							else
							{
								gfx_draw_line(last_point.x, (m_rect.top + m_rect.height) - last_point.y, cur_point.x, (m_rect.top + m_rect.height) - cur_point.y, g_color, g_width);
							}
						}
						else
						{
							g_start = cur_point;
						}
						cnt++;
						last_point = cur_point;
					}
					break;
				}
				if (cnt >= 2)
				{
					g_end = last_point;
					g_start.y = (m_rect.top + m_rect.height) - g_start.y;
					g_end.y = (m_rect.top + m_rect.height) - g_end.y;
				}

				
				//************************* 画虚线 **************************
				if ((get_map_mode() & NAV_MODE) || (get_map_mode() & RET_MODE))
				{
					gfx_point_t start, end, cur;
					gfx_color_t start_color, end_color;

					map_coord_get_pos(m_cur_coord, &cur);
					//printf("LL_the_cur.x = %d,y = %d \n", cur.x, cur.y);
					int point_x = cur.x;
					int point_y = (m_rect.top + m_rect.height) - cur.y;
					map_trk_get_pos(&start, &end);
					//printf("LL_the_start.x = %d,y = %d \n", start.x, start.y);
					//printf("LL_the_end.x = %d,y = %d \n", end.x, end.y);

//					if(!revert_pos)
//					{
						end_color = GFX_RGB(255, 0, 0);
						start_color = GFX_RGB(0, 255, 0);
//					}
//					else
//					{
//						start_color = GFX_RGB(255, 0, 0);
//						end_color = GFX_RGB(0, 255, 0);
//
//					}
					
					gfx_color_set(start_color);
					gfx_fill_circle(start.x, start.y, 6);
					gfx_color_set(end_color);
					gfx_fill_circle(end.x, end.y, 6);
					gfx_draw_dash_line(point_x, point_y, start.x, start.y, start_color, 4, 12);
					gfx_draw_dash_line(point_x, point_y, end.x, end.y, end_color, 4, 12);
					
				}
				map_status = FLUSH_STATUS;
//				out_of_range = 0;
				m_data_ready |= 0x02;
				m_is_busy = false;
			}
			//ui_view_paint2(MAP_VIEW, 0, NULL);
			break;


			/**************************3.地图轨迹解析任务**********************/

		case MSG_GPX_PARSE:
			if (m_is_busy == false)
			{
				trk_parse();

				double min_lat, min_lon, max_lat, max_lon;
				if (trk_get_border(&min_lon, &min_lat, &max_lon, &max_lat))
				{
					m_draw_coord.latitude = (min_lat + max_lat) / 2;
					m_draw_coord.longitude = (min_lon + max_lon) / 2;
					scale_count = mlp_calc_scale(min_lon, min_lat, max_lon, max_lat,gfx_wdth);
					gps_check_cur_coord(m_draw_coord, 0);

//					//printf("OOO_the_scale_count = %d \n", scale_count);
					int map_index = judge_buf_index(scale_count);
					gps_map_info.show_index = map_index;
					
					memset(gps_map_info.scale_array_done, 0, sizeof(gps_map_info.scale_array_done));
//					//printf("OOO_m_draw_coord.lon = %f ,lat = %f \n",m_draw_coord.longitude, m_draw_coord.latitude);
				}

				out_of_range = 1;
				m_data_ready |= 0x04;
				m_is_busy = false;
			}
			map_draw(scale_count);
			break;

			/**************************4.补充绘制地图任务**********************/

		case MSG_MAP_DATA_2:
			if (m_is_busy == false)
			{
				m_is_busy = true;

				// 当前展示的地图index
				int cur_map = gps_map_info.show_index;
				// 即将绘制的地图位于当前展示地图的左or右
				int right_left = 0;
				// 即将绘制的地图的比例尺
				int again_scale = 0;
				
				
				for(int i = 0;i < 6;i++)
				{
					//printf("YES_the_scale_array_done[%d] = %d \n",i,gps_map_info.scale_array_done[i]);
				}

				if (gps_map_info.zoom != 2) {
					if (gps_map_info.scale_array_done[scale_count - 12] == 0) {
						right_left = 2;
						again_scale = scale_count + 1;
					} else if (gps_map_info.scale_array_done[scale_count - 14] == 0) {
						right_left = 1;
						again_scale = scale_count - 1;
					} else {
						m_is_busy = false;
						break;
					}
				} 
				else  {
					if (gps_map_info.scale_array_done[scale_count - 14] == 0) {
						right_left = 1;
						again_scale = scale_count - 1;
					} else if (gps_map_info.scale_array_done[scale_count - 12] == 0) {
						right_left = 2;
						again_scale = scale_count + 1;
					} else {
						m_is_busy = false;
						break;
					}
				} 


				//printf("MSG_MAP_DATA_2: right_left = %d ,again_scale = %d \n,scale_count = %d ,cur_map = %d \n",right_left,again_scale,scale_count,cur_map);

				//确定当前准备绘制的地图index
				ready_draw_index = num_change(cur_map, right_left);

				//确定当前地图的中心点坐标与经纬度范围
//				compute_map_center(ready_draw_index, out_of_range);
				m_coord_min = m_coord_max = m_draw_coord;
				mlp_ratio_cal(m_draw_coord, again_scale);

				
				//printf("the_ready_draw_index = %d ,m_draw_coord.lon = %f ,lat = %f \n", ready_draw_index, m_draw_coord.longitude, m_draw_coord.latitude);
				//printf("the_ready_draw_index = %d ,m_coord_min.lon = %f ,lat = %f \n", ready_draw_index, m_coord_min.longitude, m_coord_min.latitude);
				//printf("the_ready_draw_index = %d ,m_coord_max.lon = %f ,lat = %f \n", ready_draw_index, m_coord_max.longitude, m_coord_max.latitude);

				gps_map_info.is_updata_done = 0; // 开始新一轮刷新
				map_draw_line(m_coord, ready_draw_index, again_scale);
				gps_map_info.is_updata_done = 1; // 本轮刷新结束

				//将各地图回复到原来的样子
				m_coord_min = m_coord_max = m_draw_coord;
				mlp_ratio_cal(m_draw_coord, scale_count);
				

                m_is_busy = false;
            }
			//ui_view_paint2(MAP_VIEW, 0, NULL);
			break;
		default:
			break;
		}
	}

}
#endif
/*********************************************************************
 *
 *  功能描述：创建地图绘制任务
 *
 **********************************************************************/
#if  0
void map_task_create(void)
{
	// task_create(task_map, NULL, TASK_NAME);
	if (task_map_tid == NULL)
	{
		task_map_tid = k_thread_create(&task_map_data, task_map_stack, K_KERNEL_STACK_SIZEOF(task_map_stack),
									   (k_thread_entry_t)task_map_thread, NULL, NULL, NULL, K_PRIO_COOP(CONFIG_BT_RX_PRIO), 0, K_NO_WAIT);
		k_thread_name_set(&task_map_data, TASK_NAME);
	}
}
#endif
/*********************************************************************
 *
 *  功能描述：注销地图绘制任务
 *
 **********************************************************************/
#if 0
void map_task_close(void)
{
	// task_kill(TASK_NAME);
	// 正常关闭
	if (m_is_busy == false)
	{
		if (task_map_tid != NULL)
		{
			k_thread_abort(task_map_tid);
		}
		task_map_tid = NULL;
		mlp_map_uninit();
	}
	else
	{
		// 中断
		set_mlp_int_flag(true);
		for (int i = 0; i < 9; i++)
		{
			// 等待中断结束
			k_sleep(K_MSEC(10));
			if (m_is_busy == false)
			{
				// 恢复
				set_mlp_int_flag(false);
				break;
			}
		}
		if (task_map_tid != NULL)
		{
			k_thread_abort(task_map_tid);
		}
		task_map_tid = NULL;
		mlp_map_uninit();
	}


}
#endif

/*********************************************************************
 *
 *  功能描述：中断当前绘制操作
 *
 **********************************************************************/
void map_task_close_v2(void)
{

	// 正常关闭
	if (m_is_busy == false)
	{
	}
	else
	{
		// 中断
		set_mlp_int_flag(true);
		for (int i = 0; i < 9; i++)
		{
			// 等待中断结束
			//k_sleep(K_MSEC(10));
			usleep(10*1000);
			if (m_is_busy == false)
			{
				// 恢复
				set_mlp_int_flag(false);
				break;
			}
		}
	}
}




/*********************************************************************
 *
 *  功能描述：控制地图发送绘制消息
 *
 **********************************************************************/

void crtl_check_map_draw()
{
#if PRINT_TASK_MAP
	printf("crtl_check_map_draw:m_is_busy = %d \n",m_is_busy);
#endif
	// 如果当前处于绘制状态
	if (m_is_busy == true)
	{
		// 如果当前绘制的不是正在显示的画布,并且也不是绘制过的画布
#if PRINT_TASK_MAP
		printf("UUUU_gps_map_info.show_index = %d ,gps_map_info.draw_index = %d \n", gps_map_info.show_index, gps_map_info.draw_index);
		printf("UUUU_gps_map_info.map_ratio[%d].done = %d \n", gps_map_info.show_index, gps_map_info.map_ratio[gps_map_info.show_index].done);
#endif
//		if (gps_map_info.show_index != gps_map_info.draw_index && gps_map_info.scale_array_done[scale_count - 13] == 0)
		if (gps_map_info.show_index != gps_map_info.draw_index)
		{
			// 则需要中断
			if (get_mlp_int_flag() == 0)
			{
				interrupt_flag = 1;
				set_mlp_int_flag(true); // 打断
				for (int i = 0; i < 50; i++)
				{
					// 等待中断结束
					//k_sleep(K_MSEC(10));
					usleep(2*1000);
#if PRINT_TASK_MAP

					printf("AAAAA_the_m_is_busy = %d ,is_updata_done = %d \n", m_is_busy, gps_map_info.is_updata_done);
#endif
					// 只有等到绘画结束，且一轮绘制已完成了，才能解除中断
					if (m_is_busy == false && gps_map_info.is_updata_done == 1)
					{
						//由于中断，当前比例尺其实没有绘制完，需要重新绘制
						gps_map_info.scale_array_done[gps_map_info.draw_scale] = 0;
						set_mlp_int_flag(false);
						// 发送消息
						//sleep(10);
						//usleep(10*1000);
#if PRINT_TASK_MAP
						printf("SHUHUHU_gps_map_info.show_index = %d ,gps_map_info.draw_index = %d \n", gps_map_info.show_index, gps_map_info.draw_index);
#endif
						// 直接发送绘制事件
						//LibEventDispatcherNotify(g_eventDispatch, MAP_DRAW, (void *)"event 0", 2);
						kill(getpid(), SIGUSR1);
						break;
					}
				}
			}
		}
	}
	else
	{
		interrupt_flag = 0;

		// 直接发送绘制事件
		//int ret = LibEventDispatcherNotify(g_eventDispatch, MAP_DRAW, (void *)"event 0", 2);
		kill(getpid(), SIGUSR1);
#if PRINT_TASK_MAP
		// printf("M_the_ret = %d \n",ret);
#endif
	}
}






/*********************************************************************
 *
 *  功能描述
 *    触发地图绘制API，且用来记录
 *
 *  输入参数
 *    scale：		比例尺
 *
 **********************************************************************/
void map_draw(int scale)
{
	

#if PRINT_TASK_MAP
	printf("map_draw:scale_count = %d \n",scale);
#endif
//	if (m_is_busy == false)
//	{
		
		int cur_map_index = gps_map_info.show_index;
		/****************** 要绘制的区域大小 ******************/
		m_rect.left = 0; 
		m_rect.top = 0;
		m_rect.width = gfx_wdth;
		m_rect.height = gfx_hght;
#if PRINT_TASK_MAP
		printf("QQQ_cur_map_index = %d ,FFF_the_map_mode = 0x%x ,m_draw_coord.lon = %f ,lat = %f \n", cur_map_index, get_map_mode(), m_draw_coord.longitude, m_draw_coord.latitude);
		printf("QQQ_the_map_mode = 0x%x ,m_coord_min.lon = %f ,lat = %f \n", get_map_mode(), m_coord_min.longitude, m_coord_min.latitude);
		printf("QQQ_the_map_mode = 0x%x ,m_coord_max.lon = %f ,lat = %f \n", get_map_mode(), m_coord_max.longitude, m_coord_max.latitude);
#endif
		/****************** 整理地图的绘制信息 ******************/
		compute_map_center(cur_map_index,out_of_range);
		m_coord_min = m_coord_max = m_draw_coord;
		//根据比例尺，计算地图绘制的经纬度范围等数据
		mlp_ratio_cal(m_draw_coord, scale);

#if PRINT_TASK_MAP
		printf("sqw_the_cur_map_index = %d ,m_draw_coord.lon = %f ,lat = %f \n", cur_map_index, m_draw_coord.longitude, m_draw_coord.latitude);
		printf("sqw_the_cur_map_index = %d ,m_coord_min.lon = %f ,lat = %f \n", cur_map_index, m_coord_min.longitude, m_coord_min.latitude);
		printf("sqw_the_cur_map_index = %d ,m_coord_max.lon = %f ,lat = %f \n", cur_map_index, m_coord_max.longitude, m_coord_max.latitude);
#endif

		/****************** 发送地图绘制消息 ******************/
		m_data_ready &= 0xFE;
		map_status = MAP_STATUS;
		crtl_check_map_draw();
//	}
}

/*********************************************************************
 *
 *  功能描述
 *    发送绘制轨迹消息
 *
 *  输入参数
 *    coord_callback：		回调函数
 *    color：					颜色
 *    width：					宽度
 *
 **********************************************************************/

void map_draw_track(mlp_get_coord_fun_t coord_callback, gfx_color_t color, uint16_t width)
{
#if PRINT_TASK_MAP
	printf("map_draw_track \n");
#endif
	//		if(m_is_busy == false) {
	g_fun = coord_callback;
	g_color = color;
	g_width = width;
	m_data_ready &= 0xFD;

	// 发送绘制轨迹事件
	//LibEventDispatcherNotify(g_eventDispatch, MAP_TRACK, (void *)"event 2", 2);
	kill(getpid(), rt_sig);

	
	//	}
}

/*********************************************************************
 *
 *  功能描述
 *    发送绘制轨迹消息
 *
 **********************************************************************/

void map_parse_gpx(void)
{
#if PRINT_TASK_MAP
	printf("map_parse_gpx \n");
#endif
//	if (m_is_busy == false)
	{
		m_data_ready &= 0xFB;
		//struct app_msg msg = {0};
		//msg.type = MSG_GPX_PARSE;
		//send_async_msg(TASK_NAME, &msg);
	}
}


/*********************************************************************
 *
 *  功能描述
 *    发送补充绘制地图的消息(用于初始化时)
 *
 **********************************************************************/

void map_draw_again()
{
#if PRINT_TASK_MAP
	printf("map_draw_again \n");
#endif
	if (m_is_busy == false)
	{
		//确定范围
		m_rect.left = 0; // rect;
		m_rect.top = 0;
		m_rect.width = gfx_wdth;
		m_rect.height = gfx_hght;


		// 直接发送不冲绘制事件
		//LibEventDispatcherNotify(g_eventDispatch, MAP_OTHER, (void *)"event 1", 2);
		kill(getpid(), SIGUSR2);
	}
}

/*********************************************************************
 *
 *  功能描述
 *    导航模式下，发送解析轨迹并重新绘制地图消息
 *
 **********************************************************************/

void map_draw_nav()
{
#if PRINT_TASK_MAP
	printf("map_draw_nav \n");
#endif
//	if (m_is_busy == false)
	{
		//发送轨迹解析消息
		map_parse_gpx();
	}
}


/*********************************************************************
 *
 *  功能描述
 *    发送绘制轨迹与虚线的消息
 *
 **********************************************************************/

void map_draw_trk_and_dash()
{
#if PRINT_TASK_MAP
	printf("map_draw_trk_and_dash \n");
#endif
	if (m_is_busy == false)
	{
		switch ((get_map_mode() & (~VIW_MODE)))
		{
		case NAV_MODE:	//导航模式
			map_draw_track(trk_get_coordinate_new, GFX_RGB(0, 0, 255), 5);
			break;
		case REC_MODE:	//记录模式
			map_draw_track(record_get_coordinate, GFX_RGB(0, 255, 0), 8);
			break;
		case RET_MODE:	//返航模式
			map_draw_track(record_get_coordinate, GFX_RGB(0, 0, 255), 8);
			break;
		case POS_MODE:
		default:
			map_status = FLUSH_STATUS;
			break;
		}
	}
}







/*********************************************************************
 *
 *  功能描述
 *    获取轨迹的起点与终点
 *
 *  输入参数
 *    start：		起点
 *    end：		终点
 *
 **********************************************************************/

void map_trk_get_pos(gfx_point_t *start, gfx_point_t *end)
{
	if (start != NULL && end != NULL)
	{
		if(!revert_pos)
		{
			*start = g_start;
			*end = g_end;
		}
		else
		{
			*start = g_end;
			*end = g_start;
		}
	}
}

void clear_start_end_pos()
{
	g_end.x = 0;
	g_end.y = 0;
	g_start.x = 0;
	g_start.y = 0;
}

uint8_t map_data_ready(void)
{
	return m_data_ready;
}

#if 0

/******************************************************************************************************************
 *
 *						地图授权任务相关
 *
 ******************************************************************************************************************/

#define CONFIG_TASK_MAP_LICENSE_STACK_SIZE (5120)

static struct k_thread task_map_license_data;
static K_KERNEL_STACK_DEFINE(task_map_license_stack, CONFIG_TASK_MAP_LICENSE_STACK_SIZE);

k_tid_t task_map_license_tid = NULL;

int license_check_flag = 0;	   // 是否已经成功授权过
int is_uart_ipen = 1;		   // 是否进入了串口授权界面
u8_t pc_init_buf[72] = {0};	   // 用于保存接收到的串口数据
u8_t rx_buf_len = 0;		   // 用于保存接收到的串口数据长度
u8_t lisence_codexx[64] = {0}; // 这个保存从PC授权工具返回的授权码，授权码64字节，要保存到flash里面，每次开机时调用mlp_map_init，将授权码传入进行校验
u8_t lisence_codexx1[64] = {0};

extern uart_test_dma_t uart_test_ctl;

// 初始化信号量
struct k_sem my_sem_license;
K_SEM_DEFINE(my_sem_license, 0, 1);

// 授权成功标志位
extern int license_succ_flag;

/*********************************************************************
 *
 *  功能描述：地图串口授权任务
 *
 **********************************************************************/

static void task_map_license_thread(void)
{
	//printf("task_map_license_thread \n");

	uint8_t lisence_power[64] = {0}; // 开机后存放读到的lisence校验码

	extern int nvram_config_get(const char *name, void *data, int max_len);
	int ret2 = nvram_config_get(license_name, lisence_power, 64);

	//printf("ggggggg_ret2 = %d \n", ret2);
	for (int i = 0; i < 64; i++)
	{
		//printf("xxx_lisence_power[%d] = 0x%02x \n", i, lisence_power[i]);
	}

	if (ret2 >= 0)
	{
		license_check_flag = 1; // 只要能读到lisence验证码，则之后每次进去都会判断跳转到验证成功界面
		extern void ppp_fun(void *tx_buffer, uint16_t length);
		if (Map_check_lisenceId_status(lisence_power, ppp_fun) == 1)
		{
			license_succ_flag = 1;
			//printf("----Lisence_check_SUCCESS!!!--\n");
		}
		else
		{
			//printf("----Lisence_check_FAIL!!!--\n");
		}
	}
	else
	{
		//printf("----read_syscfg_fail!!!--\n");
	}

	// 进行授权测试
	while (1)
	{
		// 如果进入了授权测试界面
		if (is_uart_ipen)
		{
			memset(pc_init_buf, 0, sizeof(pc_init_buf));
			for (int i = 0; i < rx_buf_len; i++)
			{
				//printf("SSSSthe_[%d] = 0x%02x \n", i, pc_init_buf[i]);
			}
			//printf("ssssssss \n");
			int valid_ms = 0;
			int wait_ms = 0;
			do
			{
				k_sleep(K_MSEC(10)); // 延时10ms
				// int len_tmp = ring_buffer_data_left(&pc_rb);
				int len_tmp = ring_buf_is_empty(&uart_test_ctl.rx_context.rx_rbuf); // 判断是否已经有数据了
				//printf("HHH_the_len_tmp = %d \n", len_tmp);
				if (len_tmp == 1)
				{
					valid_ms = 0;
					wait_ms++;
				}
				else
				{
					valid_ms++;
					if (valid_ms > 20)
					{
						// ring_buffer_read(&pc_rb,pc_init_buf,sizeof(pc_init_buf));
						//printf("HUHUHU \n");
						ring_buf_get(&uart_test_ctl.rx_context.rx_rbuf, pc_init_buf, sizeof(pc_init_buf)); // 把数据拿出来

						for (int i = 0; i < rx_buf_len; i++)
						{
							//printf("AAAAthe_[%d] = 0x%02x \n", i, pc_init_buf[i]);
						}

						if (pc_init_buf[0] == 0x5a && pc_init_buf[1] == 0xa5 && pc_init_buf[6] == 0x01)
						{
							// 判断CRC校验，确认是否一致，保证信息未丢失
							unsigned char crc_result = MAP_CRC(pc_init_buf);
							if (crc_result == pc_init_buf[8])
							{
								extern void ppp_fun(void *tx_buffer, uint16_t length);
								Map_reply_id_addr(pc_init_buf, ppp_fun);
							}
						}
						else if (pc_init_buf[0] == 0x5a && pc_init_buf[1] == 0xa5 && pc_init_buf[6] == 0x02)
						{
							// 判断CRC校验，确认是否一致，保证信息未丢失
							unsigned char crc_result = MAP_CRC(pc_init_buf);

							//printf(" the_crc_result = 0x%02x \n", crc_result);
							if (crc_result == pc_init_buf[71])
							{
								memcpy(lisence_codexx, &pc_init_buf[7], 64);
								extern void ppp_fun(void *tx_buffer, uint16_t length);
								unsigned char i = Map_check_lisenceId_status(lisence_codexx, ppp_fun);
								if (i)
								{
									// ui_send_event(KEY_CHANGE_PAGE, BIT(31) | PAGE_206);  //跳转到授权成功界面
									extern int nvram_config_set(const char *name, const void *data, int len);
									int ret_1 = nvram_config_set(license_name, lisence_codexx, 64); // 保存授权码
									//printf("YYY_the_ret_1 = %d \n", ret_1);
									license_succ_flag = 1;
									int yyy = ui_view_layout(LICENSE_MAP_VIEW);
									//printf("YYY = %d \n", yyy);
								}
							}
						}
						//新增1条协议用于验证
						else if (pc_init_buf[0] == 0x5a && pc_init_buf[1] == 0xa5 && pc_init_buf[6] == 0x03)
						{
							// 判断CRC校验，确认是否一致，保证信息未丢失
							unsigned char crc_result = MAP_CRC(pc_init_buf);

							//printf(" the_crc_result = 0x%02x \n", crc_result);
							if (crc_result == pc_init_buf[8])
							{

								//1.读取存放好的lisence校验码
							    uint8_t lisence_check[64] = {0}; 
								extern int nvram_config_get(const char *name, void *data, int max_len);
								int ret2 = nvram_config_get(license_name, lisence_check, 64);
								if (ret2 >= 0)
								{
									//printf("Get_nvram_map_lisence_code:SUCC! \n");
								}
								else
								{
									//printf("Get_nvram_map_lisence_code:FAIl! \n");
								}

								//2.进行授权校验,并返回授权状态
								extern void ppp_fun(void *tx_buffer, uint16_t length);
								unsigned char i = Map_check_lisenceId_status(lisence_check, ppp_fun);
								if(i)
								{
									//printf("Get_map_lisence_status:OK! \n");
								}
								else
								{
									//printf("Get_map_lisence_status:FAIL! \n");
								}

							}
						}
						//新增1条协议用于Ukey
						else if (pc_init_buf[0] == 0x5a && pc_init_buf[1] == 0xa5 && pc_init_buf[6] == 0x06)
						{
							// 判断CRC校验，确认是否一致，保证信息未丢失
							unsigned char crc_result = MAP_CRC(pc_init_buf);

							//printf(" the_crc_result = 0x%02x \n", crc_result);
							if (crc_result == pc_init_buf[63])
							{
								memcpy(lisence_codexx, &pc_init_buf[7], 32);
								extern void ppp_fun(void *tx_buffer, uint16_t length);
								
								unsigned char i = Map_check_lisenceId_status(lisence_codexx, ppp_fun);
								if (i)
								{
									// ui_send_event(KEY_CHANGE_PAGE, BIT(31) | PAGE_206);  //跳转到授权成功界面
									extern int nvram_config_set(const char *name, const void *data, int len);
									int ret_1 = nvram_config_set(license_name, lisence_codexx, 64); // 保存授权码
									//printf("YYY_the_ret_1 = %d \n", ret_1);
									license_succ_flag = 1;
									int yyy = ui_view_layout(LICENSE_MAP_VIEW);
									//printf("YYY = %d \n", yyy);
								}
								

							}
						}
						//新增1条协议用于清除Ukey
						else if (pc_init_buf[0] == 0x5a && pc_init_buf[1] == 0xa5 && pc_init_buf[6] == 0x0A)
						{

							u8_t lisence_code00[64] = {0};
							extern int nvram_config_set(const char *name, const void *data, int len);
							int ret_1 = nvram_config_set(license_name, lisence_code00, 64); // 保存授权码
							//printf("YYY_the_ret_22 = %d \n", ret_1);
							license_succ_flag = 0;
							int yyy = ui_view_layout(LICENSE_MAP_VIEW);
							//printf("YYY = %d \n", yyy);
						}
						valid_ms = 0;
						break;
					}
				}
			} while (wait_ms < 20);
		}
		// 获取信号量
		k_sem_take(&my_sem_license, K_MSEC(500));
	}
}

/*********************************************************************
 *
 *	功能描述：创建地图串口授权任务
 *
 **********************************************************************/
void map_license_task_create(void)
{
	if (task_map_license_tid == NULL)
	{
		task_map_license_tid = k_thread_create(&task_map_license_data, task_map_license_stack, K_KERNEL_STACK_SIZEOF(task_map_license_stack),
											   (k_thread_entry_t)task_map_license_thread, NULL, NULL, NULL, K_PRIO_COOP(CONFIG_BT_RX_PRIO), 0, K_NO_WAIT);
		k_thread_name_set(&task_map_license_data, TASK_LICENSE_NAME);
	}
}

/*********************************************************************
 *
 *	功能描述：注销地图串口授权任务
 *
 **********************************************************************/
void map_license_task_close(void)
{
	if (task_map_license_tid != NULL)
	{
		k_thread_abort(task_map_license_tid);
	}
	task_map_license_tid = NULL;
}

#endif

