﻿/**
 * @file trk_api.h
 * @brief 轨迹处理模块API接口
 *
 * 提供轨迹数据的解析、存储、查询和处理功能
 */

#ifndef TRK_API_H
#define TRK_API_H

#ifdef __cplusplus
extern "C" {
#endif

/*--------------------------------------------------------------------
                            INCLUDES
--------------------------------------------------------------------*/
#include <stdbool.h>
#include <stdint.h>
#include "mlpfile.h"
#include "utl_geo.h"
#include "trk_api.h"
#include "algo_trackjudge.h"

/*--------------------------------------------------------------------
                            STRUCT DEFINITIONS
--------------------------------------------------------------------*/


/*--------------------------------------------------------------------
                            FUNCTION DECLARATIONS
--------------------------------------------------------------------*/

/**
 * @brief 将GPX数据解析到临时文件
 * @return true:成功, false:失败
 */
bool gpx_to_tmp(void);

/**
 * @brief 解析轨迹数据
 */
void trk_parse(void);

/**
 * @brief 获取轨迹的边界
 * @param[out] min_lon 最小经度
 * @param[out] min_lat 最小纬度
 * @param[out] max_lon 最大经度
 * @param[out] max_lat 最大纬度
 * @return true:成功, false:失败
 */
bool trk_get_border(double* min_lon, double* min_lat, double* max_lon, double* max_lat);

/**
 * @brief 连续获取经纬度
 * @param[out] lon 经度
 * @param[out] lat 纬度
 * @return true:成功, false:失败
 */
bool trk_get_coordinate(double* lon, double* lat);

/**
 * @brief 写入轨迹数据到GPX文件
 * @param pname GPX文件名
 * @param ptrk 轨迹点数组
 * @param len 轨迹点数量
 * @return true:成功, false:失败
 */
bool trk_write(const char *pname, trk_pos_t *ptrk, uint32_t len);

/**
 * @brief 判断是否偏移轨迹
 * @param lonx 当前经度
 * @param laty 当前纬度
 * @param trackpoints 轨迹数组
 * @param track_points 轨迹点数
 * @return 偏移距离，0表示在轨迹上
 */
int navi_trackjudge(int32_t lonx, int32_t laty, int32_t trackpoints[], int track_points);

/**
 * @brief 获取轨迹点数组
 * @param trk_pointnum 轨迹点数量
 * @return 轨迹点数组指针，需要手动释放内存
 */
int32_t* trk_get_trk_array(int trk_pointnum);

/**
 * @brief 计算当前位置与轨迹的偏移量
 * @param track_points 轨迹数组
 * @param cur_coord 当前坐标
 * @return 偏移距离
 */
int map_nav_offest(int32_t* track_points, utl_geo_coord_t cur_coord);

/**
 * @brief 计算两点间的倾斜角度
 * @param coord_1 上一个坐标
 * @param coord_2 当前坐标
 * @return 倾斜角度(度)
 */
double slope_to_angle(utl_geo_coord_t coord_1, utl_geo_coord_t coord_2);

/**
 * @brief 获取地图导航角度
 * @param track_points 轨迹数组
 * @param cur_coord 当前坐标
 * @return 导航角度(度)
 */
double map_nav_angle(int32_t* track_points, utl_geo_coord_t cur_coord);

/**
 * @brief 调整当前坐标到轨迹上
 * @param track_points 轨迹数组
 * @param cur_coord 当前坐标指针
 */
void map_trk_offset(int32_t* track_points, utl_geo_coord_t* cur_coord);

/**
 * @brief 计算当前位置与轨迹的偏移距离
 * @param track_points 轨迹数组
 * @param cur_coord 当前坐标
 * @return 偏移距离(米)
 */
double map_trk_offset_yt(int32_t* track_points, utl_geo_coord_t cur_coord);

/**
 * @brief 设置GPX文件名
 * @param tmp_name 临时文件名
 */
void set_gpx_name(char* tmp_name);

/**
 * @brief 清空GPX文件名
 */
void clear_gpx_name();

/**
 * @brief 获取GPX文件名
 * @return GPX文件名指针
 */
char* get_gpx_name();

/**
 * @brief 记录轨迹点到临时文件
 * @param ptrk 当前轨迹点
 * @return 已记录的轨迹点数量
 */
int trk_record_temp(trk_pos_t ptrk);

/**
 * @brief 从记录的临时文件获取经纬度
 * @param lon 经度指针
 * @param lat 纬度指针
 * @return true:成功, false:失败
 */
bool record_get_coordinate(double* lon, double* lat);

/**
 * @brief 开始记录轨迹
 * @param cur_time 计时器秒数
 * @param ptrk 当前轨迹点
 * @return 已记录的轨迹点数量
 */
int start_trk_record(int cur_time, trk_pos_t ptrk);

/**
 * @brief 返航模式下获取经纬度
 * @param lon 经度指针
 * @param lat 纬度指针
 * @return true:成功, false:失败
 */
bool return_get_coordinate(double* lon, double* lat);

/**
 * @brief 关闭轨迹文件
 */
void trk_close(void);

/**
 * @brief 清理所有轨迹相关状态和内存
 * @note 用于地图退出时完全重置轨迹状态，防止数据残留
 */
void trk_cleanup(void);

/**
 * @brief 设置导航GPX文件名
 * @param tmp_name GPX文件名
 */
void set_nav_gpx_name(char* tmp_name);

/**
 * @brief 清空导航GPX文件名
 */
void clear_nav_gpx_name();

/**
 * @brief 导航模式下获取经纬度
 * @param lon 经度指针
 * @param lat 纬度指针
 * @return true:成功, false:失败
 */
bool trk_get_coordinate_new(double* lon, double* lat);

/**
 * @brief 解析轨迹文件
 * @param file_path 文件路径
 * @return 1:成功, 0:失败
 */
int trk_parse_yt(char* file_path);

/**
 * @brief 获取轨迹的边界(新接口)
 * @param[out] min_lon 最小经度
 * @param[out] min_lat 最小纬度
 * @param[out] max_lon 最大经度
 * @param[out] max_lat 最大纬度
 * @return true:成功, false:失败
 */
bool trk_get_border_new(double* min_lon, double* min_lat, double* max_lon, double* max_lat);

/*--------------------------------------------------------------------
                            GLOBAL VARIABLES
--------------------------------------------------------------------*/

#ifdef __cplusplus
}
#endif

#endif /* TRK_API_H */